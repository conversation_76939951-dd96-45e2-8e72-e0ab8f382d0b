import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsEnum, ValidateNested, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO for content block types in chat messages
 */
export class ContentBlockDto {
  @ApiProperty({
    description: 'Type of content block',
    enum: ['text', 'image', 'file', 'modify_last_text', 'tool_call_decision', 'cancel'],
    example: 'text'
  })
  @IsEnum(['text', 'image', 'file', 'modify_last_text', 'tool_call_decision', 'cancel'])
  @IsNotEmpty()
  type: 'text' | 'image' | 'file' | 'modify_last_text' | 'tool_call_decision' | 'cancel';

  @ApiPropertyOptional({
    description: 'Decision for tool call (if type is tool_call_decision)',
    enum: ['yes', 'no', 'always'],
    example: 'yes'
  })
  @IsOptional()
  @IsEnum(['yes', 'no', 'always'])
  decision?: 'yes' | 'no' | 'always';

  @ApiPropertyOptional({
    description: 'Content (if type is text)',
    example: 'Hello, how can I help you?'
  })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiPropertyOptional({
    description: 'File ID (if type is file)',
    example: 'file_123456'
  })
  @IsOptional()
  @IsString()
  fileId?: string;
}

/**
 * DTO for chat message requests
 */
export class MessageRequestDto {
  @ApiProperty({
    description: 'Content blocks for the message (single block or array of blocks)',
    type: [ContentBlockDto],
    example: [{ type: 'text', content: 'Hello, how can I help you?' }]
  })
  @ValidateNested({ each: true })
  @Type(() => ContentBlockDto)
  contentBlocks: ContentBlockDto | ContentBlockDto[];

  @ApiPropertyOptional({
    description: 'Optional thread ID to continue existing conversation',
    example: 'thread_123456'
  })
  @IsNotEmpty()
  @IsString()
  threadId?: string;

  @ApiPropertyOptional({
    description: 'Whether to always approve tool calls without user confirmation',
    example: false
  })
  @IsOptional()
  @IsBoolean()
  alwaysApproveToolCall?: boolean;
}
