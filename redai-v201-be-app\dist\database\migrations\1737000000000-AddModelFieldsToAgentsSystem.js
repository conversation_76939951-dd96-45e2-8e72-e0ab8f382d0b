"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddModelFieldsToAgentsSystem1737000000000 = void 0;
class AddModelFieldsToAgentsSystem1737000000000 {
    async up(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE "agents_system" 
      ADD COLUMN IF NOT EXISTS "role_id" UUID NOT NULL
    `);
        await queryRunner.query(`
      ALTER TABLE "agents_system" 
      ADD COLUMN IF NOT EXISTS "model_base_id" UUID NULL
    `);
        await queryRunner.query(`
      ALTER TABLE "agents_system" 
      ADD COLUMN IF NOT EXISTS "model_finetuning_id" UUID NULL
    `);
        await queryRunner.query(`
      ALTER TABLE "agents_system" 
      ADD COLUMN IF NOT EXISTS "prompt_task" TEXT NULL
    `);
        await queryRunner.query(`
      COMMENT ON COLUMN "agents_system"."role_id" 
      IS 'UUID của vai trò liên kết - Mỗi agent system chỉ có 1 role duy nhất'
    `);
        await queryRunner.query(`
      COMMENT ON COLUMN "agents_system"."model_base_id" 
      IS 'UUID tham chiếu đến bảng base_models - ID của model base (nếu có)'
    `);
        await queryRunner.query(`
      COMMENT ON COLUMN "agents_system"."model_finetuning_id" 
      IS 'UUID tham chiếu đến bảng fine_tuning_models - ID của fine-tuning model (nếu có)'
    `);
        await queryRunner.query(`
      COMMENT ON COLUMN "agents_system"."prompt_task" 
      IS 'Prompt cho task'
    `);
        await queryRunner.query(`
      ALTER TABLE "agents_system"
      ADD CONSTRAINT "agents_system_base_models_id_fk"
      FOREIGN KEY ("model_base_id") REFERENCES "base_models"("id")
      ON DELETE SET NULL
    `);
        await queryRunner.query(`
      ALTER TABLE "agents_system"
      ADD CONSTRAINT "agents_system_fine_tuning_models_id_fk"
      FOREIGN KEY ("model_finetuning_id") REFERENCES "fine_tuning_models"("id")
      ON DELETE SET NULL
    `);
        await queryRunner.query(`
      ALTER TABLE "agents_system"
      ADD CONSTRAINT "agents_system_agent_roles_id_fk"
      FOREIGN KEY ("role_id") REFERENCES "agent_roles"("id")
      ON DELETE CASCADE
    `);
        await queryRunner.query(`
      ALTER TABLE "agents_system"
      DROP CONSTRAINT IF EXISTS "agents_system_pkey"
    `);
        await queryRunner.query(`
      ALTER TABLE "agents_system"
      ADD CONSTRAINT "agents_system_pkey"
      PRIMARY KEY ("id", "role_id")
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "idx_agents_system_model_base_id"
      ON "agents_system" ("model_base_id")
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "idx_agents_system_model_finetuning_id"
      ON "agents_system" ("model_finetuning_id")
    `);
        await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "idx_agents_system_role_id"
      ON "agents_system" ("role_id")
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      DROP INDEX IF EXISTS "idx_agents_system_role_id"
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS "idx_agents_system_model_finetuning_id"
    `);
        await queryRunner.query(`
      DROP INDEX IF EXISTS "idx_agents_system_model_base_id"
    `);
        await queryRunner.query(`
      ALTER TABLE "agents_system"
      DROP CONSTRAINT IF EXISTS "agents_system_pkey"
    `);
        await queryRunner.query(`
      ALTER TABLE "agents_system"
      ADD CONSTRAINT "agents_system_pkey"
      PRIMARY KEY ("id")
    `);
        await queryRunner.query(`
      ALTER TABLE "agents_system"
      DROP CONSTRAINT IF EXISTS "agents_system_agent_roles_id_fk"
    `);
        await queryRunner.query(`
      ALTER TABLE "agents_system"
      DROP CONSTRAINT IF EXISTS "agents_system_fine_tuning_models_id_fk"
    `);
        await queryRunner.query(`
      ALTER TABLE "agents_system"
      DROP CONSTRAINT IF EXISTS "agents_system_base_models_id_fk"
    `);
        await queryRunner.query(`
      ALTER TABLE "agents_system" 
      DROP COLUMN IF EXISTS "prompt_task"
    `);
        await queryRunner.query(`
      ALTER TABLE "agents_system" 
      DROP COLUMN IF EXISTS "model_finetuning_id"
    `);
        await queryRunner.query(`
      ALTER TABLE "agents_system" 
      DROP COLUMN IF EXISTS "model_base_id"
    `);
        await queryRunner.query(`
      ALTER TABLE "agents_system" 
      DROP COLUMN IF EXISTS "role_id"
    `);
    }
}
exports.AddModelFieldsToAgentsSystem1737000000000 = AddModelFieldsToAgentsSystem1737000000000;
//# sourceMappingURL=1737000000000-AddModelFieldsToAgentsSystem.js.map