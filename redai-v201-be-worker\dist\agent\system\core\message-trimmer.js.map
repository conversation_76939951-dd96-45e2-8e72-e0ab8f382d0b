{"version": 3, "sources": ["../../../../src/agent/system/core/message-trimmer.ts"], "sourcesContent": ["import {\r\n  AIMessage,\r\n  BaseMessage,\r\n  HumanMessage,\r\n  RemoveMessage,\r\n  SystemMessage,\r\n  ToolMessage,\r\n  trimMessages,\r\n} from '@langchain/core/messages';\r\nimport { Logger } from '@nestjs/common';\r\nimport {\r\n  dropLeadingOrphanAsRemovals,\r\n  dropTrailingOrphanBlock,\r\n} from './helpers';\r\n\r\nconst logger = new Logger('MessageTrimmer');\r\n\r\nasync function topKMessageTrimmer(\r\n  messages: BaseMessage[],\r\n  threshold: number,\r\n  model?: any,\r\n) {\r\n  if (messages.length <= threshold) {\r\n    logger.log(`📭 No trimming needed: ${messages.length} <= ${threshold}`);\r\n    return { messages: [] };\r\n  }\r\n\r\n  const toDelete = messages.slice(0, Math.max(0, messages.length - threshold));\r\n  const toKeep = messages.slice(-threshold);\r\n  const removals = toDelete.map(\r\n    (msg: BaseMessage) => new RemoveMessage({ id: msg.id as string }),\r\n  );\r\n  const removedOrphan = dropLeadingOrphanAsRemovals(toKeep);\r\n  logger.log(\r\n    `🗑️ Deleting ${removals.length + removedOrphan.length} messages, keeping last ${toKeep.length - removedOrphan.length}`,\r\n  );\r\n  return { messages: [...removals, ...removedOrphan] };\r\n}\r\n\r\nexport async function tokenMessageTrimmer(\r\n  messages: BaseMessage[],\r\n  threshold: number,\r\n  model: any,\r\n) {\r\n  if (!model) {\r\n    logger.warn('No model provided for token trimming, falling back to no trimming');\r\n    return { messages: [] };\r\n  }\r\n\r\n  // 1️⃣ Token‐budget trim (no copy needed)\r\n  const trimmed = await trimMessages(messages, {\r\n    maxTokens: threshold,\r\n    strategy: 'last',\r\n    tokenCounter: model,\r\n    startOn: 'human',\r\n  });\r\n\r\n  // 2️⃣ Orphan‐drop at the tail\r\n  const cleaned = dropTrailingOrphanBlock(trimmed);\r\n  if (cleaned.length !== trimmed.length) {\r\n    logger.log(\r\n      `🗑️ Dropped ${trimmed.length - cleaned.length} trailing orphan msg(s)`,\r\n    );\r\n  }\r\n\r\n  // 3️⃣ ONE PASS over the original to emit RemoveMessage for any msg\r\n  //    that didn’t survive into `cleaned`.\r\n  const keptIds = new Set(cleaned.map((m) => m.id));\r\n  const removals: RemoveMessage[] = [];\r\n  for (const msg of messages) {\r\n    // Skip if it’s in the final kept set\r\n    if (msg.id && !keptIds.has(msg.id)) {\r\n      removals.push(new RemoveMessage({ id: msg.id }));\r\n    }\r\n  }\r\n\r\n  logger.log(`📨 Emitting ${removals.length} RemoveMessage cmds`);\r\n\r\n  // 4️⃣ Return just the RemoveMessage directives\r\n  return { messages: removals };\r\n}\r\n\r\nasync function aiMessageTrimmer(\r\n  messages: BaseMessage[],\r\n  threshold: number,\r\n  model: any,\r\n) {\r\n  if (messages.length <= threshold) {\r\n    logger.log(`📭 No trimming needed: ${messages.length} <= ${threshold}`);\r\n    return { messages: [] }; // Return empty messages array instead of empty object\r\n  }\r\n  // Summarize the *earliest* messages beyond the threshold\r\n  const toSummarize = messages.slice(0, messages.length - threshold);\r\n  const summarizationInput = `${toSummarize\r\n    .map((msg) => {\r\n      if (msg instanceof AIMessage) {\r\n        return `Assistant: ${JSON.stringify(msg?.content)}`;\r\n      } else if (msg instanceof HumanMessage) {\r\n        return `User: ${JSON.stringify(msg?.content)}`;\r\n      } else if (msg instanceof ToolMessage) {\r\n        return `Tool: ${JSON.stringify(msg?.content)}`;\r\n      } else {\r\n        return '';\r\n      }\r\n    })\r\n    .join('\\n')}`;\r\n  const summaryPrompt = [\r\n    new SystemMessage({\r\n      content: `You are an AI assistant that summarizes conversations. You are picky, choosing and synthesizing the most important points from the conversation.`,\r\n    }),\r\n    new HumanMessage({\r\n      content: `<task>Summarize the following conversation ${summarizationInput}</task>\r\n<requirement>Only give the output, do not add any filler text.</requirement>`,\r\n    }),\r\n  ];\r\n  const summaryResult = await model.invoke(summaryPrompt);\r\n  const summaryMsg = new AIMessage({\r\n    content: `[Summary of earlier chat]: ${summaryResult.content}`,\r\n  });\r\n  logger.debug(`summary: ${summaryMsg.content}`);\r\n  // Build deletions for all the old messages we just summarized\r\n  const deletions = toSummarize.map(\r\n    (msg: BaseMessage) => new RemoveMessage({ id: msg.id as string }),\r\n  );\r\n  // And then append the summary + the last `threshold` raw messages\r\n  const recent = messages.slice(-threshold);\r\n  console.log(\r\n    `🗑️ Deleting ${deletions.length} messages to stay under ${threshold} messages`,\r\n  );\r\n  return {\r\n    messages: [...deletions, summaryMsg, ...recent],\r\n  };\r\n}\r\n\r\nconst trimmingStrategies = {\r\n  top_k: topKMessageTrimmer,\r\n  ai: aiMessageTrimmer,\r\n  token: tokenMessageTrimmer,\r\n};\r\n\r\nexport async function trimMessagesWithStrategy(\r\n  messages: BaseMessage[],\r\n  type: string,\r\n  threshold: number,\r\n  model?: any,\r\n) {\r\n  if (!trimmingStrategies[type]) {\r\n    throw new Error(`Unknown trimming strategy: ${type}`);\r\n  }\r\n\r\n  const result = await trimmingStrategies[type](messages, threshold, model);\r\n\r\n  // Ensure result has a valid messages property\r\n  if (!result || typeof result !== 'object') {\r\n    logger.warn(`Trimming strategy '${type}' returned invalid result, using empty messages array`);\r\n    return { messages: [] };\r\n  }\r\n\r\n  if (!Array.isArray(result.messages)) {\r\n    logger.warn(`Trimming strategy '${type}' returned non-array messages, using empty messages array`);\r\n    return { messages: [] };\r\n  }\r\n\r\n  return result;\r\n}\r\n"], "names": ["tokenMessageTrimmer", "trimMessagesWithStrategy", "logger", "<PERSON><PERSON>", "topKMessageTrimmer", "messages", "threshold", "model", "length", "log", "toDelete", "slice", "Math", "max", "<PERSON><PERSON><PERSON>", "removals", "map", "msg", "RemoveMessage", "id", "<PERSON><PERSON><PERSON><PERSON>", "dropLeadingOrphanAsRemovals", "warn", "trimmed", "trimMessages", "maxTokens", "strategy", "tokenCounter", "startOn", "cleaned", "dropTrailingOrphanBlock", "keptIds", "Set", "m", "has", "push", "aiMessageTrimmer", "toSummarize", "summarizationInput", "AIMessage", "JSON", "stringify", "content", "HumanMessage", "ToolMessage", "join", "summaryPrompt", "SystemMessage", "summaryResult", "invoke", "summaryMsg", "debug", "deletions", "recent", "console", "trimmingStrategies", "top_k", "ai", "token", "type", "Error", "result", "Array", "isArray"], "mappings": ";;;;;;;;;;;QAuCsBA;eAAAA;;QAqGAC;eAAAA;;;0BApIf;wBACgB;yBAIhB;AAEP,MAAMC,SAAS,IAAIC,cAAM,CAAC;AAE1B,eAAeC,mBACbC,QAAuB,EACvBC,SAAiB,EACjBC,KAAW;IAEX,IAAIF,SAASG,MAAM,IAAIF,WAAW;QAChCJ,OAAOO,GAAG,CAAC,CAAC,uBAAuB,EAAEJ,SAASG,MAAM,CAAC,IAAI,EAAEF,WAAW;QACtE,OAAO;YAAED,UAAU,EAAE;QAAC;IACxB;IAEA,MAAMK,WAAWL,SAASM,KAAK,CAAC,GAAGC,KAAKC,GAAG,CAAC,GAAGR,SAASG,MAAM,GAAGF;IACjE,MAAMQ,SAAST,SAASM,KAAK,CAAC,CAACL;IAC/B,MAAMS,WAAWL,SAASM,GAAG,CAC3B,CAACC,MAAqB,IAAIC,uBAAa,CAAC;YAAEC,IAAIF,IAAIE,EAAE;QAAW;IAEjE,MAAMC,gBAAgBC,IAAAA,oCAA2B,EAACP;IAClDZ,OAAOO,GAAG,CACR,CAAC,aAAa,EAAEM,SAASP,MAAM,GAAGY,cAAcZ,MAAM,CAAC,wBAAwB,EAAEM,OAAON,MAAM,GAAGY,cAAcZ,MAAM,EAAE;IAEzH,OAAO;QAAEH,UAAU;eAAIU;eAAaK;SAAc;IAAC;AACrD;AAEO,eAAepB,oBACpBK,QAAuB,EACvBC,SAAiB,EACjBC,KAAU;IAEV,IAAI,CAACA,OAAO;QACVL,OAAOoB,IAAI,CAAC;QACZ,OAAO;YAAEjB,UAAU,EAAE;QAAC;IACxB;IAEA,yCAAyC;IACzC,MAAMkB,UAAU,MAAMC,IAAAA,sBAAY,EAACnB,UAAU;QAC3CoB,WAAWnB;QACXoB,UAAU;QACVC,cAAcpB;QACdqB,SAAS;IACX;IAEA,8BAA8B;IAC9B,MAAMC,UAAUC,IAAAA,gCAAuB,EAACP;IACxC,IAAIM,QAAQrB,MAAM,KAAKe,QAAQf,MAAM,EAAE;QACrCN,OAAOO,GAAG,CACR,CAAC,YAAY,EAAEc,QAAQf,MAAM,GAAGqB,QAAQrB,MAAM,CAAC,uBAAuB,CAAC;IAE3E;IAEA,mEAAmE;IACnE,yCAAyC;IACzC,MAAMuB,UAAU,IAAIC,IAAIH,QAAQb,GAAG,CAAC,CAACiB,IAAMA,EAAEd,EAAE;IAC/C,MAAMJ,WAA4B,EAAE;IACpC,KAAK,MAAME,OAAOZ,SAAU;QAC1B,qCAAqC;QACrC,IAAIY,IAAIE,EAAE,IAAI,CAACY,QAAQG,GAAG,CAACjB,IAAIE,EAAE,GAAG;YAClCJ,SAASoB,IAAI,CAAC,IAAIjB,uBAAa,CAAC;gBAAEC,IAAIF,IAAIE,EAAE;YAAC;QAC/C;IACF;IAEAjB,OAAOO,GAAG,CAAC,CAAC,YAAY,EAAEM,SAASP,MAAM,CAAC,mBAAmB,CAAC;IAE9D,+CAA+C;IAC/C,OAAO;QAAEH,UAAUU;IAAS;AAC9B;AAEA,eAAeqB,iBACb/B,QAAuB,EACvBC,SAAiB,EACjBC,KAAU;IAEV,IAAIF,SAASG,MAAM,IAAIF,WAAW;QAChCJ,OAAOO,GAAG,CAAC,CAAC,uBAAuB,EAAEJ,SAASG,MAAM,CAAC,IAAI,EAAEF,WAAW;QACtE,OAAO;YAAED,UAAU,EAAE;QAAC,GAAG,sDAAsD;IACjF;IACA,yDAAyD;IACzD,MAAMgC,cAAchC,SAASM,KAAK,CAAC,GAAGN,SAASG,MAAM,GAAGF;IACxD,MAAMgC,qBAAqB,GAAGD,YAC3BrB,GAAG,CAAC,CAACC;QACJ,IAAIA,eAAesB,mBAAS,EAAE;YAC5B,OAAO,CAAC,WAAW,EAAEC,KAAKC,SAAS,CAACxB,KAAKyB,UAAU;QACrD,OAAO,IAAIzB,eAAe0B,sBAAY,EAAE;YACtC,OAAO,CAAC,MAAM,EAAEH,KAAKC,SAAS,CAACxB,KAAKyB,UAAU;QAChD,OAAO,IAAIzB,eAAe2B,qBAAW,EAAE;YACrC,OAAO,CAAC,MAAM,EAAEJ,KAAKC,SAAS,CAACxB,KAAKyB,UAAU;QAChD,OAAO;YACL,OAAO;QACT;IACF,GACCG,IAAI,CAAC,OAAO;IACf,MAAMC,gBAAgB;QACpB,IAAIC,uBAAa,CAAC;YAChBL,SAAS,CAAC,gJAAgJ,CAAC;QAC7J;QACA,IAAIC,sBAAY,CAAC;YACfD,SAAS,CAAC,2CAA2C,EAAEJ,mBAAmB;4EACJ,CAAC;QACzE;KACD;IACD,MAAMU,gBAAgB,MAAMzC,MAAM0C,MAAM,CAACH;IACzC,MAAMI,aAAa,IAAIX,mBAAS,CAAC;QAC/BG,SAAS,CAAC,2BAA2B,EAAEM,cAAcN,OAAO,EAAE;IAChE;IACAxC,OAAOiD,KAAK,CAAC,CAAC,SAAS,EAAED,WAAWR,OAAO,EAAE;IAC7C,8DAA8D;IAC9D,MAAMU,YAAYf,YAAYrB,GAAG,CAC/B,CAACC,MAAqB,IAAIC,uBAAa,CAAC;YAAEC,IAAIF,IAAIE,EAAE;QAAW;IAEjE,kEAAkE;IAClE,MAAMkC,SAAShD,SAASM,KAAK,CAAC,CAACL;IAC/BgD,QAAQ7C,GAAG,CACT,CAAC,aAAa,EAAE2C,UAAU5C,MAAM,CAAC,wBAAwB,EAAEF,UAAU,SAAS,CAAC;IAEjF,OAAO;QACLD,UAAU;eAAI+C;YAAWF;eAAeG;SAAO;IACjD;AACF;AAEA,MAAME,qBAAqB;IACzBC,OAAOpD;IACPqD,IAAIrB;IACJsB,OAAO1D;AACT;AAEO,eAAeC,yBACpBI,QAAuB,EACvBsD,IAAY,EACZrD,SAAiB,EACjBC,KAAW;IAEX,IAAI,CAACgD,kBAAkB,CAACI,KAAK,EAAE;QAC7B,MAAM,IAAIC,MAAM,CAAC,2BAA2B,EAAED,MAAM;IACtD;IAEA,MAAME,SAAS,MAAMN,kBAAkB,CAACI,KAAK,CAACtD,UAAUC,WAAWC;IAEnE,8CAA8C;IAC9C,IAAI,CAACsD,UAAU,OAAOA,WAAW,UAAU;QACzC3D,OAAOoB,IAAI,CAAC,CAAC,mBAAmB,EAAEqC,KAAK,qDAAqD,CAAC;QAC7F,OAAO;YAAEtD,UAAU,EAAE;QAAC;IACxB;IAEA,IAAI,CAACyD,MAAMC,OAAO,CAACF,OAAOxD,QAAQ,GAAG;QACnCH,OAAOoB,IAAI,CAAC,CAAC,mBAAmB,EAAEqC,KAAK,yDAAyD,CAAC;QACjG,OAAO;YAAEtD,UAAU,EAAE;QAAC;IACxB;IAEA,OAAOwD;AACT"}