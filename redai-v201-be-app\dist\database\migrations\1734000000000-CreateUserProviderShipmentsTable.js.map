{"version": 3, "file": "1734000000000-CreateUserProviderShipmentsTable.js", "sourceRoot": "", "sources": ["../../../src/database/migrations/1734000000000-CreateUserProviderShipmentsTable.ts"], "names": [], "mappings": ";;;AAAA,qCAAiE;AAEjE,MAAa,6CAA6C;IACxD,IAAI,GAAG,+CAA+C,CAAC;IAEhD,KAAK,CAAC,EAAE,CAAC,WAAwB;QAEtC,MAAM,WAAW,CAAC,KAAK,CAAC;;KAEvB,CAAC,CAAC;QAGH,MAAM,WAAW,CAAC,WAAW,CAC3B,IAAI,eAAK,CAAC;YACR,IAAI,EAAE,yBAAyB;YAC/B,OAAO,EAAE;gBACP;oBACE,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,MAAM;oBACZ,SAAS,EAAE,IAAI;oBACf,kBAAkB,EAAE,MAAM;oBAC1B,OAAO,EAAE,oBAAoB;iBAC9B;gBACD;oBACE,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,KAAK;oBACX,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,KAAK;oBACX,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC;oBACtC,QAAQ,EAAE,wBAAwB;oBAClC,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,yDAAyD;iBACnE;aACF;YACD,WAAW,EAAE;gBACX;oBACE,WAAW,EAAE,CAAC,SAAS,CAAC;oBACxB,mBAAmB,EAAE,OAAO;oBAC5B,qBAAqB,EAAE,CAAC,IAAI,CAAC;oBAC7B,QAAQ,EAAE,SAAS;iBACpB;aACF;YACD,OAAO,EAAE;gBACP;oBACE,IAAI,EAAE,qCAAqC;oBAC3C,WAAW,EAAE,CAAC,SAAS,CAAC;iBACzB;gBACD;oBACE,IAAI,EAAE,kCAAkC;oBACxC,WAAW,EAAE,CAAC,MAAM,CAAC;iBACtB;gBACD;oBACE,IAAI,EAAE,uCAAuC;oBAC7C,WAAW,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;iBACjC;gBACD;oBACE,IAAI,EAAE,wCAAwC;oBAC9C,WAAW,EAAE,CAAC,YAAY,CAAC;iBAC5B;aACF;SACF,CAAC,EACF,IAAI,CACL,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QAExC,MAAM,WAAW,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;QAGvD,MAAM,WAAW,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;IAChE,CAAC;CACF;AAxFD,sGAwFC"}