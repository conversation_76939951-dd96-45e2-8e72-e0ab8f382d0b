"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AgentConfigService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentConfigService = void 0;
const common_1 = require("@nestjs/common");
const database_1 = require("../database");
let AgentConfigService = AgentConfigService_1 = class AgentConfigService {
    agentConfigQueries;
    logger = new common_1.Logger(AgentConfigService_1.name);
    constructor(agentConfigQueries) {
        this.agentConfigQueries = agentConfigQueries;
    }
    async buildAgentConfigMap() {
        try {
            this.logger.log('Building agent configuration map...');
            const configMap = await this.agentConfigQueries.getAllSystemAgentConfigs();
            this.logger.log(`Built configuration for ${Object.keys(configMap).length} agents`);
            for (const [agentId, config] of Object.entries(configMap)) {
                this.logger.debug(`Agent ${agentId}: ${config.name} (${config.model.provider}/${config.model.name})`);
            }
            return configMap;
        }
        catch (error) {
            this.logger.error('Failed to build agent configuration map', error);
            throw new Error(`Agent configuration building failed: ${error.message}`);
        }
    }
    async getAgentConfig(agentId) {
        try {
            this.logger.log(`Getting agent configuration for ID: ${agentId}`);
            const config = await this.agentConfigQueries.getSystemAgentConfigById(agentId);
            if (config) {
                this.logger.debug(`Found config for agent ${agentId}: ${config.name}`);
            }
            else {
                this.logger.warn(`No configuration found for agent ID: ${agentId}`);
            }
            return config;
        }
        catch (error) {
            this.logger.error(`Failed to get agent configuration for ID: ${agentId}`, error);
            throw new Error(`Agent configuration retrieval failed: ${error.message}`);
        }
    }
    validateAgentConfig(config) {
        try {
            if (!config.id || !config.name || !config.model) {
                this.logger.warn(`Invalid agent config: missing required fields for ${config.id}`);
                return false;
            }
            if (!config.model.name || !config.model.provider) {
                this.logger.warn(`Invalid model config for agent ${config.id}: missing model name or provider`);
                return false;
            }
            if (!config.model.apiKeys || config.model.apiKeys.length === 0) {
                this.logger.warn(`No API keys found for agent ${config.id}`);
                return false;
            }
            const validApiKeys = config.model.apiKeys.filter(key => key && key.trim().length > 0);
            if (validApiKeys.length === 0) {
                this.logger.warn(`No valid API keys found for agent ${config.id}`);
                return false;
            }
            this.logger.debug(`Agent config validation passed for ${config.id}`);
            return true;
        }
        catch (error) {
            this.logger.error(`Error validating agent config for ${config.id}`, error);
            return false;
        }
    }
    async getSupervisorAgent() {
        try {
            this.logger.log('Getting supervisor agent configuration...');
            const configMap = await this.buildAgentConfigMap();
            for (const config of Object.values(configMap)) {
                if (config.name.toLowerCase().includes('supervisor') ||
                    config.name.toLowerCase().includes('manager')) {
                    this.logger.log(`Found supervisor agent: ${config.name} (${config.id})`);
                    return config;
                }
            }
            const firstAgent = Object.values(configMap)[0];
            if (firstAgent) {
                this.logger.log(`Using first agent as supervisor: ${firstAgent.name} (${firstAgent.id})`);
                return firstAgent;
            }
            this.logger.warn('No supervisor agent found');
            return null;
        }
        catch (error) {
            this.logger.error('Failed to get supervisor agent', error);
            throw new Error(`Supervisor agent retrieval failed: ${error.message}`);
        }
    }
    async getWorkerAgents() {
        try {
            this.logger.log('Getting worker agent configurations...');
            const configMap = await this.buildAgentConfigMap();
            const supervisor = await this.getSupervisorAgent();
            const workers = Object.values(configMap).filter(config => config.id !== supervisor?.id);
            this.logger.log(`Found ${workers.length} worker agents`);
            return workers;
        }
        catch (error) {
            this.logger.error('Failed to get worker agents', error);
            throw new Error(`Worker agents retrieval failed: ${error.message}`);
        }
    }
    async getConfigSummary() {
        try {
            const configMap = await this.buildAgentConfigMap();
            const summary = {
                totalAgents: Object.keys(configMap).length,
                agents: Object.values(configMap).map(config => ({
                    ...config
                }))
            };
            return summary;
        }
        catch (error) {
            this.logger.error('Failed to get configuration summary', error);
            throw new Error(`Configuration summary failed: ${error.message}`);
        }
    }
};
exports.AgentConfigService = AgentConfigService;
exports.AgentConfigService = AgentConfigService = AgentConfigService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_1.AgentConfigQueries])
], AgentConfigService);
//# sourceMappingURL=agent-config.service.js.map