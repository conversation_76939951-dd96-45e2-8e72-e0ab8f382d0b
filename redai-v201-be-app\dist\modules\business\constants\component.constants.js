"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_COMPONENTS = void 0;
exports.DEFAULT_COMPONENTS = [
    {
        component: 'Text Input',
        config: {
            id: 'text-input',
            label: 'Văn bản',
            type: 'text',
            required: false,
            validation: { minLength: 0, maxLength: 255 },
            placeholder: 'Nhập văn bản',
            variant: 'outlined',
            size: 'small',
        },
    },
    {
        component: 'Date Picker',
        config: {
            id: 'date-picker',
            label: 'Ngày tháng',
            type: 'date',
            required: false,
            validation: { minDate: '1900-01-01', maxDate: '2100-12-31' },
            variant: 'outlined',
            size: 'small',
        },
    },
    {
        component: 'Select Dropdown',
        config: {
            id: 'select-dropdown',
            label: 'Lựa chọn',
            type: 'select',
            required: false,
            options: ['Tùy chọn 1', 'Tùy chọn 2', 'Tùy chọn 3'],
            variant: 'outlined',
            size: 'small',
        },
    },
    {
        component: 'Checkbox',
        config: {
            id: 'checkbox',
            label: 'Hộp kiểm',
            type: 'checkbox',
            required: false,
            size: 'small',
        },
    },
    {
        component: 'Radio Button',
        config: {
            id: 'radio-button',
            label: 'Nút radio',
            type: 'radio',
            required: false,
            options: ['Tùy chọn 1', 'Tùy chọn 2', 'Tùy chọn 3'],
            size: 'small',
        },
    },
    {
        component: 'Textarea',
        config: {
            id: 'textarea',
            label: 'Văn bản dài',
            type: 'textarea',
            required: false,
            validation: { minLength: 0, maxLength: 1000 },
            placeholder: 'Nhập văn bản dài',
            variant: 'outlined',
            size: 'small',
            rows: 4,
        },
    },
];
//# sourceMappingURL=component.constants.js.map