"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RuleContractStateService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RuleContractStateService = void 0;
const common_1 = require("@nestjs/common");
const xstate_1 = require("xstate");
const rule_contract_types_1 = require("./rule-contract.types");
const rule_contract_entity_1 = require("../entities/rule-contract.entity");
const rule_contract_machine_1 = require("./rule-contract.machine");
const repositories_1 = require("../repositories");
const exceptions_1 = require("../../../common/exceptions");
const errors_1 = require("../errors");
const rule_contract_actions_service_1 = require("./rule-contract-actions.service");
let RuleContractStateService = RuleContractStateService_1 = class RuleContractStateService {
    ruleContractRepository;
    ruleContractActionsService;
    logger = new common_1.Logger(RuleContractStateService_1.name);
    machines = new Map();
    constructor(ruleContractRepository, ruleContractActionsService) {
        this.ruleContractRepository = ruleContractRepository;
        this.ruleContractActionsService = ruleContractActionsService;
    }
    async initializeStateMachine(userId, contractId) {
        try {
            if (contractId && this.machines.has(contractId)) {
                const existingMachine = this.machines.get(contractId);
                if (existingMachine) {
                    return existingMachine;
                }
            }
            const initialContext = {
                userId,
                contractId: contractId || null,
            };
            if (contractId) {
                const contract = await this.ruleContractRepository.findById(contractId);
                if (!contract) {
                    throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND, `Không tìm thấy hợp đồng với ID ${contractId}`);
                }
                initialContext.contractId = contract.id;
                initialContext.userId = contract.userId;
                initialContext.contractType = contract.type;
                initialContext.contractStatus = contract.status;
                initialContext.contractKey = contract.contractUrlPdf;
                initialContext.createdAt = contract.createdAt;
                initialContext.userSignatureAt = contract.userSignatureAt;
                initialContext.adminSignatureAt = contract.adminSignatureAt;
            }
            const machine = (0, rule_contract_machine_1.createRuleContractMachine)(initialContext);
            const actor = (0, xstate_1.createActor)(machine, {
                input: initialContext,
                systemId: `contract-${contractId || 'new'}`,
            });
            actor.subscribe((state) => {
                this.logger.log(`Chuyển đổi trạng thái hợp đồng ${state.context.contractId}: ${state.value}`);
            });
            actor.start();
            if (contractId) {
                this.machines.set(contractId, actor);
            }
            return actor;
        }
        catch (error) {
            this.logger.error(`Lỗi khi khởi tạo máy trạng thái: ${error.message}`, error.stack);
            throw error;
        }
    }
    async createContract(userId, data) {
        try {
            const actor = await this.initializeStateMachine(userId);
            actor.send({ type: rule_contract_types_1.RuleContractEvent.CREATE, data });
            const contract = await this.ruleContractActionsService.saveContract(actor.getSnapshot().context, { type: rule_contract_types_1.RuleContractEvent.CREATE, data });
            return contract.contractId || 0;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo hợp đồng: ${error.message}`, error.stack);
            throw error;
        }
    }
    async createIndividualContract(userId, data) {
        try {
            const actor = await this.initializeStateMachine(userId);
            const createData = {
                userId,
                contractType: rule_contract_entity_1.ContractTypeEnum.INDIVIDUAL,
                individualContractData: data
            };
            actor.send({ type: rule_contract_types_1.RuleContractEvent.CREATE, data: createData });
            const result = await this.ruleContractActionsService.saveIndividualContract(actor.getSnapshot().context, { type: rule_contract_types_1.RuleContractEvent.CREATE, data: createData });
            return result;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo hợp đồng cá nhân: ${error.message}`, error.stack);
            throw error;
        }
    }
    async signContract(contractId, data) {
        try {
            const actor = await this.initializeStateMachine(0, contractId);
            actor.send({ type: rule_contract_types_1.RuleContractEvent.SIGN_BY_USER, data });
            await this.ruleContractActionsService.saveUserSignature(actor.getSnapshot().context, { type: rule_contract_types_1.RuleContractEvent.SIGN_BY_USER, data });
            await this.ruleContractActionsService.notifyAdmin(actor.getSnapshot().context, { type: rule_contract_types_1.RuleContractEvent.SIGN_BY_USER, data });
            return actor.getSnapshot().value;
        }
        catch (error) {
            this.logger.error(`Lỗi khi ký hợp đồng ${contractId}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async approveContract(contractId, data) {
        try {
            const actor = await this.initializeStateMachine(0, contractId);
            if (!this.ruleContractActionsService.isAdmin(actor.getSnapshot().context, { type: rule_contract_types_1.RuleContractEvent.APPROVE, data })) {
                throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.PERMISSION_DENIED, 'Chỉ admin mới có quyền phê duyệt hợp đồng');
            }
            actor.send({ type: rule_contract_types_1.RuleContractEvent.APPROVE, data });
            await this.ruleContractActionsService.saveAdminApproval(actor.getSnapshot().context, { type: rule_contract_types_1.RuleContractEvent.APPROVE, data });
            await this.ruleContractActionsService.notifyUser(actor.getSnapshot().context, { type: rule_contract_types_1.RuleContractEvent.APPROVE, data });
            await this.ruleContractActionsService.finalizeContract(actor.getSnapshot().context, { type: rule_contract_types_1.RuleContractEvent.APPROVE, data });
            return actor.getSnapshot().value;
        }
        catch (error) {
            this.logger.error(`Lỗi khi phê duyệt hợp đồng ${contractId}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async rejectContract(contractId, data) {
        try {
            const actor = await this.initializeStateMachine(0, contractId);
            if (!this.ruleContractActionsService.isAdmin(actor.getSnapshot().context, { type: rule_contract_types_1.RuleContractEvent.REJECT, data })) {
                throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.PERMISSION_DENIED, 'Chỉ admin mới có quyền từ chối hợp đồng');
            }
            actor.send({ type: rule_contract_types_1.RuleContractEvent.REJECT, data });
            await this.ruleContractActionsService.saveRejection(actor.getSnapshot().context, { type: rule_contract_types_1.RuleContractEvent.REJECT, data });
            await this.ruleContractActionsService.notifyUser(actor.getSnapshot().context, { type: rule_contract_types_1.RuleContractEvent.REJECT, data });
            return actor.getSnapshot().value;
        }
        catch (error) {
            this.logger.error(`Lỗi khi từ chối hợp đồng ${contractId}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async resubmitContract(contractId) {
        try {
            const actor = await this.initializeStateMachine(0, contractId);
            actor.send({ type: rule_contract_types_1.RuleContractEvent.RESUBMIT });
            await this.ruleContractActionsService.clearRejectionReason(actor.getSnapshot().context, { type: rule_contract_types_1.RuleContractEvent.RESUBMIT });
            return actor.getSnapshot().value;
        }
        catch (error) {
            this.logger.error(`Lỗi khi gửi lại hợp đồng ${contractId}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async upgradeToBusinessContract(contractId, data) {
        try {
            const actor = await this.initializeStateMachine(0, contractId);
            if (!this.ruleContractActionsService.canUpgradeToBusinessType(actor.getSnapshot().context, { type: rule_contract_types_1.RuleContractEvent.UPGRADE_TO_BUSINESS, data })) {
                throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.INVALID_OPERATION, 'Không thể nâng cấp hợp đồng này lên loại doanh nghiệp');
            }
            actor.send({ type: rule_contract_types_1.RuleContractEvent.UPGRADE_TO_BUSINESS, data });
            await this.ruleContractActionsService.upgradeContractType(actor.getSnapshot().context, { type: rule_contract_types_1.RuleContractEvent.UPGRADE_TO_BUSINESS, data });
            return actor.getSnapshot().value;
        }
        catch (error) {
            this.logger.error(`Lỗi khi nâng cấp hợp đồng ${contractId} lên loại doanh nghiệp: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getCurrentState(contractId) {
        try {
            const actor = await this.initializeStateMachine(0, contractId);
            return actor.getSnapshot().value;
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy trạng thái hiện tại của hợp đồng ${contractId}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getContractContext(contractId) {
        try {
            const actor = await this.initializeStateMachine(0, contractId);
            return actor.getSnapshot().context;
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy context của hợp đồng ${contractId}: ${error.message}`, error.stack);
            throw error;
        }
    }
    async canExecuteEvent(contractId, eventType) {
        try {
            const actor = await this.initializeStateMachine(0, contractId);
            const snapshot = actor.getSnapshot();
            const currentState = snapshot.value;
            switch (currentState) {
                case rule_contract_types_1.RuleContractState.DRAFT:
                    return [
                        rule_contract_types_1.RuleContractEvent.CREATE,
                        rule_contract_types_1.RuleContractEvent.SIGN_BY_USER,
                        rule_contract_types_1.RuleContractEvent.UPGRADE_TO_BUSINESS
                    ].includes(eventType);
                case rule_contract_types_1.RuleContractState.PENDING_APPROVAL:
                    return [
                        rule_contract_types_1.RuleContractEvent.APPROVE,
                        rule_contract_types_1.RuleContractEvent.REJECT
                    ].includes(eventType);
                case rule_contract_types_1.RuleContractState.REJECTED:
                    return eventType === rule_contract_types_1.RuleContractEvent.RESUBMIT;
                case rule_contract_types_1.RuleContractState.APPROVED:
                    return false;
                default:
                    return false;
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi kiểm tra event ${eventType} cho hợp đồng ${contractId}: ${error.message}`, error.stack);
            return false;
        }
    }
    async getAvailableEvents(contractId) {
        try {
            const availableEvents = [];
            for (const event of Object.values(rule_contract_types_1.RuleContractEvent)) {
                if (await this.canExecuteEvent(contractId, event)) {
                    availableEvents.push(event);
                }
            }
            return availableEvents;
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy danh sách events cho hợp đồng ${contractId}: ${error.message}`, error.stack);
            return [];
        }
    }
    async cleanupStateMachine(contractId) {
        try {
            if (this.machines.has(contractId)) {
                const actor = this.machines.get(contractId);
                if (actor) {
                    actor.stop();
                }
                this.machines.delete(contractId);
                this.logger.log(`Đã cleanup máy trạng thái cho hợp đồng ${contractId}`);
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi cleanup máy trạng thái cho hợp đồng ${contractId}: ${error.message}`, error.stack);
        }
    }
    getStateMachineStats() {
        const contractIds = Array.from(this.machines.keys());
        return {
            activeCount: this.machines.size,
            contractIds,
        };
    }
};
exports.RuleContractStateService = RuleContractStateService;
exports.RuleContractStateService = RuleContractStateService = RuleContractStateService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.RuleContractRepository,
        rule_contract_actions_service_1.RuleContractActionsService])
], RuleContractStateService);
//# sourceMappingURL=rule-contract-state.service.js.map