"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RuleContractModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const rule_contract_entity_1 = require("./entities/rule-contract.entity");
const repositories_1 = require("./repositories");
const services_1 = require("./admin/services");
const services_2 = require("./user/services");
const controllers_1 = require("./admin/controllers");
const controllers_2 = require("./user/controllers");
const user_module_1 = require("../user/user.module");
const services_module_1 = require("../../shared/services/services.module");
const contract_helper_service_1 = require("./user/services/contract-helper.service");
const system_configuration_module_1 = require("../system-configuration/system-configuration.module");
const email_module_1 = require("../email/email.module");
const rule_contract_state_service_1 = require("./state-machine/rule-contract-state.service");
const rule_contract_actions_service_1 = require("./state-machine/rule-contract-actions.service");
let RuleContractModule = class RuleContractModule {
};
exports.RuleContractModule = RuleContractModule;
exports.RuleContractModule = RuleContractModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([rule_contract_entity_1.RuleContract]),
            user_module_1.UserModule,
            services_module_1.ServicesModule,
            system_configuration_module_1.SystemConfigurationModule,
            email_module_1.EmailModule,
        ],
        controllers: [
            controllers_1.RuleContractAdminController,
            controllers_2.RuleContractUserController,
        ],
        providers: [
            repositories_1.RuleContractRepository,
            services_1.RuleContractAdminService,
            services_2.RuleContractUserService,
            contract_helper_service_1.ContractHelperService,
            rule_contract_state_service_1.RuleContractStateService,
            rule_contract_actions_service_1.RuleContractActionsService,
        ],
        exports: [
            repositories_1.RuleContractRepository,
            services_1.RuleContractAdminService,
            services_2.RuleContractUserService,
            contract_helper_service_1.ContractHelperService,
            rule_contract_state_service_1.RuleContractStateService,
            rule_contract_actions_service_1.RuleContractActionsService,
        ],
    })
], RuleContractModule);
//# sourceMappingURL=rule-contract.module.js.map