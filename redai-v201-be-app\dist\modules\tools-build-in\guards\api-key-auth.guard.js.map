{"version": 3, "file": "api-key-auth.guard.js", "sourceRoot": "", "sources": ["../../../../src/modules/tools-build-in/guards/api-key-auth.guard.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAmF;AACnF,uCAAyC;AAIzC,2DAAkD;AAClD,8CAAmE;AACnE,8CAAiD;AACjD,4CAAgD;AAChD,oCAAsC;AACtC,qCAAqC;AAO9B,IAAM,eAAe,uBAArB,MAAM,eAAe;IAIP;IACA;IACA;IALF,MAAM,GAAG,IAAI,eAAM,CAAC,iBAAe,CAAC,IAAI,CAAC,CAAC;IAE3D,YACmB,SAAoB,EACpB,UAAsB,EACtB,UAAsB;QAFtB,cAAS,GAAT,SAAS,CAAW;QACpB,eAAU,GAAV,UAAU,CAAY;QACtB,eAAU,GAAV,UAAU,CAAY;IACtC,CAAC;IAOJ,KAAK,CAAC,WAAW,CACf,OAAyB;QAGzB,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CACvD,6BAAgB,EAChB,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAC3C,CAAC;QAGF,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAW,CAAC;QAG7D,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAE3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,MAAM,EAAE,CAAC,CAAC;QAGvC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,yBAAY,CACpB,+CAAkC,CAAC,eAAe,EAClD,0CAA0C,CAC3C,CAAC;QACJ,CAAC;QAGD,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gDAAgD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC5F,MAAM,IAAI,yBAAY,CACpB,+CAAkC,CAAC,eAAe,EAClD,6CAA6C,CAC9C,CAAC;QACJ,CAAC;QAGD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC9D,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;YAC/D,MAAM,IAAI,yBAAY,CACpB,+CAAkC,CAAC,eAAe,EAClD,qBAAqB,CACtB,CAAC;QACJ,CAAC;QAGD,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;YACjE,MAAM,IAAI,yBAAY,CACpB,+CAAkC,CAAC,cAAc,EACjD,uBAAuB,CACxB,CAAC;QACJ,CAAC;QAGD,OAAO,CAAC,OAAO,CAAC,GAAG;YACjB,EAAE,EAAE,UAAU,CAAC,OAAO;YACtB,MAAM,EAAE,UAAU,CAAC,MAAM;SAC1B,CAAC;QAGF,OAAO,IAAI,CAAC;IACd,CAAC;IAOO,aAAa,CAAC,OAAgB;QACpC,OAAO,OAAO,CAAC,OAAO,CAAC,4BAAgB,CAAC,OAAO,CAAC,WAAW,EAAE,CAAW,CAAC;IAC3E,CAAC;IAOO,KAAK,CAAC,gBAAgB,CAAC,OAAe;QAC5C,IAAI,CAAC;YAEH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,UAAU;iBAChC,kBAAkB,EAAE;iBACpB,MAAM,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;iBACxB,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC;iBACvB,KAAK,CAAC,qBAAqB,EAAE,EAAE,OAAO,EAAE,CAAC;iBACzC,QAAQ,CAAC,0BAA0B,CAAC;iBACpC,SAAS,EAAE,CAAC;YAEf,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO,IAAI,CAAC;YACd,CAAC;YAGD,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,KAAK,UAAU,IAAI,KAAK,CAAC,MAAM,KAAK,SAAS,CAAC;YAE3E,OAAO;gBACL,EAAE,EAAE,KAAK,CAAC,EAAE;gBACZ,QAAQ;aACT,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC3E,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAOO,UAAU,CAAC,MAAc;QAC/B,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,OAAO,UAAU,CAAC;QACpB,CAAC;QAGD,OAAO,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC;CACF,CAAA;AA1IY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;qCAKmB,gBAAS;QACR,kBAAU;QACV,oBAAU;GAN9B,eAAe,CA0I3B"}