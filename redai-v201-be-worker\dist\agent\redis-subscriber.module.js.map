{"version": 3, "sources": ["../../src/agent/redis-subscriber.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\nimport { ClientsModule, Transport } from '@nestjs/microservices';\nimport { env } from '../config';\n\n/**\n * Redis Subscriber Module for Worker Service\n * \n * This module configures NestJS microservices for Redis pub/sub communication\n * to receive events from the backend API and process agent runs.\n */\n@Module({\n  imports: [\n    ClientsModule.register([\n      {\n        name: 'REDIS_SUBSCRIBER',\n        transport: Transport.REDIS,\n        options: {\n          // Parse Redis URL to extract connection details\n          ...parseRedisUrl(env.external.REDIS_URL),\n          retryDelay: 1000,\n          maxRetriesPerRequest: 3,\n        },\n      },\n    ]),\n  ],\n  exports: [ClientsModule],\n})\nexport class RedisSubscriberModule {}\n\n/**\n * Parse Redis URL to extract connection options\n * @param redisUrl Redis URL string\n * @returns Redis connection options\n */\nfunction parseRedisUrl(redisUrl: string) {\n  try {\n    const url = new URL(redisUrl);\n    \n    return {\n      host: url.hostname,\n      port: parseInt(url.port) || 6379,\n      password: url.password || undefined,\n      db: parseInt(url.pathname.slice(1)) || 0, // Extract DB from URL path\n    };\n  } catch (error) {\n    // Fallback to default values if URL parsing fails\n    return {\n      host: 'localhost',\n      port: 6379,\n      db: 0,\n    };\n  }\n}\n"], "names": ["RedisSubscriberModule", "imports", "ClientsModule", "register", "name", "transport", "Transport", "REDIS", "options", "parseRedisUrl", "env", "external", "REDIS_URL", "retry<PERSON><PERSON><PERSON>", "maxRetriesPerRequest", "exports", "redisUrl", "url", "URL", "host", "hostname", "port", "parseInt", "password", "undefined", "db", "pathname", "slice", "error"], "mappings": ";;;;+BA2BaA;;;eAAAA;;;wBA3BU;+BACkB;wBACrB;;;;;;;AAyBb,IAAA,AAAMA,wBAAN,MAAMA;AAAuB;;;QAhBlCC,SAAS;YACPC,4BAAa,CAACC,QAAQ,CAAC;gBACrB;oBACEC,MAAM;oBACNC,WAAWC,wBAAS,CAACC,KAAK;oBAC1BC,SAAS;wBACP,gDAAgD;wBAChD,GAAGC,cAAcC,WAAG,CAACC,QAAQ,CAACC,SAAS,CAAC;wBACxCC,YAAY;wBACZC,sBAAsB;oBACxB;gBACF;aACD;SACF;QACDC,SAAS;YAACb,4BAAa;SAAC;;;AAI1B;;;;CAIC,GACD,SAASO,cAAcO,QAAgB;IACrC,IAAI;QACF,MAAMC,MAAM,IAAIC,IAAIF;QAEpB,OAAO;YACLG,MAAMF,IAAIG,QAAQ;YAClBC,MAAMC,SAASL,IAAII,IAAI,KAAK;YAC5BE,UAAUN,IAAIM,QAAQ,IAAIC;YAC1BC,IAAIH,SAASL,IAAIS,QAAQ,CAACC,KAAK,CAAC,OAAO;QACzC;IACF,EAAE,OAAOC,OAAO;QACd,kDAAkD;QAClD,OAAO;YACLT,MAAM;YACNE,MAAM;YACNI,IAAI;QACN;IACF;AACF"}