"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestUploadController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const test_upload_service_1 = require("../services/test-upload.service");
const media_upload_url_dto_1 = require("../dto/media-upload-url.dto");
const response_1 = require("../../../common/response");
let TestUploadController = class TestUploadController {
    testUploadService;
    constructor(testUploadService) {
        this.testUploadService = testUploadService;
    }
    async createUploadUrl(dto) {
        const result = await this.testUploadService.createMediaUploadUrl(dto);
        return response_1.ApiResponseDto.success(result, 'URL tạm thời được tạo thành công');
    }
};
exports.TestUploadController = TestUploadController;
__decorate([
    (0, common_1.Post)('upload-url'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo URL tạm thời để upload tài nguyên' }),
    (0, swagger_1.ApiBody)({ type: media_upload_url_dto_1.MediaUploadUrlDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'URL tạm thời được tạo thành công',
        schema: response_1.ApiResponseDto.getSchema(media_upload_url_dto_1.PresignedUrlResponseDto),
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [media_upload_url_dto_1.MediaUploadUrlDto]),
    __metadata("design:returntype", Promise)
], TestUploadController.prototype, "createUploadUrl", null);
exports.TestUploadController = TestUploadController = __decorate([
    (0, swagger_1.ApiTags)('Test'),
    (0, common_1.Controller)('test'),
    __metadata("design:paramtypes", [test_upload_service_1.TestUploadService])
], TestUploadController);
//# sourceMappingURL=test-upload.controller.js.map