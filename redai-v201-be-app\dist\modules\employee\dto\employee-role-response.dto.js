"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmployeeRoleResponseDto = exports.PermissionResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class PermissionResponseDto {
    id;
    module;
    action;
    description;
}
exports.PermissionResponseDto = PermissionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của quyền',
        example: 1
    }),
    __metadata("design:type", Number)
], PermissionResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Module của quyền',
        example: 'users'
    }),
    __metadata("design:type", String)
], PermissionResponseDto.prototype, "module", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Hành động của quyền',
        example: 'read'
    }),
    __metadata("design:type", String)
], PermissionResponseDto.prototype, "action", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả của quyền',
        example: 'Xem danh sách người dùng'
    }),
    __metadata("design:type", String)
], PermissionResponseDto.prototype, "description", void 0);
class EmployeeRoleResponseDto {
    id;
    name;
    code;
    description;
    permissions;
}
exports.EmployeeRoleResponseDto = EmployeeRoleResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của vai trò',
        example: 1
    }),
    __metadata("design:type", Number)
], EmployeeRoleResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên vai trò',
        example: 'Admin'
    }),
    __metadata("design:type", String)
], EmployeeRoleResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mã vai trò',
        example: 'admin'
    }),
    __metadata("design:type", String)
], EmployeeRoleResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả vai trò',
        example: 'Quyền quản trị viên'
    }),
    __metadata("design:type", String)
], EmployeeRoleResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách quyền của vai trò',
        type: [PermissionResponseDto]
    }),
    __metadata("design:type", Array)
], EmployeeRoleResponseDto.prototype, "permissions", void 0);
//# sourceMappingURL=employee-role-response.dto.js.map