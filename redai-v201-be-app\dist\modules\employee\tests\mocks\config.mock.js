"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mockConfigModule = exports.mockConfigService = void 0;
exports.mockConfigService = {
    get: jest.fn((key) => {
        const configValues = {
            DB_HOST: 'localhost',
            DB_PORT: 5432,
            DB_USERNAME: 'test',
            DB_PASSWORD: 'test',
            DB_DATABASE: 'test_db',
            CF_R2_ACCESS_KEY: 'mock_access_key',
            CF_R2_SECRET_KEY: 'mock_secret_key',
            CF_R2_ENDPOINT: 'https://mock-endpoint.com',
            CF_BUCKET_NAME: 'mock-bucket',
            OPENAI_API_KEY: 'mock_openai_key',
            CDN_URL: 'https://mock-cdn.com',
            CDN_SECRET_KEY: 'mock_cdn_key',
            JWT_SECRET: 'mock_jwt_secret',
            JWT_EXPIRES_IN: '1h',
            JWT_REFRESH_EXPIRES_IN: '7d',
        };
        return configValues[key];
    }),
};
exports.mockConfigModule = {
    provide: 'ConfigService',
    useValue: exports.mockConfigService,
};
//# sourceMappingURL=config.mock.js.map