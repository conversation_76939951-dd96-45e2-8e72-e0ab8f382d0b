"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var GenericPageRepository_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericPageRepository = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const generic_page_entity_1 = require("../entities/generic-page.entity");
const generic_page_enum_1 = require("../constants/generic-page.enum");
const common_2 = require("../../../common");
const generic_page_error_code_1 = require("../exceptions/generic-page-error.code");
let GenericPageRepository = GenericPageRepository_1 = class GenericPageRepository extends typeorm_1.Repository {
    dataSource;
    logger = new common_1.Logger(GenericPageRepository_1.name);
    constructor(dataSource) {
        super(generic_page_entity_1.GenericPage, dataSource.createEntityManager());
        this.dataSource = dataSource;
    }
    createBaseQuery() {
        return this.createQueryBuilder('genericPage');
    }
    async findById(id) {
        try {
            const page = await this.createBaseQuery()
                .where('genericPage.id = :id', { id })
                .getOne();
            if (!page) {
                throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_FOUND, `Không tìm thấy trang với ID ${id}`);
            }
            return page;
        }
        catch (error) {
            if (error instanceof common_2.AppException) {
                throw error;
            }
            this.logger.error(`Error finding page by ID: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_FOUND, `Lỗi khi tìm trang với ID ${id}`);
        }
    }
    async findByPath(path) {
        try {
            const page = await this.createBaseQuery()
                .where('genericPage.path = :path', { path })
                .getOne();
            if (!page) {
                throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_FOUND, `Không tìm thấy trang với đường dẫn ${path}`);
            }
            return page;
        }
        catch (error) {
            if (error instanceof common_2.AppException) {
                throw error;
            }
            this.logger.error(`Error finding page by path: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_FOUND, `Lỗi khi tìm trang với đường dẫn ${path}`);
        }
    }
    async isPathExists(path, excludeId) {
        try {
            const query = this.createBaseQuery()
                .where('genericPage.path = :path', { path });
            if (excludeId) {
                query.andWhere('genericPage.id != :excludeId', { excludeId });
            }
            const count = await query.getCount();
            return count > 0;
        }
        catch (error) {
            this.logger.error(`Error checking path existence: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_CREATE_ERROR, 'Lỗi khi kiểm tra đường dẫn');
        }
    }
    async findPublishedByPath(path) {
        try {
            const page = await this.createBaseQuery()
                .where('genericPage.path = :path', { path })
                .andWhere('genericPage.status = :status', { status: generic_page_enum_1.GenericPageStatusEnum.PUBLISHED })
                .getOne();
            if (!page) {
                throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_FOUND, `Không tìm thấy trang đã xuất bản với đường dẫn ${path}`);
            }
            return page;
        }
        catch (error) {
            if (error instanceof common_2.AppException) {
                throw error;
            }
            this.logger.error(`Error finding published page by path: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_FOUND, `Lỗi khi tìm trang đã xuất bản với đường dẫn ${path}`);
        }
    }
};
exports.GenericPageRepository = GenericPageRepository;
exports.GenericPageRepository = GenericPageRepository = GenericPageRepository_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], GenericPageRepository);
//# sourceMappingURL=generic-page.repository.js.map