"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversionSearchResponseDto = exports.ConversionResponseDto = exports.ConversionCustomerResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ConversionCustomerResponseDto {
    id;
    name;
    email;
    phone;
    avatar;
    platform;
    metadata;
}
exports.ConversionCustomerResponseDto = ConversionCustomerResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của khách hàng',
        example: 1,
        nullable: true
    }),
    __metadata("design:type", Number)
], ConversionCustomerResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên khách hàng',
        example: 'Nguyễn Văn A'
    }),
    __metadata("design:type", String)
], ConversionCustomerResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email khách hàng',
        example: {
            primary: '<EMAIL>',
            verified: true,
            alternates: ['<EMAIL>']
        },
        nullable: true
    }),
    __metadata("design:type", Object)
], ConversionCustomerResponseDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số điện thoại khách hàng',
        example: '0987654321',
        nullable: true
    }),
    __metadata("design:type", String)
], ConversionCustomerResponseDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Ảnh đại diện',
        example: 'https://example.com/avatar.jpg',
        nullable: true
    }),
    __metadata("design:type", String)
], ConversionCustomerResponseDto.prototype, "avatar", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nền tảng nguồn',
        example: 'Facebook',
        nullable: true
    }),
    __metadata("design:type", String)
], ConversionCustomerResponseDto.prototype, "platform", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Metadata tùy chỉnh',
        example: {
            age: 30,
            interests: ['thể thao', 'âm nhạc'],
            lastInteraction: 1672531200000
        },
        nullable: true
    }),
    __metadata("design:type", Object)
], ConversionCustomerResponseDto.prototype, "metadata", void 0);
class ConversionResponseDto {
    id;
    convertCustomerId;
    customer;
    conversionType;
    source;
    notes;
    content;
    createdAt;
}
exports.ConversionResponseDto = ConversionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của bản ghi chuyển đổi',
        example: 1,
        nullable: true
    }),
    __metadata("design:type", Number)
], ConversionResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID khách hàng được chuyển đổi',
        example: 2,
        nullable: true
    }),
    __metadata("design:type", Number)
], ConversionResponseDto.prototype, "convertCustomerId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin khách hàng được chuyển đổi',
        type: ConversionCustomerResponseDto,
        nullable: true
    }),
    __metadata("design:type", ConversionCustomerResponseDto)
], ConversionResponseDto.prototype, "customer", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại chuyển đổi',
        example: 'online',
        nullable: true
    }),
    __metadata("design:type", String)
], ConversionResponseDto.prototype, "conversionType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nguồn gốc chuyển đổi',
        example: 'Facebook',
        nullable: true
    }),
    __metadata("design:type", String)
], ConversionResponseDto.prototype, "source", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Ghi chú',
        example: 'Khách hàng quan tâm đến sản phẩm X',
        nullable: true
    }),
    __metadata("design:type", String)
], ConversionResponseDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nội dung bổ sung',
        example: {
            interestedProducts: [1, 2, 3],
            conversationHistory: [
                { time: 1672531200000, message: 'Xin chào' },
                { time: 1672531260000, message: 'Tôi cần tư vấn về sản phẩm X' }
            ]
        },
        nullable: true
    }),
    __metadata("design:type", Object)
], ConversionResponseDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo',
        example: 1672531200000,
        nullable: true
    }),
    __metadata("design:type", Number)
], ConversionResponseDto.prototype, "createdAt", void 0);
class ConversionSearchResponseDto {
    conversions;
    total;
    filters;
}
exports.ConversionSearchResponseDto = ConversionSearchResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách chuyển đổi tìm thấy',
        type: [ConversionResponseDto]
    }),
    __metadata("design:type", Array)
], ConversionSearchResponseDto.prototype, "conversions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tổng số kết quả',
        example: 25
    }),
    __metadata("design:type", Number)
], ConversionSearchResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Bộ lọc đã áp dụng',
        example: {
            conversionType: 'online',
            source: 'Facebook',
            dateRange: { start: 1672531200000, end: 1675209600000 }
        },
        nullable: true
    }),
    __metadata("design:type", Object)
], ConversionSearchResponseDto.prototype, "filters", void 0);
//# sourceMappingURL=conversion-response.dto.js.map