{"version": 3, "sources": ["../../../src/agent/constants/redis-events.constants.ts"], "sourcesContent": ["/**\r\n * Redis Event Patterns for Pub/Sub Communication (Agent Module)\r\n * \r\n * IMPORTANT: These constants must be kept identical to the ones in redai-v201-be-app\r\n * to maintain consistency across separate codebases.\r\n * \r\n * These constants define the event patterns used for Redis pub/sub communication\r\n * between the backend API and worker services.\r\n */\r\n\r\n/**\r\n * Event patterns for Redis pub/sub communication between backend and worker\r\n */\r\nexport const REDIS_EVENTS = {\r\n  /**\r\n   * Trigger event to start processing a run\r\n   * Published by: Backend API (Chat Module)\r\n   * Consumed by: Worker Service (Agent Module)\r\n   */\r\n  RUN_TRIGGER: 'run.trigger',\r\n\r\n  /**\r\n   * Cancel event to stop processing a run\r\n   * Published by: Backend API (Chat Module)\r\n   * Consumed by: Worker Service (Agent Module)\r\n   */\r\n  RUN_CANCEL: 'run.cancel',\r\n\r\n  /**\r\n   * Status update event for run progress\r\n   * Published by: Worker Service (Agent Module)\r\n   * Consumed by: Backend API (optional)\r\n   */\r\n  RUN_STATUS_UPDATE: 'run.status.update',\r\n\r\n  /**\r\n   * Completion event when run finishes\r\n   * Published by: Worker Service (Agent Module)\r\n   * Consumed by: Backend API (optional)\r\n   */\r\n  RUN_COMPLETED: 'run.completed',\r\n\r\n  /**\r\n   * Error event when run fails\r\n   * Published by: Worker Service (Agent Module)\r\n   * Consumed by: Backend API (optional)\r\n   */\r\n  RUN_ERROR: 'run.error',\r\n} as const;\r\n\r\n/**\r\n * Type for Redis event patterns\r\n */\r\nexport type RedisEventPattern = typeof REDIS_EVENTS[keyof typeof REDIS_EVENTS];\r\n\r\n/**\r\n * Base interface for all Redis events\r\n */\r\nexport interface BaseRedisEvent {\r\n  eventType: RedisEventPattern;\r\n  publishedAt: number;\r\n  version?: string;\r\n}\r\n\r\n/**\r\n * Interface for run trigger event payload\r\n */\r\nexport interface RunTriggerEvent extends BaseRedisEvent {\r\n  eventType: typeof REDIS_EVENTS.RUN_TRIGGER;\r\n  runId: string;\r\n  threadId: string; // LangGraph thread ID for processing\r\n  agentId: string;\r\n  userId: number;\r\n  jwt: string; // JWT token for authenticated API calls\r\n  timestamp: number;\r\n  priority?: 'high' | 'medium' | 'low';\r\n}\r\n\r\n/**\r\n * Interface for run cancel event payload\r\n */\r\nexport interface RunCancelEvent extends BaseRedisEvent {\r\n  eventType: typeof REDIS_EVENTS.RUN_CANCEL;\r\n  threadId: string; // LangGraph thread ID for cancellation\r\n  runId?: string; // Optional, for logging purposes only\r\n  reason: string;\r\n  timestamp: number;\r\n}\r\n\r\n/**\r\n * Interface for run status update event payload\r\n */\r\nexport interface RunStatusUpdateEvent extends BaseRedisEvent {\r\n  eventType: typeof REDIS_EVENTS.RUN_STATUS_UPDATE;\r\n  runId: string;\r\n  status: string;\r\n  timestamp: number;\r\n  metadata?: any;\r\n}\r\n\r\n/**\r\n * Interface for run completed event payload\r\n */\r\nexport interface RunCompletedEvent extends BaseRedisEvent {\r\n  eventType: typeof REDIS_EVENTS.RUN_COMPLETED;\r\n  runId: string;\r\n  result: any;\r\n  timestamp: number;\r\n  duration?: number;\r\n}\r\n\r\n/**\r\n * Interface for run error event payload\r\n */\r\nexport interface RunErrorEvent extends BaseRedisEvent {\r\n  eventType: typeof REDIS_EVENTS.RUN_ERROR;\r\n  runId: string;\r\n  error: {\r\n    message: string;\r\n    code?: string;\r\n    stack?: string;\r\n  };\r\n  timestamp: number;\r\n}\r\n\r\n/**\r\n * Union type for all Redis event payloads\r\n */\r\nexport type RedisEventPayload = \r\n  | RunTriggerEvent\r\n  | RunCancelEvent\r\n  | RunStatusUpdateEvent\r\n  | RunCompletedEvent\r\n  | RunErrorEvent;\r\n"], "names": ["REDIS_EVENTS", "RUN_TRIGGER", "RUN_CANCEL", "RUN_STATUS_UPDATE", "RUN_COMPLETED", "RUN_ERROR"], "mappings": "AAAA;;;;;;;;CAQC,GAED;;CAEC;;;;+BACYA;;;eAAAA;;;AAAN,MAAMA,eAAe;IAC1B;;;;GAIC,GACDC,aAAa;IAEb;;;;GAIC,GACDC,YAAY;IAEZ;;;;GAIC,GACDC,mBAAmB;IAEnB;;;;GAIC,GACDC,eAAe;IAEf;;;;GAIC,GACDC,WAAW;AACb"}