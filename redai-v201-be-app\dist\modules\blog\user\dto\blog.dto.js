"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlogDto = exports.AuthorDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const enums_1 = require("../../enums");
class AuthorDto {
    id;
    name;
    type;
    avatar;
}
exports.AuthorDto = AuthorDto;
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID của tác giả',
        example: 1,
        nullable: true,
    }),
    __metadata("design:type", Object)
], AuthorDto.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Tên tác giả',
        example: 'Nguyễn Văn A',
        nullable: true,
    }),
    __metadata("design:type", String)
], AuthorDto.prototype, "name", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Loại tác giả',
        example: enums_1.AuthorTypeEnum.USER,
        enum: enums_1.AuthorTypeEnum,
    }),
    __metadata("design:type", String)
], AuthorDto.prototype, "type", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Avatar của tác giả',
        example: 'https://cdn.example.com/avatars/user10.jpg',
        nullable: true,
    }),
    __metadata("design:type", String)
], AuthorDto.prototype, "avatar", void 0);
class BlogDto {
    id;
    title;
    content;
    point;
    view_count;
    thumbnail_url;
    tags;
    created_at;
    updated_at;
    author;
    employee_moderator;
    status;
    enable;
    like;
}
exports.BlogDto = BlogDto;
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID của bài viết',
        example: 1,
    }),
    __metadata("design:type", Number)
], BlogDto.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Tiêu đề bài viết',
        example: 'Tiêu đề bài viết',
    }),
    __metadata("design:type", String)
], BlogDto.prototype, "title", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'URL file content trên CDN',
        example: 'URL file content trên CDN',
    }),
    __metadata("design:type", String)
], BlogDto.prototype, "content", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Số point',
        example: 100,
    }),
    __metadata("design:type", Number)
], BlogDto.prototype, "point", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Số lượt xem',
        example: 150,
    }),
    __metadata("design:type", Number)
], BlogDto.prototype, "view_count", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'URL thumbnail',
        example: 'URL thumbnail',
    }),
    __metadata("design:type", String)
], BlogDto.prototype, "thumbnail_url", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Tags',
        example: ['tag1', 'tag2'],
        type: [String],
    }),
    __metadata("design:type", Array)
], BlogDto.prototype, "tags", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo (Unix timestamp)',
        example: 1632474086123,
    }),
    __metadata("design:type", Number)
], BlogDto.prototype, "created_at", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian cập nhật (Unix timestamp)',
        example: 1632474086123,
    }),
    __metadata("design:type", Number)
], BlogDto.prototype, "updated_at", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => AuthorDto),
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin tác giả',
        type: AuthorDto,
    }),
    __metadata("design:type", AuthorDto)
], BlogDto.prototype, "author", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Nhân viên kiểm duyệt bài viết',
        example: null,
        nullable: true,
    }),
    __metadata("design:type", Object)
], BlogDto.prototype, "employee_moderator", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái của bài viết',
        example: enums_1.BlogStatusEnum.APPROVED,
        enum: enums_1.BlogStatusEnum,
    }),
    __metadata("design:type", String)
], BlogDto.prototype, "status", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái hiển thị của bài viết',
        example: true,
    }),
    __metadata("design:type", Boolean)
], BlogDto.prototype, "enable", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Số lượt like',
        example: 45,
    }),
    __metadata("design:type", Number)
], BlogDto.prototype, "like", void 0);
//# sourceMappingURL=blog.dto.js.map