"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initMockTransactionalContext = void 0;
exports.MockTransactional = MockTransactional;
function MockTransactional() {
    return function (target, propertyKey, descriptor) {
        const originalMethod = descriptor.value;
        descriptor.value = async function (...args) {
            return await originalMethod.apply(this, args);
        };
        return descriptor;
    };
}
jest.mock('typeorm-transactional', () => ({
    Transactional: function () {
        return function (target, propertyKey, descriptor) {
            const originalMethod = descriptor.value;
            descriptor.value = async function (...args) {
                return await originalMethod.apply(this, args);
            };
            return descriptor;
        };
    },
    initializeTransactionalContext: jest.fn(),
    patchTypeORMRepositoryWithBaseRepository: jest.fn(),
}));
const initMockTransactionalContext = () => {
    const { initializeTransactionalContext, patchTypeORMRepositoryWithBaseRepository } = require('typeorm-transactional');
    initializeTransactionalContext();
    patchTypeORMRepositoryWithBaseRepository();
};
exports.initMockTransactionalContext = initMockTransactionalContext;
//# sourceMappingURL=typeorm-transactional.mock.js.map