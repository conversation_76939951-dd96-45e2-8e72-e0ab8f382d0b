"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FineTuningJobController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const api_error_response_decorator_1 = require("../../../../common/decorators/api-error-response.decorator");
const fine_tuning_job_service_1 = require("../services/fine-tuning-job.service");
const create_fine_tuning_job_dto_1 = require("../dto/user-data-fine-tune/create-fine-tuning-job.dto");
const models_exception_1 = require("../../exceptions/models.exception");
const guards_1 = require("../../../auth/guards");
const response_1 = require("../../../../common/response");
const decorators_1 = require("../../../auth/decorators");
const swagger_2 = require("../../../../common/swagger");
let FineTuningJobController = class FineTuningJobController {
    fineTuningJobService;
    constructor(fineTuningJobService) {
        this.fineTuningJobService = fineTuningJobService;
    }
    async createFineTuningJob(user, createDto) {
        const result = await this.fineTuningJobService.createFineTuningJob(user.id, createDto);
        return response_1.ApiResponseDto.success(result, 'Fine-tuning job đã được tạo thành công');
    }
};
exports.FineTuningJobController = FineTuningJobController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Tạo fine-tuning job mới',
        description: `
    Tạo fine-tuning job mới cho OpenAI hoặc Google AI.
    
    **Quy trình:**
    1. Validate dataset và model cơ sở
    2. Tính toán token và chi phí
    3. Trừ R-Points từ tài khoản user
    4. Upload file training lên provider
    5. Tạo fine-tuning job
    6. Lưu thông tin vào database
    
    **Lưu ý:**
    - Nếu cung cấp userKeyLlmId, sẽ sử dụng API key riêng của user
    - Nếu không có userKeyLlmId, sẽ sử dụng system API key
    - Chi phí được tính dựa trên số token và trainingPricing của model
    - User phải có đủ R-Points để thực hiện fine-tuning
    `,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Fine-tuning job đã được tạo thành công',
        type: (response_1.ApiResponseDto),
    }),
    (0, api_error_response_decorator_1.ApiErrorResponse)(models_exception_1.MODELS_ERROR_CODES.INVALID_INPUT, models_exception_1.MODELS_ERROR_CODES.DATASET_NOT_FOUND, models_exception_1.MODELS_ERROR_CODES.MODEL_NOT_FOUND, models_exception_1.MODELS_ERROR_CODES.INVALID_PROVIDER, models_exception_1.MODELS_ERROR_CODES.MISSING_API_KEY, models_exception_1.MODELS_ERROR_CODES.MISSING_GOOGLE_CLOUD_CONFIG, models_exception_1.MODELS_ERROR_CODES.TOKEN_CALCULATION_FAILED, models_exception_1.MODELS_ERROR_CODES.INSUFFICIENT_POINTS, models_exception_1.MODELS_ERROR_CODES.FINE_TUNING_JOB_CREATION_FAILED),
    __param(0, (0, decorators_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_fine_tuning_job_dto_1.CreateFineTuningJobDto]),
    __metadata("design:returntype", Promise)
], FineTuningJobController.prototype, "createFineTuningJob", null);
exports.FineTuningJobController = FineTuningJobController = __decorate([
    (0, swagger_1.ApiTags)(swagger_2.SWAGGER_API_TAGS.USER_FINETUNING_JOB),
    (0, common_1.Controller)('user/fine-tuning-jobs'),
    (0, common_1.UseGuards)(guards_1.JwtUserGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [fine_tuning_job_service_1.FineTuningJobService])
], FineTuningJobController);
//# sourceMappingURL=fine-tuning-job.controller.js.map