"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createRuleContractMachine = void 0;
const xstate_1 = require("xstate");
const rule_contract_types_1 = require("./rule-contract.types");
const rule_contract_entity_1 = require("../entities/rule-contract.entity");
const createRuleContractMachine = (initialContext = {}) => {
    return (0, xstate_1.createMachine)({
        id: 'ruleContract',
        initial: rule_contract_types_1.RuleContractState.DRAFT,
        context: {
            contractId: null,
            userId: null,
            contractType: rule_contract_entity_1.ContractTypeEnum.INDIVIDUAL,
            contractStatus: rule_contract_entity_1.ContractStatusEnum.DRAFT,
            ...initialContext,
        },
        states: {
            [rule_contract_types_1.RuleContractState.DRAFT]: {
                on: {
                    [rule_contract_types_1.RuleContractEvent.CREATE]: {
                        target: rule_contract_types_1.RuleContractState.DRAFT,
                        actions: { type: 'saveContract' },
                    },
                    [rule_contract_types_1.RuleContractEvent.SIGN_BY_USER]: {
                        target: rule_contract_types_1.RuleContractState.PENDING_APPROVAL,
                        actions: [
                            { type: 'saveUserSignature' },
                            { type: 'notifyAdmin' }
                        ],
                        guard: { type: 'isValidSignature' },
                    },
                    [rule_contract_types_1.RuleContractEvent.UPGRADE_TO_BUSINESS]: {
                        target: rule_contract_types_1.RuleContractState.DRAFT,
                        actions: [
                            { type: 'upgradeContractType' },
                            { type: 'saveContract' }
                        ],
                        guard: { type: 'canUpgradeToBusinessType' },
                    },
                },
            },
            [rule_contract_types_1.RuleContractState.PENDING_APPROVAL]: {
                on: {
                    [rule_contract_types_1.RuleContractEvent.APPROVE]: {
                        target: rule_contract_types_1.RuleContractState.APPROVED,
                        actions: [
                            { type: 'saveAdminApproval' },
                            { type: 'notifyUser' }
                        ],
                        guard: { type: 'isAdmin' },
                    },
                    [rule_contract_types_1.RuleContractEvent.REJECT]: {
                        target: rule_contract_types_1.RuleContractState.REJECTED,
                        actions: [
                            { type: 'saveRejection' },
                            { type: 'notifyUser' }
                        ],
                        guard: { type: 'isAdmin' },
                    },
                },
            },
            [rule_contract_types_1.RuleContractState.APPROVED]: {
                type: 'final',
                entry: { type: 'finalizeContract' },
            },
            [rule_contract_types_1.RuleContractState.REJECTED]: {
                on: {
                    [rule_contract_types_1.RuleContractEvent.RESUBMIT]: {
                        target: rule_contract_types_1.RuleContractState.DRAFT,
                        actions: { type: 'clearRejectionReason' },
                    },
                },
            },
        },
    });
};
exports.createRuleContractMachine = createRuleContractMachine;
//# sourceMappingURL=rule-contract.machine.js.map