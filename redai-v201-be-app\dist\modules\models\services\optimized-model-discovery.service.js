"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OptimizedModelDiscoveryService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OptimizedModelDiscoveryService = void 0;
const common_1 = require("@nestjs/common");
const ai_provider_helper_1 = require("../../../shared/services/ai/helpers/ai-provider.helper");
const model_registry_repository_1 = require("../repositories/model-registry.repository");
const system_models_repository_1 = require("../repositories/system-models.repository");
const user_models_repository_1 = require("../repositories/user-models.repository");
const system_model_key_llm_repository_1 = require("../repositories/system-model-key-llm.repository");
const user_model_key_llm_repository_1 = require("../repositories/user-model-key-llm.repository");
const pattern_matching_engine_service_1 = require("./pattern-matching-engine.service");
const performance_monitor_service_1 = require("./performance-monitor.service");
const bulk_model_operations_service_1 = require("./bulk-model-operations.service");
let OptimizedModelDiscoveryService = OptimizedModelDiscoveryService_1 = class OptimizedModelDiscoveryService {
    aiProviderHelper;
    modelRegistryRepository;
    systemModelsRepository;
    userModelsRepository;
    systemModelKeyLlmRepository;
    userModelKeyLlmRepository;
    patternMatchingEngine;
    performanceMonitor;
    bulkModelOperations;
    logger = new common_1.Logger(OptimizedModelDiscoveryService_1.name);
    constructor(aiProviderHelper, modelRegistryRepository, systemModelsRepository, userModelsRepository, systemModelKeyLlmRepository, userModelKeyLlmRepository, patternMatchingEngine, performanceMonitor, bulkModelOperations) {
        this.aiProviderHelper = aiProviderHelper;
        this.modelRegistryRepository = modelRegistryRepository;
        this.systemModelsRepository = systemModelsRepository;
        this.userModelsRepository = userModelsRepository;
        this.systemModelKeyLlmRepository = systemModelKeyLlmRepository;
        this.userModelKeyLlmRepository = userModelKeyLlmRepository;
        this.patternMatchingEngine = patternMatchingEngine;
        this.performanceMonitor = performanceMonitor;
        this.bulkModelOperations = bulkModelOperations;
    }
    async discoverSystemModels(models, keyId, provider) {
        const operationId = `discover_system_models_${keyId}_${Date.now()}`;
        this.performanceMonitor.startMonitoring(operationId, {
            operation: 'discoverSystemModels',
            keyId,
            provider,
            modelCount: models.length
        });
        try {
            const patterns = await this.getCompiledPatterns(provider);
            const patternMatchStart = Date.now();
            const modelIds = models.map(model => this.getModelId(model));
            const matches = this.patternMatchingEngine.matchModels(modelIds, patterns, provider);
            const patternMatchTime = Date.now() - patternMatchStart;
            const matchedModels = [];
            for (const model of models) {
                const modelId = this.getModelId(model);
                const match = matches.get(modelId);
                if (match) {
                    matchedModels.push({
                        model,
                        registryId: match.registryId,
                        score: match.score
                    });
                }
            }
            matchedModels.sort((a, b) => b.score - a.score);
            this.logger.log(`Pattern matching completed: ${matchedModels.length}/${models.length} models matched in ${patternMatchTime}ms`);
            const dbOperationStart = Date.now();
            const result = await this.bulkSaveSystemModels(matchedModels, keyId);
            const dbOperationTime = Date.now() - dbOperationStart;
            this.performanceMonitor.updateMetrics(operationId, {
                modelsProcessed: models.length,
                patternsMatched: matchedModels.length,
                newModelsCreated: result.newModelsCreated,
                mappingsCreated: result.mappingsCreated
            });
            const totalMetrics = this.performanceMonitor.stopMonitoring(operationId);
            result.performanceMetrics = {
                patternMatchingTime: patternMatchTime,
                databaseOperationTime: dbOperationTime,
                totalTime: totalMetrics?.duration || 0,
                cacheHitRate: this.calculateCacheHitRate(provider)
            };
            this.logger.log(`System model discovery completed: ${result.newModelsCreated} new, ${result.existingModelsFound} existing`);
            return result;
        }
        catch (error) {
            this.performanceMonitor.addError(operationId, error.message);
            this.logger.error(`System model discovery failed: ${error.message}`, error.stack);
            throw error;
        }
    }
    async discoverUserModels(models, keyId, provider) {
        const operationId = `discover_user_models_${keyId}_${Date.now()}`;
        this.performanceMonitor.startMonitoring(operationId, {
            operation: 'discoverUserModels',
            keyId,
            provider,
            modelCount: models.length
        });
        try {
            const patterns = await this.getCompiledPatterns(provider);
            const patternMatchStart = Date.now();
            const modelIds = models.map(model => this.getModelId(model));
            const matches = this.patternMatchingEngine.matchModels(modelIds, patterns, provider);
            const patternMatchTime = Date.now() - patternMatchStart;
            const matchedModels = [];
            for (const model of models) {
                const modelId = this.getModelId(model);
                const match = matches.get(modelId);
                if (match) {
                    matchedModels.push({
                        model,
                        registryId: match.registryId,
                        score: match.score
                    });
                }
            }
            matchedModels.sort((a, b) => b.score - a.score);
            this.logger.log(`Pattern matching completed: ${matchedModels.length}/${models.length} models matched in ${patternMatchTime}ms`);
            const dbOperationStart = Date.now();
            const result = await this.bulkSaveUserModels(matchedModels, keyId);
            const dbOperationTime = Date.now() - dbOperationStart;
            this.performanceMonitor.updateMetrics(operationId, {
                modelsProcessed: models.length,
                patternsMatched: matchedModels.length,
                newModelsCreated: result.newModelsCreated,
                mappingsCreated: result.mappingsCreated
            });
            const totalMetrics = this.performanceMonitor.stopMonitoring(operationId);
            result.performanceMetrics = {
                patternMatchingTime: patternMatchTime,
                databaseOperationTime: dbOperationTime,
                totalTime: totalMetrics?.duration || 0,
                cacheHitRate: this.calculateCacheHitRate(provider)
            };
            this.logger.log(`User model discovery completed: ${result.newModelsCreated} new, ${result.existingModelsFound} existing`);
            return result;
        }
        catch (error) {
            this.performanceMonitor.addError(operationId, error.message);
            this.logger.error(`User model discovery failed: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getCompiledPatterns(provider) {
        const cacheKey = `patterns_${provider}`;
        let patterns = this.patternMatchingEngine.getCachedPatterns(cacheKey);
        if (!patterns) {
            this.logger.debug(`Cache miss for patterns: ${provider}`);
            const registryPatterns = await this.modelRegistryRepository.findActivePatterns();
            patterns = this.patternMatchingEngine.compilePatterns(registryPatterns, provider);
            this.patternMatchingEngine.cachePatterns(cacheKey, patterns);
            this.logger.debug(`Compiled and cached ${patterns.length} patterns for provider: ${provider}`);
        }
        else {
            this.logger.debug(`Cache hit for patterns: ${provider} (${patterns.length} patterns)`);
        }
        return patterns;
    }
    getModelId(model) {
        return model.id || model.name || 'unknown';
    }
    calculateCacheHitRate(provider) {
        const stats = this.patternMatchingEngine.getCacheStats();
        const providerEntry = stats.entries.find(entry => entry.key.includes(provider));
        if (providerEntry && !providerEntry.isExpired) {
            return 0.9;
        }
        return 0.1;
    }
    async bulkSaveSystemModels(matchedModels, keyId) {
        if (matchedModels.length === 0) {
            return {
                totalModelsFound: 0,
                modelsMatched: 0,
                newModelsCreated: 0,
                existingModelsFound: 0,
                mappingsCreated: 0,
                savedModels: [],
                errors: []
            };
        }
        try {
            const bulkModelData = matchedModels.map(({ model, registryId, score }) => ({
                modelId: this.getModelId(model),
                modelRegistryId: registryId,
                metadata: {
                    score,
                    originalModel: model
                }
            }));
            const bulkResult = await this.bulkModelOperations.bulkUpsertSystemModels(bulkModelData, keyId);
            const savedModels = matchedModels.map(({ model, registryId }) => ({
                modelId: this.getModelId(model),
                modelRegistryId: registryId,
                isNew: bulkResult.modelIdMap.has(this.getModelId(model))
            }));
            return {
                totalModelsFound: matchedModels.length,
                modelsMatched: matchedModels.length,
                newModelsCreated: bulkResult.newModelsCreated,
                existingModelsFound: bulkResult.existingModelsFound,
                mappingsCreated: bulkResult.mappingsCreated,
                savedModels,
                errors: bulkResult.errors
            };
        }
        catch (error) {
            this.logger.error(`Bulk save system models failed: ${error.message}`, error.stack);
            return {
                totalModelsFound: matchedModels.length,
                modelsMatched: matchedModels.length,
                newModelsCreated: 0,
                existingModelsFound: 0,
                mappingsCreated: 0,
                savedModels: [],
                errors: [`Bulk save failed: ${error.message}`]
            };
        }
    }
    async bulkSaveUserModels(matchedModels, keyId) {
        if (matchedModels.length === 0) {
            return {
                totalModelsFound: 0,
                modelsMatched: 0,
                newModelsCreated: 0,
                existingModelsFound: 0,
                mappingsCreated: 0,
                savedModels: [],
                errors: []
            };
        }
        try {
            const bulkModelData = matchedModels.map(({ model, registryId, score }) => ({
                modelId: this.getModelId(model),
                modelRegistryId: registryId,
                metadata: {
                    score,
                    originalModel: model
                }
            }));
            const bulkResult = await this.bulkModelOperations.bulkUpsertUserModels(bulkModelData, keyId);
            const savedModels = matchedModels.map(({ model, registryId }) => ({
                modelId: this.getModelId(model),
                modelRegistryId: registryId,
                isNew: bulkResult.modelIdMap.has(this.getModelId(model))
            }));
            return {
                totalModelsFound: matchedModels.length,
                modelsMatched: matchedModels.length,
                newModelsCreated: bulkResult.newModelsCreated,
                existingModelsFound: bulkResult.existingModelsFound,
                mappingsCreated: bulkResult.mappingsCreated,
                savedModels,
                errors: bulkResult.errors
            };
        }
        catch (error) {
            this.logger.error(`Bulk save user models failed: ${error.message}`, error.stack);
            return {
                totalModelsFound: matchedModels.length,
                modelsMatched: matchedModels.length,
                newModelsCreated: 0,
                existingModelsFound: 0,
                mappingsCreated: 0,
                savedModels: [],
                errors: [`Bulk save failed: ${error.message}`]
            };
        }
    }
};
exports.OptimizedModelDiscoveryService = OptimizedModelDiscoveryService;
exports.OptimizedModelDiscoveryService = OptimizedModelDiscoveryService = OptimizedModelDiscoveryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [ai_provider_helper_1.AiProviderHelper,
        model_registry_repository_1.ModelRegistryRepository,
        system_models_repository_1.SystemModelsRepository,
        user_models_repository_1.UserModelsRepository,
        system_model_key_llm_repository_1.SystemModelKeyLlmRepository,
        user_model_key_llm_repository_1.UserModelKeyLlmRepository,
        pattern_matching_engine_service_1.PatternMatchingEngineService,
        performance_monitor_service_1.PerformanceMonitorService,
        bulk_model_operations_service_1.BulkModelOperationsService])
], OptimizedModelDiscoveryService);
//# sourceMappingURL=optimized-model-discovery.service.js.map