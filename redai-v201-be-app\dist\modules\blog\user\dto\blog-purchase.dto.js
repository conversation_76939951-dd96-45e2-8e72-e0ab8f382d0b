"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginatedBlogPurchaseDto = exports.PurchaseStatusDto = exports.BlogPurchaseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const blog_dto_1 = require("./blog.dto");
class BlogPurchaseDto {
    id;
    user_id;
    blog_id;
    point;
    purchased_at;
    blog;
}
exports.BlogPurchaseDto = BlogPurchaseDto;
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID của giao dịch mua bài viết',
        example: 1,
    }),
    __metadata("design:type", Number)
], BlogPurchaseDto.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID của người mua',
        example: 10,
    }),
    __metadata("design:type", Number)
], BlogPurchaseDto.prototype, "user_id", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID của bài viết',
        example: 1,
    }),
    __metadata("design:type", Number)
], BlogPurchaseDto.prototype, "blog_id", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Số point thời điểm mua',
        example: 100,
    }),
    __metadata("design:type", Number)
], BlogPurchaseDto.prototype, "point", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian mua hàng (Unix timestamp)',
        example: 1632474086123,
    }),
    __metadata("design:type", Number)
], BlogPurchaseDto.prototype, "purchased_at", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => blog_dto_1.BlogDto),
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin bài viết',
        type: blog_dto_1.BlogDto,
    }),
    __metadata("design:type", blog_dto_1.BlogDto)
], BlogPurchaseDto.prototype, "blog", void 0);
class PurchaseStatusDto {
    purchased;
    purchased_at;
}
exports.PurchaseStatusDto = PurchaseStatusDto;
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Đã mua hay chưa',
        example: true,
    }),
    __metadata("design:type", Boolean)
], PurchaseStatusDto.prototype, "purchased", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian mua (Unix timestamp)',
        example: 1632474086123,
        nullable: true,
    }),
    __metadata("design:type", Object)
], PurchaseStatusDto.prototype, "purchased_at", void 0);
class PaginatedBlogPurchaseDto {
    content;
    totalItems;
    itemCount;
    itemsPerPage;
    totalPages;
    currentPage;
}
exports.PaginatedBlogPurchaseDto = PaginatedBlogPurchaseDto;
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách bài viết đã mua',
        type: [BlogPurchaseDto],
    }),
    __metadata("design:type", Array)
], PaginatedBlogPurchaseDto.prototype, "content", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Tổng số bài viết đã mua',
        example: 25,
    }),
    __metadata("design:type", Number)
], PaginatedBlogPurchaseDto.prototype, "totalItems", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Số lượng bài viết trên trang hiện tại',
        example: 10,
    }),
    __metadata("design:type", Number)
], PaginatedBlogPurchaseDto.prototype, "itemCount", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Số lượng bài viết trên mỗi trang',
        example: 10,
    }),
    __metadata("design:type", Number)
], PaginatedBlogPurchaseDto.prototype, "itemsPerPage", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Tổng số trang',
        example: 3,
    }),
    __metadata("design:type", Number)
], PaginatedBlogPurchaseDto.prototype, "totalPages", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Trang hiện tại',
        example: 1,
    }),
    __metadata("design:type", Number)
], PaginatedBlogPurchaseDto.prototype, "currentPage", void 0);
//# sourceMappingURL=blog-purchase.dto.js.map