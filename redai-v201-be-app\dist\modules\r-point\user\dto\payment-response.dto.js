"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentStatusResponseDto = exports.PaymentResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class PaymentResponseDto {
    transactionId;
    pointAmount;
    amount;
    originalAmount;
    qrCodeUrl;
    expiredAt;
    status;
}
exports.PaymentResponseDto = PaymentResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của giao dịch',
        example: '123e4567-e89b-12d3-a456-426614174000'
    }),
    __metadata("design:type", String)
], PaymentResponseDto.prototype, "transactionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số lượng R-Point',
        example: 100
    }),
    __metadata("design:type", Number)
], PaymentResponseDto.prototype, "pointAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số tiền cần thanh toán (VND)',
        example: 100000
    }),
    __metadata("design:type", Number)
], PaymentResponseDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số tiền gốc trước khi áp dụng khuyến mãi (VND)',
        example: 120000
    }),
    __metadata("design:type", Number)
], PaymentResponseDto.prototype, "originalAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mã QR code để thanh toán',
        example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...'
    }),
    __metadata("design:type", String)
], PaymentResponseDto.prototype, "qrCodeUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian hết hạn của giao dịch',
        example: '2023-08-15T10:30:00Z'
    }),
    __metadata("design:type", String)
], PaymentResponseDto.prototype, "expiredAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái giao dịch',
        example: 'PENDING',
        enum: ['PENDING', 'SUCCESS', 'FAILED', 'EXPIRED']
    }),
    __metadata("design:type", String)
], PaymentResponseDto.prototype, "status", void 0);
class PaymentStatusResponseDto {
    transactionId;
    pointAmount;
    amount;
    status;
    createdAt;
    updatedAt;
}
exports.PaymentStatusResponseDto = PaymentStatusResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của giao dịch',
        example: '123e4567-e89b-12d3-a456-426614174000'
    }),
    __metadata("design:type", String)
], PaymentStatusResponseDto.prototype, "transactionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số lượng R-Point',
        example: 100
    }),
    __metadata("design:type", Number)
], PaymentStatusResponseDto.prototype, "pointAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số tiền đã thanh toán (VND)',
        example: 100000
    }),
    __metadata("design:type", Number)
], PaymentStatusResponseDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái giao dịch',
        example: 'SUCCESS',
        enum: ['PENDING', 'SUCCESS', 'FAILED', 'EXPIRED']
    }),
    __metadata("design:type", String)
], PaymentStatusResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo giao dịch',
        example: '2023-08-15T10:00:00Z'
    }),
    __metadata("design:type", String)
], PaymentStatusResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian cập nhật giao dịch',
        example: '2023-08-15T10:15:00Z'
    }),
    __metadata("design:type", String)
], PaymentStatusResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=payment-response.dto.js.map