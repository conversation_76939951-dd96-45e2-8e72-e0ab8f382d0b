"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UserMessagesQueries_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserMessagesQueries = void 0;
const common_1 = require("@nestjs/common");
const database_service_1 = require("./database.service");
let UserMessagesQueries = UserMessagesQueries_1 = class UserMessagesQueries {
    databaseService;
    logger = new common_1.Logger(UserMessagesQueries_1.name);
    constructor(databaseService) {
        this.databaseService = databaseService;
    }
    async createMessage(messageData) {
        const query = `
      INSERT INTO user_messages (thread_id, role, content, timestamp, created_by)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING message_id
    `;
        const timestamp = Date.now();
        const values = [
            messageData.thread_id,
            messageData.role,
            JSON.stringify(messageData.content),
            timestamp,
            messageData.created_by
        ];
        try {
            this.logger.debug(`Creating message for thread ${messageData.thread_id}, role: ${messageData.role}`);
            const result = await this.databaseService.query(query, values);
            const messageId = result[0]?.message_id;
            if (!messageId) {
                throw new Error('Failed to create message - no ID returned');
            }
            this.logger.log(`Created message ${messageId} for thread ${messageData.thread_id}`);
            return messageId;
        }
        catch (error) {
            this.logger.error(`Failed to create message for thread ${messageData.thread_id}:`, error);
            throw new Error(`Message creation failed: ${error.message}`);
        }
    }
    async getMessagesByThreadId(threadId, limit) {
        let query = `
      SELECT message_id, thread_id, role, content, timestamp, created_by
      FROM user_messages
      WHERE thread_id = $1
      ORDER BY timestamp ASC
    `;
        const values = [threadId];
        if (limit) {
            query += ` LIMIT $2`;
            values.push(limit);
        }
        try {
            this.logger.debug(`Getting messages for thread ${threadId}${limit ? ` (limit: ${limit})` : ''}`);
            const result = await this.databaseService.query(query, values);
            const messages = result.map(row => ({
                message_id: row.message_id,
                thread_id: row.thread_id,
                role: row.role,
                content: row.content,
                timestamp: row.timestamp,
                created_by: row.created_by
            }));
            this.logger.debug(`Found ${messages.length} messages for thread ${threadId}`);
            return messages;
        }
        catch (error) {
            this.logger.error(`Failed to get messages for thread ${threadId}:`, error);
            throw new Error(`Failed to retrieve messages: ${error.message}`);
        }
    }
    async getMessageById(messageId) {
        const query = `
      SELECT message_id, thread_id, role, content, timestamp, created_by
      FROM user_messages
      WHERE message_id = $1
    `;
        try {
            this.logger.debug(`Getting message ${messageId}`);
            const result = await this.databaseService.query(query, [messageId]);
            if (result.length === 0) {
                this.logger.debug(`Message ${messageId} not found`);
                return null;
            }
            const row = result[0];
            return {
                message_id: row.message_id,
                thread_id: row.thread_id,
                role: row.role,
                content: row.content,
                timestamp: row.timestamp,
                created_by: row.created_by
            };
        }
        catch (error) {
            this.logger.error(`Failed to get message ${messageId}:`, error);
            throw new Error(`Failed to retrieve message: ${error.message}`);
        }
    }
    async getMessageCountByThreadId(threadId) {
        const query = `
      SELECT COUNT(*) as count
      FROM user_messages
      WHERE thread_id = $1
    `;
        try {
            const result = await this.databaseService.query(query, [threadId]);
            const count = parseInt(result[0]?.count || '0');
            this.logger.debug(`Thread ${threadId} has ${count} messages`);
            return count;
        }
        catch (error) {
            this.logger.error(`Failed to count messages for thread ${threadId}:`, error);
            throw new Error(`Message count failed: ${error.message}`);
        }
    }
    async deleteMessagesByThreadId(threadId) {
        const query = `
      DELETE FROM user_messages
      WHERE thread_id = $1
    `;
        try {
            this.logger.debug(`Deleting messages for thread ${threadId}`);
            await this.databaseService.query(query, [threadId]);
            this.logger.log(`Deleted messages for thread ${threadId}`);
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to delete messages for thread ${threadId}:`, error);
            throw new Error(`Message deletion failed: ${error.message}`);
        }
    }
};
exports.UserMessagesQueries = UserMessagesQueries;
exports.UserMessagesQueries = UserMessagesQueries = UserMessagesQueries_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.ChatDatabaseService])
], UserMessagesQueries);
//# sourceMappingURL=user-messages.queries.js.map