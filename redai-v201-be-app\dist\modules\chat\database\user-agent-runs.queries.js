"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserAgentRunsQueries = void 0;
const common_1 = require("@nestjs/common");
const enums_1 = require("../../../shared/enums");
const database_service_1 = require("./database.service");
let UserAgentRunsQueries = class UserAgentRunsQueries {
    databaseService;
    constructor(databaseService) {
        this.databaseService = databaseService;
    }
    async createRun(data) {
        const query = `
      INSERT INTO user_agent_runs (payload, status, created_by) 
      VALUES ($1, $2, $3) 
      RETURNING id
    `;
        const result = await this.databaseService.query(query, [
            JSON.stringify(data.payload),
            data.status || enums_1.UserAgentRunStatus.CREATED,
            data.created_by
        ]);
        return result[0].id;
    }
    async getRunById(id) {
        const query = `
      SELECT id, payload, status, created_at, created_by 
      FROM user_agent_runs 
      WHERE id = $1
    `;
        const result = await this.databaseService.query(query, [id]);
        if (result.length === 0) {
            return null;
        }
        return {
            id: result[0].id,
            payload: result[0].payload,
            status: result[0].status,
            created_at: result[0].created_at,
            created_by: result[0].created_by
        };
    }
    async updateRunStatus(id, status) {
        const query = `
      UPDATE user_agent_runs 
      SET status = $1 
      WHERE id = $2
    `;
        const result = await this.databaseService.query(query, [status, id]);
        return Array.isArray(result) && result.length >= 0;
    }
    async getRunsByStatus(status, limit = 100) {
        const query = `
      SELECT id, payload, status, created_at, created_by 
      FROM user_agent_runs 
      WHERE status = $1 
      ORDER BY created_at DESC 
      LIMIT $2
    `;
        const result = await this.databaseService.query(query, [status, limit]);
        return result.map((row) => ({
            id: row.id,
            payload: row.payload,
            status: row.status,
            created_at: row.created_at,
            created_by: row.created_by
        }));
    }
    async getRunsByUser(userId, limit = 100) {
        const query = `
      SELECT id, payload, status, created_at, created_by 
      FROM user_agent_runs 
      WHERE created_by = $1 
      ORDER BY created_at DESC 
      LIMIT $2
    `;
        const result = await this.databaseService.query(query, [userId, limit]);
        return result.map((row) => ({
            id: row.id,
            payload: row.payload,
            status: row.status,
            created_at: row.created_at,
            created_by: row.created_by
        }));
    }
};
exports.UserAgentRunsQueries = UserAgentRunsQueries;
exports.UserAgentRunsQueries = UserAgentRunsQueries = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.ChatDatabaseService])
], UserAgentRunsQueries);
//# sourceMappingURL=user-agent-runs.queries.js.map