"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PaymentUserService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentUserService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
const sepay_hub_1 = require("../../../../shared/services/sepay-hub");
const affiliate_service_1 = require("../../../affiliate/admin/services/affiliate.service");
const entities_1 = require("../../entities");
const system_configuration_1 = require("../../../system-configuration");
const service_1 = require("../../../user/user/service");
let PaymentUserService = PaymentUserService_1 = class PaymentUserService {
    transactionRepository;
    pointRepository;
    couponRepository;
    webhookLogRepository;
    configService;
    userService;
    affiliateService;
    sepayHubService;
    systemConfigurationService;
    logger = new common_1.Logger(PaymentUserService_1.name);
    webhookApiKey;
    transactionPrefix = 'RPOINT';
    transactionExpireMinutes = 15;
    constructor(transactionRepository, pointRepository, couponRepository, webhookLogRepository, configService, userService, affiliateService, sepayHubService, systemConfigurationService) {
        this.transactionRepository = transactionRepository;
        this.pointRepository = pointRepository;
        this.couponRepository = couponRepository;
        this.webhookLogRepository = webhookLogRepository;
        this.configService = configService;
        this.userService = userService;
        this.affiliateService = affiliateService;
        this.sepayHubService = sepayHubService;
        this.systemConfigurationService = systemConfigurationService;
    }
    async createPayment(userId, createPaymentDto) {
    }
};
exports.PaymentUserService = PaymentUserService;
exports.PaymentUserService = PaymentUserService = PaymentUserService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.PointPurchaseTransaction)),
    __param(1, (0, typeorm_1.InjectRepository)(entities_1.Point)),
    __param(2, (0, typeorm_1.InjectRepository)(entities_1.Coupon)),
    __param(3, (0, typeorm_1.InjectRepository)(entities_1.WebhookLog)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        config_1.ConfigService,
        service_1.UserService,
        affiliate_service_1.AffiliateService,
        sepay_hub_1.SepayHubService,
        system_configuration_1.SystemConfigurationService])
], PaymentUserService);
//# sourceMappingURL=payment-user.service.js.map