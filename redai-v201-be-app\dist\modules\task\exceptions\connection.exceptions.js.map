{"version": 3, "file": "connection.exceptions.js", "sourceRoot": "", "sources": ["../../../../src/modules/task/exceptions/connection.exceptions.ts"], "names": [], "mappings": ";;;AAAA,4CAAqC;AACrC,2CAA4C;AAM/B,QAAA,sBAAsB,GAAG;IAEpC,oBAAoB,EAAE,IAAI,kBAAS,CAAC,KAAK,EAAE,wBAAwB,EAAE,mBAAU,CAAC,SAAS,CAAC;IAC1F,0BAA0B,EAAE,IAAI,kBAAS,CAAC,KAAK,EAAE,sBAAsB,EAAE,mBAAU,CAAC,qBAAqB,CAAC;IAC1G,wBAAwB,EAAE,IAAI,kBAAS,CAAC,KAAK,EAAE,2BAA2B,EAAE,mBAAU,CAAC,qBAAqB,CAAC;IAC7G,wBAAwB,EAAE,IAAI,kBAAS,CAAC,KAAK,EAAE,sBAAsB,EAAE,mBAAU,CAAC,qBAAqB,CAAC;IACxG,uBAAuB,EAAE,IAAI,kBAAS,CAAC,KAAK,EAAE,gCAAgC,EAAE,mBAAU,CAAC,qBAAqB,CAAC;IAGjH,uBAAuB,EAAE,IAAI,kBAAS,CAAC,KAAK,EAAE,qCAAqC,EAAE,mBAAU,CAAC,SAAS,CAAC;IAG1G,uBAAuB,EAAE,IAAI,kBAAS,CAAC,KAAK,EAAE,8BAA8B,EAAE,mBAAU,CAAC,WAAW,CAAC;IACrG,oBAAoB,EAAE,IAAI,kBAAS,CAAC,KAAK,EAAE,yCAAyC,EAAE,mBAAU,CAAC,WAAW,CAAC;IAC7G,yBAAyB,EAAE,IAAI,kBAAS,CAAC,KAAK,EAAE,gCAAgC,EAAE,mBAAU,CAAC,WAAW,CAAC;IACzG,yBAAyB,EAAE,IAAI,kBAAS,CAAC,KAAK,EAAE,+CAA+C,EAAE,mBAAU,CAAC,WAAW,CAAC;IACxH,yBAAyB,EAAE,IAAI,kBAAS,CAAC,KAAK,EAAE,gCAAgC,EAAE,mBAAU,CAAC,SAAS,CAAC;IACvG,gCAAgC,EAAE,IAAI,kBAAS,CAAC,KAAK,EAAE,uCAAuC,EAAE,mBAAU,CAAC,WAAW,CAAC;IAGvH,6BAA6B,EAAE,IAAI,kBAAS,CAAC,KAAK,EAAE,8CAA8C,EAAE,mBAAU,CAAC,WAAW,CAAC;IAC3H,oBAAoB,EAAE,IAAI,kBAAS,CAAC,KAAK,EAAE,wBAAwB,EAAE,mBAAU,CAAC,WAAW,CAAC;IAC5F,6BAA6B,EAAE,IAAI,kBAAS,CAAC,KAAK,EAAE,uDAAuD,EAAE,mBAAU,CAAC,WAAW,CAAC;IAGpI,yBAAyB,EAAE,IAAI,kBAAS,CAAC,KAAK,EAAE,uCAAuC,EAAE,mBAAU,CAAC,WAAW,CAAC;CACjH,CAAC"}