"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TOOLS_BUILD_IN_API_KEY_ERROR_CODES = void 0;
const common_1 = require("@nestjs/common");
const exceptions_1 = require("../../../common/exceptions");
exports.TOOLS_BUILD_IN_API_KEY_ERROR_CODES = {
    API_KEY_MISSING: new exceptions_1.ErrorCode(30201, 'API Key không được cung cấp', common_1.HttpStatus.UNAUTHORIZED),
    API_KEY_INVALID: new exceptions_1.ErrorCode(30202, 'API Key không hợp lệ', common_1.HttpStatus.UNAUTHORIZED),
    API_KEY_EXPIRED: new exceptions_1.ErrorCode(30203, 'API Key đã hết hạn', common_1.HttpStatus.UNAUTHORIZED),
    API_KEY_FORBIDDEN: new exceptions_1.ErrorCode(30204, 'API Key không có quyền truy cập', common_1.HttpStatus.FORBIDDEN),
    AGENT_NOT_FOUND: new exceptions_1.ErrorCode(30205, 'Agent không tồn tại', common_1.HttpStatus.NOT_FOUND),
    AGENT_INACTIVE: new exceptions_1.ErrorCode(30206, 'Agent không hoạt động', common_1.HttpStatus.FORBIDDEN),
};
//# sourceMappingURL=tools-build-in-api-key.exception.js.map