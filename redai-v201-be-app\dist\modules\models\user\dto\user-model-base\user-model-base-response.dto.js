"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserModelBaseResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const type_provider_util_1 = require("../../../../../shared/services/ai/utils/type-provider.util");
let UserModelBaseResponseDto = class UserModelBaseResponseDto {
    id;
    name;
    modelId;
    provider;
    description;
    isUserAccessible;
    isFineTunable;
    inputCostPer1kTokens;
    outputCostPer1kTokens;
    contextLength;
    capabilities;
    source;
    userKeyInfo;
    metadata;
    createdAt;
    updatedAt;
    availability;
    usageStats;
};
exports.UserModelBaseResponseDto = UserModelBaseResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'UUID của model base',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], UserModelBaseResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên hiển thị của model',
        example: 'GPT-4 Turbo',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], UserModelBaseResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Model ID từ nhà cung cấp',
        example: 'gpt-4-turbo-preview',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], UserModelBaseResponseDto.prototype, "modelId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nhà cung cấp model',
        enum: type_provider_util_1.ProviderEnumq,
        example: type_provider_util_1.ProviderEnumq.OPENAI,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], UserModelBaseResponseDto.prototype, "provider", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả về model',
        example: 'GPT-4 Turbo with improved instruction following',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], UserModelBaseResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User có thể access model này không',
        example: true,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], UserModelBaseResponseDto.prototype, "isUserAccessible", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Model có thể fine-tune không',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], UserModelBaseResponseDto.prototype, "isFineTunable", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Chi phí input per 1k tokens (USD)',
        example: 0.01,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], UserModelBaseResponseDto.prototype, "inputCostPer1kTokens", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Chi phí output per 1k tokens (USD)',
        example: 0.03,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], UserModelBaseResponseDto.prototype, "outputCostPer1kTokens", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Độ dài context tối đa',
        example: 128000,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], UserModelBaseResponseDto.prototype, "contextLength", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Capabilities của model',
        example: ['text-generation', 'function-calling'],
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Array)
], UserModelBaseResponseDto.prototype, "capabilities", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Source của model (admin hoặc user-key)',
        enum: ['admin', 'user-key'],
        example: 'admin',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], UserModelBaseResponseDto.prototype, "source", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin user key (nếu source là user-key)',
        example: {
            keyId: '123e4567-e89b-12d3-a456-************',
            keyName: 'My OpenAI Key',
            isDefault: true
        },
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], UserModelBaseResponseDto.prototype, "userKeyInfo", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Metadata bổ sung',
        example: {
            version: '2024-01',
            trainingData: 'Up to April 2023',
            multimodal: false
        },
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], UserModelBaseResponseDto.prototype, "metadata", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo (epoch millis)',
        example: 1640995200000,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], UserModelBaseResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian cập nhật (epoch millis)',
        example: 1640995200000,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], UserModelBaseResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Availability status',
        example: {
            isAvailable: true,
            lastChecked: 1640995200000,
            responseTime: 250
        },
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], UserModelBaseResponseDto.prototype, "availability", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Usage statistics (nếu có)',
        example: {
            totalRequests: 1500,
            totalTokens: 2500000,
            avgResponseTime: 1200
        },
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], UserModelBaseResponseDto.prototype, "usageStats", void 0);
exports.UserModelBaseResponseDto = UserModelBaseResponseDto = __decorate([
    (0, class_transformer_1.Exclude)()
], UserModelBaseResponseDto);
//# sourceMappingURL=user-model-base-response.dto.js.map