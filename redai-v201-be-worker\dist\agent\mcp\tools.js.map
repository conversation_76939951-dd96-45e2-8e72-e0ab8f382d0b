{"version": 3, "sources": ["../../../src/agent/mcp/tools.ts"], "sourcesContent": ["import { Client } from '@modelcontextprotocol/sdk/client/index.js';\r\nimport type {\r\n  CallToolResult,\r\n  EmbeddedResource,\r\n  ImageContent,\r\n  ReadResourceResult,\r\n  TextContent,\r\n  Tool as MCPTool,\r\n} from '@modelcontextprotocol/sdk/types.js';\r\nimport {\r\n  DynamicStructuredTool,\r\n  type DynamicStructuredToolInput,\r\n  type StructuredToolInterface,\r\n} from '@langchain/core/tools';\r\nimport {\r\n  MessageContent,\r\n  MessageContentComplex,\r\n  MessageContentImageUrl,\r\n  MessageContentText,\r\n} from '@langchain/core/messages';\r\nimport debug from 'debug';\r\n\r\nconst mcpBuildInToolName = 'execute_tool_buildin';\r\nconst mcpIntegrationToolName = 'execute_tool_integration';\r\n// Replace direct initialization with lazy initialization\r\nlet debugLog: debug.Debugger;\r\n\r\nfunction getDebugLog() {\r\n  if (!debugLog) {\r\n    debugLog = debug('@langchain/mcp-adapters:tools');\r\n  }\r\n  return debugLog;\r\n}\r\n\r\nexport type CallToolResultContentType =\r\n  CallToolResult['content'][number]['type'];\r\nexport type CallToolResultContent =\r\n  | TextContent\r\n  | ImageContent\r\n  | EmbeddedResource;\r\n\r\nasync function _embeddedResourceToArtifact(\r\n  resource: EmbeddedResource,\r\n  client: Client,\r\n): Promise<EmbeddedResource[]> {\r\n  if (!resource.blob && !resource.text && resource.uri) {\r\n    const response: ReadResourceResult = await client.readResource({\r\n      uri: resource.resource.uri,\r\n    });\r\n\r\n    return response.contents.map(\r\n      (content: ReadResourceResult['contents'][number]) => ({\r\n        type: 'resource',\r\n        resource: {\r\n          ...content,\r\n        },\r\n      }),\r\n    );\r\n  }\r\n  return [resource];\r\n}\r\n\r\n/**\r\n * Custom error class for tool exceptions\r\n */\r\nexport class ToolException extends Error {\r\n  constructor(message: string) {\r\n    super(message);\r\n    this.name = 'ToolException';\r\n  }\r\n}\r\n\r\n/**\r\n * Process the result from calling an MCP tool.\r\n * Extracts text content and non-text content for better worker compatibility.\r\n *\r\n * @param serverName\r\n * @param toolName\r\n * @param result - The result from the MCP tool call\r\n * @param client\r\n * @returns A tuple of [textContent, nonTextContent]\r\n */\r\nasync function _convertCallToolResult(\r\n  serverName: string,\r\n  toolName: string,\r\n  result: CallToolResult,\r\n  client: Client,\r\n): Promise<[MessageContent, EmbeddedResource[]]> {\r\n  if (!result) {\r\n    throw new ToolException(\r\n      `MCP tool '${toolName}' on server '${serverName}' returned an invalid result - tool call response was undefined`,\r\n    );\r\n  }\r\n\r\n  if (!Array.isArray(result.content)) {\r\n    throw new ToolException(\r\n      `MCP tool '${toolName}' on server '${serverName}' returned an invalid result - expected an array of content, but was ${typeof result.content}`,\r\n    );\r\n  }\r\n\r\n  if (result.isError) {\r\n    throw new ToolException(\r\n      `MCP tool '${toolName}' on server '${serverName}' returned an error: ${result.content\r\n        .map((content) => content.text)\r\n        .join('\\n')}`,\r\n    );\r\n  }\r\n\r\n  const mcpTextAndImageContent: MessageContentComplex[] = (\r\n    result.content.filter(\r\n      (content) => content.type === 'text' || content.type === 'image',\r\n    ) as (TextContent | ImageContent)[]\r\n  ).map((content: TextContent | ImageContent) => {\r\n    switch (content.type) {\r\n      case 'text':\r\n        return {\r\n          type: 'text',\r\n          text: content.text,\r\n        } as MessageContentText;\r\n      case 'image':\r\n        return {\r\n          type: 'image_url',\r\n          image_url: {\r\n            url: `data:${content.mimeType};base64,${content.data}`,\r\n          },\r\n        } as MessageContentImageUrl;\r\n      default:\r\n        throw new ToolException(\r\n          `MCP tool '${toolName}' on server '${serverName}' returned an invalid result - expected a text or image content, but was ${\r\n            (content as any).type\r\n          }`,\r\n        );\r\n    }\r\n  });\r\n\r\n  // Create the text content output\r\n  const artifacts = (\r\n    await Promise.all(\r\n      (\r\n        result.content.filter(\r\n          (content) => content.type === 'resource',\r\n        ) as EmbeddedResource[]\r\n      ).map((content: EmbeddedResource) =>\r\n        _embeddedResourceToArtifact(content, client),\r\n      ),\r\n    )\r\n  ).flat();\r\n\r\n  if (\r\n    mcpTextAndImageContent.length === 1 &&\r\n    mcpTextAndImageContent[0].type === 'text'\r\n  ) {\r\n    return [mcpTextAndImageContent[0].text, artifacts];\r\n  }\r\n\r\n  return [mcpTextAndImageContent, artifacts];\r\n}\r\n\r\nexport type LoadMcpToolsOptions = {\r\n  /**\r\n   * If true, throw an error if a tool fails to load.\r\n   *\r\n   * @default true\r\n   */\r\n  throwOnLoadError?: boolean;\r\n\r\n  /**\r\n   * If true, the tool name will be prefixed with the server name followed by a double underscore.\r\n   * This is useful if you want to avoid tool name collisions across servers.\r\n   *\r\n   * @default false\r\n   */\r\n  prefixToolNameWithServerName?: boolean;\r\n\r\n  /**\r\n   * An additional prefix to add to the tool name. Will be added at the very beginning of the tool\r\n   * name, separated by a double underscore.\r\n   *\r\n   * For example, if `additionalToolNamePrefix` is `\"mcp\"`, and `prefixToolNameWithServerName` is\r\n   * `true`, the tool name `\"my-tool\"` provided by server `\"my-server\"` will become\r\n   * `\"mcp__my-server__my-tool\"`.\r\n   *\r\n   * Similarly, if `additionalToolNamePrefix` is `mcp` and `prefixToolNameWithServerName` is false,\r\n   * the tool name would be `\"mcp__my-tool\"`.\r\n   *\r\n   * @default \"\"\r\n   */\r\n  additionalToolNamePrefix?: string;\r\n\r\n  apiKey?: string;\r\n};\r\n\r\n/**\r\n * Call an MCP tool.\r\n *\r\n * Use this with `.bind` to capture the fist three arguments, then pass to the constructor of DynamicStructuredTool.\r\n *\r\n * @internal\r\n *\r\n * @param serverName\r\n * @param client - The MCP client\r\n * @param toolName - The name of the tool (forwarded to the client)\r\n * @param args - The arguments to pass to the tool\r\n * @param extra\r\n * @returns A tuple of [textContent, nonTextContent]\r\n */\r\nasync function _callTool(\r\n  serverName: string,\r\n  toolName: string,\r\n  client: Client,\r\n  extra: Record<string, unknown>,\r\n  args: Record<string, unknown>,\r\n): Promise<[MessageContent, EmbeddedResource[]]> {\r\n  let result: CallToolResult;\r\n  try {\r\n    getDebugLog()(`INFO: Calling tool ${toolName}(${JSON.stringify(args)})`);\r\n    result = (await client.callTool({\r\n      name: toolName,\r\n      arguments: args,\r\n      extra,\r\n    })) as CallToolResult;\r\n  } catch (error) {\r\n    getDebugLog()(`Error calling tool ${toolName}: ${String(error)}`);\r\n    // eslint-disable-next-line no-instanceof/no-instanceof\r\n    if (error instanceof ToolException) {\r\n      throw error;\r\n    }\r\n    throw new ToolException(`Error calling tool ${toolName}: ${String(error)}`);\r\n  }\r\n\r\n  return _convertCallToolResult(serverName, toolName, result, client);\r\n}\r\n\r\nconst defaultLoadMcpToolsOptions: LoadMcpToolsOptions = {\r\n  throwOnLoadError: true,\r\n  prefixToolNameWithServerName: false,\r\n  additionalToolNamePrefix: '',\r\n};\r\n\r\n/**\r\n * Load all tools from an MCP client.\r\n *\r\n * @param serverName - The name of the server to load tools from\r\n * @param client - The MCP client\r\n * @param options\r\n * @returns A list of LangChain tools\r\n */\r\nexport async function loadMcpTools(\r\n  serverName: string,\r\n  client: Client,\r\n  options?: LoadMcpToolsOptions,\r\n): Promise<StructuredToolInterface[]> {\r\n  const {\r\n    throwOnLoadError,\r\n    prefixToolNameWithServerName,\r\n    additionalToolNamePrefix,\r\n  } = {\r\n    ...defaultLoadMcpToolsOptions,\r\n    ...(options ?? {}),\r\n  };\r\n  if (!options?.apiKey) {\r\n    throw new Error('API key is required');\r\n  }\r\n  // Get tools in a single operation\r\n  const { resources } = await client.listResources();\r\n  const resourceURI = resources[0].uri;\r\n  const toolsResponseRaw = await client.readResource({\r\n    uri: resourceURI,\r\n    headers: {\r\n      'x-api-key': options.apiKey,\r\n    },\r\n  });\r\n  const toolsResponse = JSON.parse(\r\n    toolsResponseRaw?.contents[0].text as string,\r\n  )['result']['tools'];\r\n  getDebugLog()(`INFO: Found ${toolsResponse.tools?.length || 0} MCP tools`);\r\n\r\n  const initialPrefix = additionalToolNamePrefix\r\n    ? `${additionalToolNamePrefix}__`\r\n    : '';\r\n  const serverPrefix = prefixToolNameWithServerName ? `${serverName}__` : '';\r\n  const toolNamePrefix = `${initialPrefix}${serverPrefix}`;\r\n\r\n  // Filter out tools without names and convert in a single map operation\r\n  return (\r\n    await Promise.all(\r\n      (toolsResponse.tools || [])\r\n        .filter((tool: MCPTool) => !!tool.name)\r\n        .map(async (tool: MCPTool) => {\r\n          try {\r\n            const dst = new DynamicStructuredTool({\r\n              name: `${toolNamePrefix}${tool.name}`,\r\n              description: tool.description || '',\r\n              schema: tool.inputSchema,\r\n              responseFormat: 'content_and_artifact',\r\n              func: _callTool.bind(\r\n                null,\r\n                serverName,\r\n                tool['toolType'] === 'IN_SYSTEM'\r\n                  ? mcpBuildInToolName\r\n                  : mcpIntegrationToolName,\r\n                client,\r\n                tool['extra'],\r\n              ) as DynamicStructuredToolInput['func'],\r\n            });\r\n            getDebugLog()(`INFO: Successfully loaded tool: ${dst.name}`);\r\n            return dst;\r\n          } catch (error) {\r\n            getDebugLog()(`ERROR: Failed to load tool \"${tool.name}\":`, error);\r\n            if (throwOnLoadError) {\r\n              throw error;\r\n            }\r\n            return null;\r\n          }\r\n        }),\r\n    )\r\n  ).filter(Boolean) as StructuredToolInterface[];\r\n}\r\n"], "names": ["ToolException", "loadMcpTools", "mcpBuildInToolName", "mcpIntegrationToolName", "debugLog", "getDebugLog", "debug", "_embeddedResourceToArtifact", "resource", "client", "blob", "text", "uri", "response", "readResource", "contents", "map", "content", "type", "Error", "constructor", "message", "name", "_convertCallToolResult", "serverName", "toolName", "result", "Array", "isArray", "isError", "join", "mcpTextAndImageContent", "filter", "image_url", "url", "mimeType", "data", "artifacts", "Promise", "all", "flat", "length", "_callTool", "extra", "args", "JSON", "stringify", "callTool", "arguments", "error", "String", "defaultLoadMcpToolsOptions", "throwOnLoadError", "prefixToolNameWithServerName", "additionalToolNamePrefix", "options", "<PERSON><PERSON><PERSON><PERSON>", "resources", "listResources", "resourceURI", "toolsResponseRaw", "headers", "toolsResponse", "parse", "tools", "initialPrefix", "serverPrefix", "toolNamePrefix", "tool", "dst", "DynamicStructuredTool", "description", "schema", "inputSchema", "responseFormat", "func", "bind", "Boolean"], "mappings": ";;;;;;;;;;;QAiEaA;eAAAA;;QAsLSC;eAAAA;;;uBA1Of;8DAOW;;;;;;AAElB,MAAMC,qBAAqB;AAC3B,MAAMC,yBAAyB;AAC/B,yDAAyD;AACzD,IAAIC;AAEJ,SAASC;IACP,IAAI,CAACD,UAAU;QACbA,WAAWE,IAAAA,cAAK,EAAC;IACnB;IACA,OAAOF;AACT;AASA,eAAeG,4BACbC,QAA0B,EAC1BC,MAAc;IAEd,IAAI,CAACD,SAASE,IAAI,IAAI,CAACF,SAASG,IAAI,IAAIH,SAASI,GAAG,EAAE;QACpD,MAAMC,WAA+B,MAAMJ,OAAOK,YAAY,CAAC;YAC7DF,KAAKJ,SAASA,QAAQ,CAACI,GAAG;QAC5B;QAEA,OAAOC,SAASE,QAAQ,CAACC,GAAG,CAC1B,CAACC,UAAqD,CAAA;gBACpDC,MAAM;gBACNV,UAAU;oBACR,GAAGS,OAAO;gBACZ;YACF,CAAA;IAEJ;IACA,OAAO;QAACT;KAAS;AACnB;AAKO,IAAA,AAAMR,gBAAN,MAAMA,sBAAsBmB;IACjCC,YAAYC,OAAe,CAAE;QAC3B,KAAK,CAACA;QACN,IAAI,CAACC,IAAI,GAAG;IACd;AACF;AAEA;;;;;;;;;CASC,GACD,eAAeC,uBACbC,UAAkB,EAClBC,QAAgB,EAChBC,MAAsB,EACtBjB,MAAc;IAEd,IAAI,CAACiB,QAAQ;QACX,MAAM,IAAI1B,cACR,CAAC,UAAU,EAAEyB,SAAS,aAAa,EAAED,WAAW,+DAA+D,CAAC;IAEpH;IAEA,IAAI,CAACG,MAAMC,OAAO,CAACF,OAAOT,OAAO,GAAG;QAClC,MAAM,IAAIjB,cACR,CAAC,UAAU,EAAEyB,SAAS,aAAa,EAAED,WAAW,qEAAqE,EAAE,OAAOE,OAAOT,OAAO,EAAE;IAElJ;IAEA,IAAIS,OAAOG,OAAO,EAAE;QAClB,MAAM,IAAI7B,cACR,CAAC,UAAU,EAAEyB,SAAS,aAAa,EAAED,WAAW,qBAAqB,EAAEE,OAAOT,OAAO,CAClFD,GAAG,CAAC,CAACC,UAAYA,QAAQN,IAAI,EAC7BmB,IAAI,CAAC,OAAO;IAEnB;IAEA,MAAMC,yBAAkD,AACtDL,OAAOT,OAAO,CAACe,MAAM,CACnB,CAACf,UAAYA,QAAQC,IAAI,KAAK,UAAUD,QAAQC,IAAI,KAAK,SAE3DF,GAAG,CAAC,CAACC;QACL,OAAQA,QAAQC,IAAI;YAClB,KAAK;gBACH,OAAO;oBACLA,MAAM;oBACNP,MAAMM,QAAQN,IAAI;gBACpB;YACF,KAAK;gBACH,OAAO;oBACLO,MAAM;oBACNe,WAAW;wBACTC,KAAK,CAAC,KAAK,EAAEjB,QAAQkB,QAAQ,CAAC,QAAQ,EAAElB,QAAQmB,IAAI,EAAE;oBACxD;gBACF;YACF;gBACE,MAAM,IAAIpC,cACR,CAAC,UAAU,EAAEyB,SAAS,aAAa,EAAED,WAAW,yEAAyE,EACvH,AAACP,QAAgBC,IAAI,EACrB;QAER;IACF;IAEA,iCAAiC;IACjC,MAAMmB,YAAY,AAChB,CAAA,MAAMC,QAAQC,GAAG,CACf,AACEb,OAAOT,OAAO,CAACe,MAAM,CACnB,CAACf,UAAYA,QAAQC,IAAI,KAAK,YAEhCF,GAAG,CAAC,CAACC,UACLV,4BAA4BU,SAASR,SAEzC,EACA+B,IAAI;IAEN,IACET,uBAAuBU,MAAM,KAAK,KAClCV,sBAAsB,CAAC,EAAE,CAACb,IAAI,KAAK,QACnC;QACA,OAAO;YAACa,sBAAsB,CAAC,EAAE,CAACpB,IAAI;YAAE0B;SAAU;IACpD;IAEA,OAAO;QAACN;QAAwBM;KAAU;AAC5C;AAoCA;;;;;;;;;;;;;CAaC,GACD,eAAeK,UACblB,UAAkB,EAClBC,QAAgB,EAChBhB,MAAc,EACdkC,KAA8B,EAC9BC,IAA6B;IAE7B,IAAIlB;IACJ,IAAI;QACFrB,cAAc,CAAC,mBAAmB,EAAEoB,SAAS,CAAC,EAAEoB,KAAKC,SAAS,CAACF,MAAM,CAAC,CAAC;QACvElB,SAAU,MAAMjB,OAAOsC,QAAQ,CAAC;YAC9BzB,MAAMG;YACNuB,WAAWJ;YACXD;QACF;IACF,EAAE,OAAOM,OAAO;QACd5C,cAAc,CAAC,mBAAmB,EAAEoB,SAAS,EAAE,EAAEyB,OAAOD,QAAQ;QAChE,uDAAuD;QACvD,IAAIA,iBAAiBjD,eAAe;YAClC,MAAMiD;QACR;QACA,MAAM,IAAIjD,cAAc,CAAC,mBAAmB,EAAEyB,SAAS,EAAE,EAAEyB,OAAOD,QAAQ;IAC5E;IAEA,OAAO1B,uBAAuBC,YAAYC,UAAUC,QAAQjB;AAC9D;AAEA,MAAM0C,6BAAkD;IACtDC,kBAAkB;IAClBC,8BAA8B;IAC9BC,0BAA0B;AAC5B;AAUO,eAAerD,aACpBuB,UAAkB,EAClBf,MAAc,EACd8C,OAA6B;IAE7B,MAAM,EACJH,gBAAgB,EAChBC,4BAA4B,EAC5BC,wBAAwB,EACzB,GAAG;QACF,GAAGH,0BAA0B;QAC7B,GAAII,WAAW,CAAC,CAAC;IACnB;IACA,IAAI,CAACA,SAASC,QAAQ;QACpB,MAAM,IAAIrC,MAAM;IAClB;IACA,kCAAkC;IAClC,MAAM,EAAEsC,SAAS,EAAE,GAAG,MAAMhD,OAAOiD,aAAa;IAChD,MAAMC,cAAcF,SAAS,CAAC,EAAE,CAAC7C,GAAG;IACpC,MAAMgD,mBAAmB,MAAMnD,OAAOK,YAAY,CAAC;QACjDF,KAAK+C;QACLE,SAAS;YACP,aAAaN,QAAQC,MAAM;QAC7B;IACF;IACA,MAAMM,gBAAgBjB,KAAKkB,KAAK,CAC9BH,kBAAkB7C,QAAQ,CAAC,EAAE,CAACJ,KAC/B,CAAC,SAAS,CAAC,QAAQ;IACpBN,cAAc,CAAC,YAAY,EAAEyD,cAAcE,KAAK,EAAEvB,UAAU,EAAE,UAAU,CAAC;IAEzE,MAAMwB,gBAAgBX,2BAClB,GAAGA,yBAAyB,EAAE,CAAC,GAC/B;IACJ,MAAMY,eAAeb,+BAA+B,GAAG7B,WAAW,EAAE,CAAC,GAAG;IACxE,MAAM2C,iBAAiB,GAAGF,gBAAgBC,cAAc;IAExD,uEAAuE;IACvE,OAAO,AACL,CAAA,MAAM5B,QAAQC,GAAG,CACf,AAACuB,CAAAA,cAAcE,KAAK,IAAI,EAAE,AAAD,EACtBhC,MAAM,CAAC,CAACoC,OAAkB,CAAC,CAACA,KAAK9C,IAAI,EACrCN,GAAG,CAAC,OAAOoD;QACV,IAAI;YACF,MAAMC,MAAM,IAAIC,4BAAqB,CAAC;gBACpChD,MAAM,GAAG6C,iBAAiBC,KAAK9C,IAAI,EAAE;gBACrCiD,aAAaH,KAAKG,WAAW,IAAI;gBACjCC,QAAQJ,KAAKK,WAAW;gBACxBC,gBAAgB;gBAChBC,MAAMjC,UAAUkC,IAAI,CAClB,MACApD,YACA4C,IAAI,CAAC,WAAW,KAAK,cACjBlE,qBACAC,wBACJM,QACA2D,IAAI,CAAC,QAAQ;YAEjB;YACA/D,cAAc,CAAC,gCAAgC,EAAEgE,IAAI/C,IAAI,EAAE;YAC3D,OAAO+C;QACT,EAAE,OAAOpB,OAAO;YACd5C,cAAc,CAAC,4BAA4B,EAAE+D,KAAK9C,IAAI,CAAC,EAAE,CAAC,EAAE2B;YAC5D,IAAIG,kBAAkB;gBACpB,MAAMH;YACR;YACA,OAAO;QACT;IACF,GACJ,EACAjB,MAAM,CAAC6C;AACX"}