{"version": 3, "file": "error-codes.js", "sourceRoot": "", "sources": ["../../../../src/modules/integration/constants/error-codes.ts"], "names": [], "mappings": ";;;AAGa,QAAA,uBAAuB,GAAG;IAErC,YAAY,EAAE,cAAc;IAC5B,iBAAiB,EAAE,mBAAmB;IACtC,iBAAiB,EAAE,mBAAmB;IAGtC,kBAAkB,EAAE,oBAAoB;IACxC,oBAAoB,EAAE,sBAAsB;IAC5C,uBAAuB,EAAE,yBAAyB;IAGlD,sBAAsB,EAAE,wBAAwB;IAChD,mBAAmB,EAAE,qBAAqB;IAC1C,oBAAoB,EAAE,sBAAsB;IAC5C,aAAa,EAAE,eAAe;IAC9B,aAAa,EAAE,eAAe;IAG9B,iBAAiB,EAAE,mBAAmB;IACtC,kBAAkB,EAAE,oBAAoB;IACxC,aAAa,EAAE,eAAe;IAG9B,kBAAkB,EAAE,oBAAoB;IACxC,yBAAyB,EAAE,2BAA2B;IACtD,cAAc,EAAE,gBAAgB;IAGhC,uBAAuB,EAAE,yBAAyB;IAClD,sBAAsB,EAAE,wBAAwB;IAChD,mBAAmB,EAAE,qBAAqB;IAC1C,iBAAiB,EAAE,mBAAmB;IAGtC,mBAAmB,EAAE,qBAAqB;IAC1C,kBAAkB,EAAE,oBAAoB;IACxC,kBAAkB,EAAE,oBAAoB;IACxC,YAAY,EAAE,cAAc;IAG5B,uBAAuB,EAAE,yBAAyB;IAClD,2BAA2B,EAAE,6BAA6B;IAC1D,2BAA2B,EAAE,6BAA6B;IAC1D,2BAA2B,EAAE,6BAA6B;CAClD,CAAC;AAKE,QAAA,0BAA0B,GAAG;IACxC,CAAC,+BAAuB,CAAC,YAAY,CAAC,EAAE,sBAAsB;IAC9D,CAAC,+BAAuB,CAAC,iBAAiB,CAAC,EAAE,oBAAoB;IACjE,CAAC,+BAAuB,CAAC,iBAAiB,CAAC,EAAE,qBAAqB;IAElE,CAAC,+BAAuB,CAAC,kBAAkB,CAAC,EAAE,6BAA6B;IAC3E,CAAC,+BAAuB,CAAC,oBAAoB,CAAC,EAAE,gCAAgC;IAChF,CAAC,+BAAuB,CAAC,uBAAuB,CAAC,EAAE,oCAAoC;IAEvF,CAAC,+BAAuB,CAAC,sBAAsB,CAAC,EAAE,2BAA2B;IAC7E,CAAC,+BAAuB,CAAC,mBAAmB,CAAC,EAAE,iCAAiC;IAChF,CAAC,+BAAuB,CAAC,oBAAoB,CAAC,EAAE,6BAA6B;IAC7E,CAAC,+BAAuB,CAAC,aAAa,CAAC,EAAE,2BAA2B;IACpE,CAAC,+BAAuB,CAAC,aAAa,CAAC,EAAE,kBAAkB;IAG3D,CAAC,+BAAuB,CAAC,iBAAiB,CAAC,EAAE,wBAAwB;IACrE,CAAC,+BAAuB,CAAC,kBAAkB,CAAC,EAAE,4CAA4C;IAC1F,CAAC,+BAAuB,CAAC,aAAa,CAAC,EAAE,aAAa;IAGtD,CAAC,+BAAuB,CAAC,kBAAkB,CAAC,EAAE,yBAAyB;IACvE,CAAC,+BAAuB,CAAC,yBAAyB,CAAC,EAAE,8BAA8B;IACnF,CAAC,+BAAuB,CAAC,cAAc,CAAC,EAAE,cAAc;IAGxD,CAAC,+BAAuB,CAAC,uBAAuB,CAAC,EAAE,8BAA8B;IACjF,CAAC,+BAAuB,CAAC,sBAAsB,CAAC,EAAE,oCAAoC;IACtF,CAAC,+BAAuB,CAAC,mBAAmB,CAAC,EAAE,2BAA2B;IAC1E,CAAC,+BAAuB,CAAC,iBAAiB,CAAC,EAAE,iBAAiB;IAG9D,CAAC,+BAAuB,CAAC,mBAAmB,CAAC,EAAE,2BAA2B;IAC1E,CAAC,+BAAuB,CAAC,kBAAkB,CAAC,EAAE,0BAA0B;IACxE,CAAC,+BAAuB,CAAC,kBAAkB,CAAC,EAAE,gBAAgB;IAC9D,CAAC,+BAAuB,CAAC,YAAY,CAAC,EAAE,aAAa;IAGrD,CAAC,+BAAuB,CAAC,uBAAuB,CAAC,EAAE,sCAAsC;IACzF,CAAC,+BAAuB,CAAC,2BAA2B,CAAC,EAAE,oCAAoC;IAC3F,CAAC,+BAAuB,CAAC,2BAA2B,CAAC,EAAE,yCAAyC;IAChG,CAAC,+BAAuB,CAAC,2BAA2B,CAAC,EAAE,oCAAoC;CACnF,CAAC;AAKE,QAAA,6BAA6B,GAAG;IAC3C,CAAC,+BAAuB,CAAC,YAAY,CAAC,EAAE,GAAG;IAC3C,CAAC,+BAAuB,CAAC,iBAAiB,CAAC,EAAE,GAAG;IAChD,CAAC,+BAAuB,CAAC,iBAAiB,CAAC,EAAE,GAAG;IAEhD,CAAC,+BAAuB,CAAC,kBAAkB,CAAC,EAAE,GAAG;IACjD,CAAC,+BAAuB,CAAC,oBAAoB,CAAC,EAAE,GAAG;IACnD,CAAC,+BAAuB,CAAC,uBAAuB,CAAC,EAAE,GAAG;IAEtD,CAAC,+BAAuB,CAAC,sBAAsB,CAAC,EAAE,GAAG;IACrD,CAAC,+BAAuB,CAAC,mBAAmB,CAAC,EAAE,GAAG;IAClD,CAAC,+BAAuB,CAAC,oBAAoB,CAAC,EAAE,GAAG;IACnD,CAAC,+BAAuB,CAAC,aAAa,CAAC,EAAE,GAAG;IAC5C,CAAC,+BAAuB,CAAC,aAAa,CAAC,EAAE,GAAG;IAG5C,CAAC,+BAAuB,CAAC,iBAAiB,CAAC,EAAE,GAAG;IAChD,CAAC,+BAAuB,CAAC,kBAAkB,CAAC,EAAE,GAAG;IACjD,CAAC,+BAAuB,CAAC,aAAa,CAAC,EAAE,GAAG;IAG5C,CAAC,+BAAuB,CAAC,kBAAkB,CAAC,EAAE,GAAG;IACjD,CAAC,+BAAuB,CAAC,yBAAyB,CAAC,EAAE,GAAG;IACxD,CAAC,+BAAuB,CAAC,cAAc,CAAC,EAAE,GAAG;IAG7C,CAAC,+BAAuB,CAAC,uBAAuB,CAAC,EAAE,GAAG;IACtD,CAAC,+BAAuB,CAAC,sBAAsB,CAAC,EAAE,GAAG;IACrD,CAAC,+BAAuB,CAAC,mBAAmB,CAAC,EAAE,GAAG;IAClD,CAAC,+BAAuB,CAAC,iBAAiB,CAAC,EAAE,GAAG;IAGhD,CAAC,+BAAuB,CAAC,mBAAmB,CAAC,EAAE,GAAG;IAClD,CAAC,+BAAuB,CAAC,kBAAkB,CAAC,EAAE,GAAG;IACjD,CAAC,+BAAuB,CAAC,kBAAkB,CAAC,EAAE,GAAG;IACjD,CAAC,+BAAuB,CAAC,YAAY,CAAC,EAAE,GAAG;IAG3C,CAAC,+BAAuB,CAAC,uBAAuB,CAAC,EAAE,GAAG;IACtD,CAAC,+BAAuB,CAAC,2BAA2B,CAAC,EAAE,GAAG;IAC1D,CAAC,+BAAuB,CAAC,2BAA2B,CAAC,EAAE,GAAG;IAC1D,CAAC,+BAAuB,CAAC,2BAA2B,CAAC,EAAE,GAAG;CAClD,CAAC"}