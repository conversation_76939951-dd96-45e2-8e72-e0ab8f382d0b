"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnowledgeFileRepository = void 0;
class KnowledgeFileRepository {
    findOne = jest.fn().mockResolvedValue(null);
    find = jest.fn().mockResolvedValue([]);
    create = jest.fn().mockImplementation(entity => entity);
    save = jest.fn().mockImplementation(entity => Promise.resolve(entity));
    delete = jest.fn().mockResolvedValue({ affected: 1 });
    remove = jest.fn().mockResolvedValue(undefined);
    count = jest.fn().mockResolvedValue(0);
    createQueryBuilder = jest.fn().mockReturnValue({
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
        getRawOne: jest.fn().mockResolvedValue({}),
        getMany: jest.fn().mockResolvedValue([]),
        getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
        getOne: jest.fn().mockResolvedValue(null),
        from: jest.fn().mockReturnThis(),
    });
}
exports.KnowledgeFileRepository = KnowledgeFileRepository;
//# sourceMappingURL=knowledge-file.repository.js.map