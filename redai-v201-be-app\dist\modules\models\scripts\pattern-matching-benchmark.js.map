{"version": 3, "file": "pattern-matching-benchmark.js", "sourceRoot": "", "sources": ["../../../../src/modules/models/scripts/pattern-matching-benchmark.ts"], "names": [], "mappings": ";;AAsRS,kEAA2B;AAtRpC,uCAA2C;AAC3C,oDAAgD;AAChD,iGAA4G;AAC5G,yFAAoF;AAcpF,KAAK,UAAU,2BAA2B;IACxC,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAC;IAEvE,IAAI,CAAC;QAEH,MAAM,GAAG,GAAG,MAAM,kBAAW,CAAC,wBAAwB,CAAC,sBAAS,CAAC,CAAC;QAGlE,MAAM,aAAa,GAAG,GAAG,CAAC,GAAG,CAAC,8DAA4B,CAAC,CAAC;QAC5D,MAAM,kBAAkB,GAAG,GAAG,CAAC,GAAG,CAAC,uDAAyB,CAAC,CAAC;QAE9D,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QAGnD,MAAM,aAAa,GAAG;YACpB,EAAE,UAAU,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC,EAAE,WAAW,EAAE,aAAa,EAAE;YAC/D,EAAE,UAAU,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE,WAAW,EAAE,cAAc,EAAE;YACjE,EAAE,UAAU,EAAE,GAAG,EAAE,YAAY,EAAE,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE;YACjE,EAAE,UAAU,EAAE,GAAG,EAAE,YAAY,EAAE,EAAE,EAAE,WAAW,EAAE,kBAAkB,EAAE;SACvE,CAAC;QAEF,KAAK,MAAM,QAAQ,IAAI,aAAa,EAAE,CAAC;YACrC,OAAO,CAAC,GAAG,CAAC,cAAc,QAAQ,CAAC,WAAW,KAAK,QAAQ,CAAC,UAAU,cAAc,QAAQ,CAAC,YAAY,WAAW,CAAC,CAAC;YAGtH,MAAM,MAAM,GAAG,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YACvD,MAAM,QAAQ,GAAG,oBAAoB,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAG7D,MAAM,SAAS,GAAG,MAAM,sBAAsB,CAC5C,MAAM,EACN,QAAQ,EACR,kBAAkB,EAClB,QAAQ,CAAC,WAAW,CACrB,CAAC;YAGF,MAAM,SAAS,GAAG,MAAM,sBAAsB,CAC5C,MAAM,EACN,QAAQ,EACR,aAAa,EACb,kBAAkB,EAClB,QAAQ,CAAC,WAAW,CACrB,CAAC;YAGF,cAAc,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;YAE/C,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;IAEtE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QACpD,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;AACH,CAAC;AAKD,KAAK,UAAU,sBAAsB,CACnC,MAAgB,EAChB,QAAgD,EAChD,OAAkC,EAClC,QAAgB;IAMhB,MAAM,WAAW,GAAG,wBAAwB,QAAQ,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;IAErE,OAAO,CAAC,eAAe,CAAC,WAAW,EAAE;QACnC,QAAQ,EAAE,sBAAsB;QAChC,QAAQ;QACR,UAAU,EAAE,MAAM,CAAC,MAAM;QACzB,YAAY,EAAE,QAAQ,CAAC,MAAM;KAC9B,CAAC,CAAC;IAEH,MAAM,OAAO,GAA4B,EAAE,CAAC;IAG5C,KAAK,MAAM,OAAO,IAAI,MAAM,EAAE,CAAC;QAE7B,MAAM,eAAe,GAAG,QAAQ;aAC7B,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,sBAAsB,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;aACnE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAEvD,MAAM,SAAS,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;QAErC,OAAO,CAAC,IAAI,CAAC;YACX,OAAO;YACP,UAAU,EAAE,SAAS,EAAE,EAAE,IAAI,IAAI;YACjC,cAAc,EAAE,SAAS,EAAE,OAAO,IAAI,IAAI;SAC3C,CAAC,CAAC;IACL,CAAC;IAED,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;IAEpD,OAAO;QACL,QAAQ,EAAE,OAAO,EAAE,QAAQ,IAAI,CAAC;QAChC,OAAO;QACP,UAAU,EAAE,cAAc;KAC3B,CAAC;AACJ,CAAC;AAKD,KAAK,UAAU,sBAAsB,CACnC,MAAgB,EAChB,QAAgD,EAChD,MAAoC,EACpC,OAAkC,EAClC,QAAgB;IAMhB,MAAM,WAAW,GAAG,wBAAwB,QAAQ,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;IAErE,OAAO,CAAC,eAAe,CAAC,WAAW,EAAE;QACnC,QAAQ,EAAE,sBAAsB;QAChC,QAAQ;QACR,UAAU,EAAE,MAAM,CAAC,MAAM;QACzB,YAAY,EAAE,QAAQ,CAAC,MAAM;KAC9B,CAAC,CAAC;IAGH,MAAM,gBAAgB,GAAG,MAAM,CAAC,eAAe,CAC7C,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,gBAAgB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAC/D,CAAC;IAGF,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;IAE7D,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;IAEpD,OAAO;QACL,QAAQ,EAAE,OAAO,EAAE,QAAQ,IAAI,CAAC;QAChC,OAAO;QACP,UAAU,EAAE,YAAY;KACzB,CAAC;AACJ,CAAC;AAKD,SAAS,sBAAsB,CAAC,OAAe,EAAE,OAAe;IAE9D,MAAM,YAAY,GAAG,OAAO;SACzB,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;SACpB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAEvB,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,IAAI,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC;IACnD,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7B,CAAC;AAKD,SAAS,cAAc,CACrB,SAAc,EACd,SAAc,EACd,QAAa;IAEb,MAAM,OAAO,GAAG,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;IACxD,MAAM,UAAU,GAAG,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,KAAK,IAAI,CAAC,CAAC,MAAM,CAAC;IACtF,MAAM,UAAU,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC;IAE1C,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,CAAC,WAAW,GAAG,CAAC,CAAC;IACvD,OAAO,CAAC,GAAG,CAAC,0BAA0B,SAAS,CAAC,UAAU,IAAI,CAAC,CAAC;IAChE,OAAO,CAAC,GAAG,CAAC,kBAAkB,SAAS,CAAC,QAAQ,IAAI,CAAC,CAAC;IACtD,OAAO,CAAC,GAAG,CAAC,iBAAiB,UAAU,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;IAClE,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAEnG,OAAO,CAAC,GAAG,CAAC,0BAA0B,SAAS,CAAC,UAAU,IAAI,CAAC,CAAC;IAChE,OAAO,CAAC,GAAG,CAAC,kBAAkB,SAAS,CAAC,QAAQ,IAAI,CAAC,CAAC;IACtD,OAAO,CAAC,GAAG,CAAC,iBAAiB,UAAU,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;IAClE,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAEnG,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAC9C,OAAO,CAAC,GAAG,CAAC,iBAAiB,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;IAC3D,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC/E,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAG1E,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,qEAAqE,CAAC,CAAC;IACrF,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,iDAAiD,UAAU,UAAU,UAAU,GAAG,CAAC,CAAC;IAClG,CAAC;AACH,CAAC;AAKD,SAAS,kBAAkB,CAAC,KAAa;IACvC,MAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,MAAM,QAAQ,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;IACnF,MAAM,QAAQ,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACvE,MAAM,QAAQ,GAAG,CAAC,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;IAEjE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;QAC/B,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC7C,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC9C,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;QAE7C,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAKD,SAAS,oBAAoB,CAAC,KAAa;IACzC,MAAM,QAAQ,GAA2C,EAAE,CAAC;IAC5D,MAAM,YAAY,GAAG;QACnB,OAAO;QACP,UAAU;QACV,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,YAAY;QACZ,SAAS;KACV,CAAC;IAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;QAC/B,MAAM,WAAW,GAAG,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;QAC1D,QAAQ,CAAC,IAAI,CAAC;YACZ,EAAE,EAAE,WAAW,CAAC,EAAE;YAClB,OAAO,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI;SACzE,CAAC,CAAC;IACL,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAKD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,2BAA2B,EAAE;SAC1B,IAAI,CAAC,GAAG,EAAE;QACT,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QACxD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QAC7C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC"}