{"version": 3, "sources": ["../../../src/agent/enums/index.ts"], "sourcesContent": ["export enum ModelProviderEnum {\r\n  OPENAI = 'OPENAI',\r\n  XAI = 'XAI',\r\n  ANTHROPIC = 'ANTHROPIC',\r\n  GOOGLE = 'GOOGLE',\r\n  DEEPSEEK = 'DEEPSEEK',\r\n}\r\n\r\nexport enum InputModality {\r\n  TEXT = 'text',\r\n  IMAGE = 'image',\r\n  AUDIO = 'audio',\r\n  VIDEO = 'video',\r\n}\r\n\r\nexport enum OutputModality {\r\n  TEXT = 'text',\r\n  IMAGE = 'image',\r\n  AUDIO = 'audio',\r\n  VIDEO = 'video',\r\n}\r\n\r\nexport enum SamplingParameter {\r\n  TEMPERATURE = 'temperature',\r\n  TOP_P = 'top_p',\r\n  TOP_K = 'top_k',\r\n  MAX_TOKENS = 'max_tokens',\r\n  MAX_OUTPUT_TOKENS = 'max_output_tokens',\r\n}\r\n\r\nexport enum ModelFeature {\r\n  TOOL_CALL = 'tool_call',\r\n  PARALLEL_TOOL_CALL = 'parallel_tool_call',\r\n}\r\n"], "names": ["InputModality", "ModelFeature", "ModelProviderEnum", "OutputModality", "SamplingParameter"], "mappings": ";;;;;;;;;;;QAQYA;eAAAA;;QAsBAC;eAAAA;;QA9BAC;eAAAA;;QAeAC;eAAAA;;QAOAC;eAAAA;;;AAtBL,IAAA,AAAKF,2CAAAA;;;;;;WAAAA;;AAQL,IAAA,AAAKF,uCAAAA;;;;;WAAAA;;AAOL,IAAA,AAAKG,wCAAAA;;;;;WAAAA;;AAOL,IAAA,AAAKC,2CAAAA;;;;;;WAAAA;;AAQL,IAAA,AAAKH,sCAAAA;;;WAAAA"}