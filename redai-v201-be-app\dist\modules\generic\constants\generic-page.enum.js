"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericPageSubmissionSortByEnum = exports.GenericPageTemplateSortByEnum = exports.GenericPageSortByEnum = exports.GenericPageSubmissionStatusEnum = exports.GenericPageStatusEnum = void 0;
var GenericPageStatusEnum;
(function (GenericPageStatusEnum) {
    GenericPageStatusEnum["DRAFT"] = "draft";
    GenericPageStatusEnum["PUBLISHED"] = "published";
    GenericPageStatusEnum["ARCHIVED"] = "archived";
})(GenericPageStatusEnum || (exports.GenericPageStatusEnum = GenericPageStatusEnum = {}));
var GenericPageSubmissionStatusEnum;
(function (GenericPageSubmissionStatusEnum) {
    GenericPageSubmissionStatusEnum["PENDING"] = "pending";
    GenericPageSubmissionStatusEnum["PROCESSED"] = "processed";
    GenericPageSubmissionStatusEnum["REJECTED"] = "rejected";
})(GenericPageSubmissionStatusEnum || (exports.GenericPageSubmissionStatusEnum = GenericPageSubmissionStatusEnum = {}));
var GenericPageSortByEnum;
(function (GenericPageSortByEnum) {
    GenericPageSortByEnum["CREATED_AT"] = "createdAt";
    GenericPageSortByEnum["UPDATED_AT"] = "updatedAt";
    GenericPageSortByEnum["NAME"] = "name";
    GenericPageSortByEnum["PATH"] = "path";
    GenericPageSortByEnum["STATUS"] = "status";
})(GenericPageSortByEnum || (exports.GenericPageSortByEnum = GenericPageSortByEnum = {}));
var GenericPageTemplateSortByEnum;
(function (GenericPageTemplateSortByEnum) {
    GenericPageTemplateSortByEnum["CREATED_AT"] = "createdAt";
    GenericPageTemplateSortByEnum["UPDATED_AT"] = "updatedAt";
    GenericPageTemplateSortByEnum["NAME"] = "name";
    GenericPageTemplateSortByEnum["CATEGORY"] = "category";
})(GenericPageTemplateSortByEnum || (exports.GenericPageTemplateSortByEnum = GenericPageTemplateSortByEnum = {}));
var GenericPageSubmissionSortByEnum;
(function (GenericPageSubmissionSortByEnum) {
    GenericPageSubmissionSortByEnum["CREATED_AT"] = "createdAt";
    GenericPageSubmissionSortByEnum["UPDATED_AT"] = "updatedAt";
    GenericPageSubmissionSortByEnum["STATUS"] = "status";
})(GenericPageSubmissionSortByEnum || (exports.GenericPageSubmissionSortByEnum = GenericPageSubmissionSortByEnum = {}));
//# sourceMappingURL=generic-page.enum.js.map