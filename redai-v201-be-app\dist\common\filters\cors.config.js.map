{"version": 3, "file": "cors.config.js", "sourceRoot": "", "sources": ["../../../src/common/filters/cors.config.ts"], "names": [], "mappings": ";;;AAMa,QAAA,UAAU,GAAgB;IACrC,MAAM,EAAE;QACN,uBAAuB;QACvB,uBAAuB;QACvB,uBAAuB;QACvB,uBAAuB;QACvB,uBAAuB;QACvB,uBAAuB;QACvB,uBAAuB;QACvB,uBAAuB;QACvB,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,qBAAqB;KACtB;IACD,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;IAC7D,WAAW,EAAE,IAAI;IACjB,cAAc,EAAE;QACd,QAAQ;QACR,kBAAkB;QAClB,cAAc;QACd,QAAQ;QACR,eAAe;QACf,iBAAiB;QACjB,mBAAmB;QACnB,wBAAwB;KACzB;IACD,cAAc,EAAE,CAAC,qBAAqB,CAAC;IACvC,MAAM,EAAE,IAAI;CACb,CAAC;AAOK,MAAM,kBAAkB,GAAG,CAChC,MAAc,EACd,QAAsD,EACtD,EAAE;IACF,MAAM,cAAc,GAAG;QACrB,uBAAuB;QACvB,uBAAuB;QACvB,uBAAuB;QACvB,uBAAuB;QACvB,uBAAuB;QACvB,uBAAuB;QACvB,uBAAuB;QACvB,uBAAuB;QACvB,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,2BAA2B;QAC3B,qBAAqB;KACtB,CAAC;IAEF,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QACpC,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC9B,CAAC;SAAM,CAAC;QACN,OAAO,QAAQ,CAAC,IAAI,KAAK,CAAC,UAAU,MAAM,2BAA2B,CAAC,CAAC,CAAC;IAC1E,CAAC;AACH,CAAC,CAAC;AAjCW,QAAA,kBAAkB,sBAiC7B;AAKW,QAAA,iBAAiB,GAAgB;IAC5C,MAAM,EAAE,0BAAkB;IAC1B,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;IAC7D,WAAW,EAAE,IAAI;IACjB,cAAc,EAAE;QACd,QAAQ;QACR,kBAAkB;QAClB,cAAc;QACd,QAAQ;QACR,eAAe;QACf,iBAAiB;QACjB,mBAAmB;QACnB,wBAAwB;QACxB,iBAAiB;QACjB,SAAS;QACT,WAAW;KACZ;IACD,cAAc,EAAE;QACd,qBAAqB;QACrB,iBAAiB;QACjB,SAAS;QACT,WAAW;KACZ;IACD,MAAM,EAAE,IAAI;CACb,CAAC"}