"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RuleContractQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const query_dto_1 = require("../../../../common/dto/query.dto");
const rule_contract_entity_1 = require("../../entities/rule-contract.entity");
class RuleContractQueryDto extends query_dto_1.QueryDto {
    status;
    type;
}
exports.RuleContractQueryDto = RuleContractQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Lọc theo trạng thái hợp đồng',
        enum: rule_contract_entity_1.ContractStatusEnum,
        required: false,
        example: rule_contract_entity_1.ContractStatusEnum.APPROVED,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(rule_contract_entity_1.ContractStatusEnum),
    __metadata("design:type", String)
], RuleContractQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Lọc theo loại hợp đồng',
        enum: rule_contract_entity_1.ContractTypeEnum,
        required: false,
        example: rule_contract_entity_1.ContractTypeEnum.INDIVIDUAL,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(rule_contract_entity_1.ContractTypeEnum),
    __metadata("design:type", String)
], RuleContractQueryDto.prototype, "type", void 0);
//# sourceMappingURL=rule-contract-query.dto.js.map