"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeTypeOrmTransactional = initializeTypeOrmTransactional;
const typeorm_transactional_1 = require("typeorm-transactional");
function initializeTypeOrmTransactional() {
    (0, typeorm_transactional_1.initializeTransactionalContext)({ storageDriver: typeorm_transactional_1.StorageDriver.AUTO });
}
//# sourceMappingURL=typeorm-transactional.config.js.map