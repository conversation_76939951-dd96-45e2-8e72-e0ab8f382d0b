"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get DEFAULT_TRIMMING_STRATEGY () {
        return DEFAULT_TRIMMING_STRATEGY;
    },
    get DEFAULT_TRIMMING_THRESHOLD () {
        return DEFAULT_TRIMMING_THRESHOLD;
    },
    get SUPERVISOR_TAG () {
        return SUPERVISOR_TAG;
    },
    get SUPERVISOR_TOOL_CALL_TAG () {
        return SUPERVISOR_TOOL_CALL_TAG;
    },
    get WORKER_TAG () {
        return WORKER_TAG;
    },
    get WORKER_TOOL_CALL_TAG () {
        return WORKER_TOOL_CALL_TAG;
    }
});
const DEFAULT_TRIMMING_STRATEGY = 'top_k';
const DEFAULT_TRIMMING_THRESHOLD = 20;
const SUPERVISOR_TOOL_CALL_TAG = 'supervisor_tool_call';
const WORKER_TOOL_CALL_TAG = 'worker_tool_call';
const SUPERVISOR_TAG = 'supervisor';
const WORKER_TAG = 'worker';

//# sourceMappingURL=constants.js.map