"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmployeeInfoMapper = void 0;
const common_1 = require("../dto/common");
class EmployeeInfoMapper {
    static toDto(employeeId, name, avatar, date) {
        const dto = new common_1.EmployeeInfoDto();
        dto.employeeId = employeeId;
        dto.name = name || 'Unknown';
        dto.avatar = avatar || null;
        dto.date = date;
        return dto;
    }
}
exports.EmployeeInfoMapper = EmployeeInfoMapper;
//# sourceMappingURL=employee-info.mapper.js.map