"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminStepController = void 0;
const guards_1 = require("../../../auth/guards");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const services_1 = require("../services");
const swagger_2 = require("../../../../common/swagger");
let AdminStepController = class AdminStepController {
    adminStepService;
    constructor(adminStepService) {
        this.adminStepService = adminStepService;
    }
};
exports.AdminStepController = AdminStepController;
exports.AdminStepController = AdminStepController = __decorate([
    (0, swagger_1.ApiTags)(swagger_2.SWAGGER_API_TAGS.ADMIN_TASK),
    (0, common_1.Controller)('admin/steps'),
    (0, common_1.UseGuards)(guards_1.JwtEmployeeGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [services_1.AdminStepService])
], AdminStepController);
//# sourceMappingURL=admin-step.controller.js.map