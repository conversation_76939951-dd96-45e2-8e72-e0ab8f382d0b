"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnowledgeFileListResponseSchema = exports.KnowledgeFileSchema = void 0;
const swagger_1 = require("@nestjs/swagger");
class KnowledgeFileSchema {
    id;
    name;
    storageKey;
    ownerType;
    ownedBy;
    isOwner;
    isForSale;
    createdAt;
    storage;
    constructor(partial) {
        Object.assign(this, partial);
    }
}
exports.KnowledgeFileSchema = KnowledgeFileSchema;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của file tri thức',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    __metadata("design:type", String)
], KnowledgeFileSchema.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên hiển thị của file tri thức',
        example: 'Tài liệu hướng dẫn sử dụng NestJS',
    }),
    __metadata("design:type", String)
], KnowledgeFileSchema.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Khóa định danh file trên hệ thống lưu trữ',
        example: 'knowledge/123e4567-e89b-12d3-a456-426614174000.pdf',
    }),
    __metadata("design:type", String)
], KnowledgeFileSchema.prototype, "storageKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại người sở hữu file',
        example: 'user',
        enum: ['user', 'employee'],
    }),
    __metadata("design:type", String)
], KnowledgeFileSchema.prototype, "ownerType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của người sở hữu file',
        example: 1,
    }),
    __metadata("design:type", Number)
], KnowledgeFileSchema.prototype, "ownedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái sở hữu file',
        example: true,
    }),
    __metadata("design:type", Boolean)
], KnowledgeFileSchema.prototype, "isOwner", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái đăng bán file',
        example: false,
    }),
    __metadata("design:type", Boolean)
], KnowledgeFileSchema.prototype, "isForSale", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời điểm tạo bản ghi file (unix timestamp)',
        example: 1625097600000,
    }),
    __metadata("design:type", Number)
], KnowledgeFileSchema.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Dung lượng của file tri thức (byte)',
        example: 1024000,
    }),
    __metadata("design:type", Number)
], KnowledgeFileSchema.prototype, "storage", void 0);
class KnowledgeFileListResponseSchema {
    items;
    meta;
}
exports.KnowledgeFileListResponseSchema = KnowledgeFileListResponseSchema;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách file tri thức',
        type: [KnowledgeFileSchema],
    }),
    __metadata("design:type", Array)
], KnowledgeFileListResponseSchema.prototype, "items", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin phân trang',
        type: 'object',
        properties: {
            totalItems: {
                type: 'number',
                example: 100,
                description: 'Tổng số file tri thức',
            },
            itemCount: {
                type: 'number',
                example: 10,
                description: 'Số file tri thức trên trang hiện tại',
            },
            itemsPerPage: {
                type: 'number',
                example: 10,
                description: 'Số file tri thức trên mỗi trang',
            },
            totalPages: {
                type: 'number',
                example: 10,
                description: 'Tổng số trang',
            },
            currentPage: {
                type: 'number',
                example: 1,
                description: 'Trang hiện tại',
            },
        },
    }),
    __metadata("design:type", Object)
], KnowledgeFileListResponseSchema.prototype, "meta", void 0);
//# sourceMappingURL=knowledge-file.schema.js.map