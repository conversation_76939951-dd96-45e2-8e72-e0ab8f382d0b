"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserDataFineTuneDetialResponseDto = exports.UserDataFineTuneResponseDto = void 0;
const data_fine_tune_status_enum_1 = require("../../../constants/data-fine-tune-status.enum");
const swagger_1 = require("@nestjs/swagger");
class UserDataFineTuneResponseDto {
    id;
    name;
    description;
    createdAt;
    status;
}
exports.UserDataFineTuneResponseDto = UserDataFineTuneResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của bộ dữ liệu',
        example: '123e4567-e89b-12d3-a456-426614174000'
    }),
    __metadata("design:type", String)
], UserDataFineTuneResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên bộ dữ liệu',
        example: 'Customer Service Training Dataset'
    }),
    __metadata("design:type", String)
], UserDataFineTuneResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả về bộ dữ liệu',
        example: 'Bộ dữ liệu huấn luyện cho chatbot hỗ trợ khách hàng',
        nullable: true
    }),
    __metadata("design:type", Object)
], UserDataFineTuneResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo (epoch timestamp)',
        example: 1703980800000
    }),
    __metadata("design:type", Number)
], UserDataFineTuneResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái của bộ dữ liệu',
        enum: data_fine_tune_status_enum_1.DataFineTuneStatus,
        example: data_fine_tune_status_enum_1.DataFineTuneStatus.PENDING,
    }),
    __metadata("design:type", String)
], UserDataFineTuneResponseDto.prototype, "status", void 0);
class UserDataFineTuneDetialResponseDto extends UserDataFineTuneResponseDto {
    trainDatasetUrl;
    validDatasetUrl;
}
exports.UserDataFineTuneDetialResponseDto = UserDataFineTuneDetialResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Đường dẫn đến tập dữ liệu huấn luyện',
        example: 'https://s3.amazonaws.com/bucket/path/to/dataset.jsonl'
    }),
    __metadata("design:type", String)
], UserDataFineTuneDetialResponseDto.prototype, "trainDatasetUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số lượng mẫu validation',
        example: 'https://s3.amazonaws.com/bucket/path/to/dataset.jsonl',
        nullable: true
    }),
    __metadata("design:type", Object)
], UserDataFineTuneDetialResponseDto.prototype, "validDatasetUrl", void 0);
//# sourceMappingURL=user-data-fine-tune-response.dto.js.map