"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddOriginalVersionIdToUserToolVersions1720000000001 = void 0;
class AddOriginalVersionIdToUserToolVersions1720000000001 {
    async up(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE "user_tool_versions" 
      ADD COLUMN IF NOT EXISTS "original_version_id" UUID NULL
    `);
        await queryRunner.query(`
      COMMENT ON COLUMN "user_tool_versions"."original_version_id" 
      IS 'ID của phiên bản gốc từ admin tool version, tham chiếu đến admin_tool_versions.id'
    `);
        await queryRunner.query(`
      ALTER TABLE "user_tool_versions" 
      DROP CONSTRAINT IF EXISTS "unique_user_function_version"
    `);
        await queryRunner.query(`
      ALTER TABLE "user_tool_versions" 
      ADD CONSTRAINT "unique_user_function_version" 
      UNIQUE ("user_id", "original_function_id", "original_version_id")
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE "user_tool_versions" 
      DROP CONSTRAINT IF EXISTS "unique_user_function_version"
    `);
        await queryRunner.query(`
      ALTER TABLE "user_tool_versions" 
      ADD CONSTRAINT "unique_user_function_version" 
      UNIQUE ("user_id", "original_function_id", "version_number")
    `);
        await queryRunner.query(`
      ALTER TABLE "user_tool_versions" 
      DROP COLUMN IF EXISTS "original_version_id"
    `);
    }
}
exports.AddOriginalVersionIdToUserToolVersions1720000000001 = AddOriginalVersionIdToUserToolVersions1720000000001;
//# sourceMappingURL=1720000000001-AddOriginalVersionIdToUserToolVersions.js.map