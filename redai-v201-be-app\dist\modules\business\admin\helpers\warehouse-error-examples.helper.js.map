{"version": 3, "file": "warehouse-error-examples.helper.js", "sourceRoot": "", "sources": ["../../../../../src/modules/business/admin/helpers/warehouse-error-examples.helper.ts"], "names": [], "mappings": ";;;AAAA,2EAAgF;AAKnE,QAAA,wBAAwB,GAAG;IAEtC,mBAAmB,EAAE;QACnB,IAAI,EAAE,iDAA2B,CAAC,mBAAmB,CAAC,IAAI;QAC1D,OAAO,EAAE,iDAA2B,CAAC,mBAAmB,CAAC,OAAO;QAChE,MAAM,EAAE;YACN,WAAW,EAAE,GAAG;SACjB;KACF;IAED,qBAAqB,EAAE;QACrB,IAAI,EAAE,iDAA2B,CAAC,qBAAqB,CAAC,IAAI;QAC5D,OAAO,EAAE,iDAA2B,CAAC,qBAAqB,CAAC,OAAO;QAClE,MAAM,EAAE;YACN,KAAK,EAAE,2BAA2B;SACnC;KACF;IAGD,4BAA4B,EAAE;QAC5B,IAAI,EAAE,iDAA2B,CAAC,4BAA4B,CAAC,IAAI;QACnE,OAAO,EAAE,iDAA2B,CAAC,4BAA4B,CAAC,OAAO;QACzE,MAAM,EAAE;YACN,WAAW,EAAE,GAAG;SACjB;KACF;IAED,8BAA8B,EAAE;QAC9B,IAAI,EAAE,iDAA2B,CAAC,8BAA8B,CAAC,IAAI;QACrE,OAAO,EAAE,iDAA2B,CAAC,8BAA8B,CAAC,OAAO;QAC3E,MAAM,EAAE;YACN,KAAK,EAAE,2BAA2B;SACnC;KACF;IAGD,2BAA2B,EAAE;QAC3B,IAAI,EAAE,iDAA2B,CAAC,2BAA2B,CAAC,IAAI;QAClE,OAAO,EAAE,iDAA2B,CAAC,2BAA2B,CAAC,OAAO;QACxE,MAAM,EAAE;YACN,WAAW,EAAE,GAAG;SACjB;KACF;IAED,6BAA6B,EAAE;QAC7B,IAAI,EAAE,iDAA2B,CAAC,6BAA6B,CAAC,IAAI;QACpE,OAAO,EAAE,iDAA2B,CAAC,6BAA6B,CAAC,OAAO;QAC1E,MAAM,EAAE;YACN,KAAK,EAAE,2BAA2B;SACnC;KACF;IAGD,gCAAgC,EAAE;QAChC,IAAI,EAAE,iDAA2B,CAAC,gCAAgC,CAAC,IAAI;QACvE,OAAO,EAAE,iDAA2B,CAAC,gCAAgC,CAAC,OAAO;QAC7E,MAAM,EAAE;YACN,WAAW,EAAE,CAAC;YACd,OAAO,EAAE,GAAG;SACb;KACF;IAED,kCAAkC,EAAE;QAClC,IAAI,EAAE,iDAA2B,CAAC,kCAAkC,CAAC,IAAI;QACzE,OAAO,EAAE,iDAA2B,CAAC,kCAAkC,CAAC,OAAO;QAC/E,MAAM,EAAE;YACN,KAAK,EAAE,2BAA2B;SACnC;KACF;IAGD,sBAAsB,EAAE;QACtB,IAAI,EAAE,iDAA2B,CAAC,sBAAsB,CAAC,IAAI;QAC7D,OAAO,EAAE,iDAA2B,CAAC,sBAAsB,CAAC,OAAO;QACnE,MAAM,EAAE;YACN,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;SACpC;KACF;CACF,CAAC"}