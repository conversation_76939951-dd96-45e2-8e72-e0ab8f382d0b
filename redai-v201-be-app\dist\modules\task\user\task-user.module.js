"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskUserModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const entities_1 = require("../entities");
const repositories_1 = require("../repositories");
const controllers_1 = require("./controllers");
const services_1 = require("./services");
let TaskUserModule = class TaskUserModule {
};
exports.TaskUserModule = TaskUserModule;
exports.TaskUserModule = TaskUserModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                entities_1.UserTask,
                entities_1.UserStep,
                entities_1.UserTaskExecution,
                entities_1.UserStepConnection
            ])
        ],
        controllers: [
            controllers_1.UserTaskController,
            controllers_1.UserStepController,
            controllers_1.UserConnectionController
        ],
        providers: [
            repositories_1.UserTaskRepository,
            repositories_1.UserStepRepository,
            repositories_1.UserTaskExecutionRepository,
            repositories_1.UserStepConnectionRepository,
            services_1.UserTaskService,
            services_1.UserStepService,
            services_1.UserConnectionService
        ],
        exports: [
            services_1.UserTaskService,
            services_1.UserStepService,
            services_1.UserConnectionService
        ],
    })
], TaskUserModule);
//# sourceMappingURL=task-user.module.js.map