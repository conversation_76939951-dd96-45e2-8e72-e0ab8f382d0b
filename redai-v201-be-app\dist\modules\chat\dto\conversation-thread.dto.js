"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetThreadMessagesQueryDto = exports.ThreadMessageResponseDto = exports.GetConversationThreadsQueryDto = exports.ConversationThreadResponseDto = exports.UpdateConversationThreadDto = exports.CreateConversationThreadDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const query_dto_1 = require("../../../common/dto/query.dto");
class CreateConversationThreadDto {
    name;
    userId;
}
exports.CreateConversationThreadDto = CreateConversationThreadDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the conversation thread',
        example: 'My AI Assistant Chat',
        maxLength: 255,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateConversationThreadDto.prototype, "name", void 0);
class UpdateConversationThreadDto {
    name;
}
exports.UpdateConversationThreadDto = UpdateConversationThreadDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Name of the conversation thread',
        example: 'Updated Chat Name',
        maxLength: 255,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateConversationThreadDto.prototype, "name", void 0);
class ConversationThreadResponseDto {
    threadId;
    name;
    userId;
    createdAt;
    updatedAt;
}
exports.ConversationThreadResponseDto = ConversationThreadResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique thread identifier',
        example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ConversationThreadResponseDto.prototype, "threadId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the conversation thread',
        example: 'My AI Assistant Chat',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ConversationThreadResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID who owns this thread',
        example: 123,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], ConversationThreadResponseDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Creation timestamp (epoch milliseconds)',
        example: 1749269123456,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], ConversationThreadResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last update timestamp (epoch milliseconds)',
        example: 1749269123456,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], ConversationThreadResponseDto.prototype, "updatedAt", void 0);
class GetConversationThreadsQueryDto extends query_dto_1.QueryDto {
}
exports.GetConversationThreadsQueryDto = GetConversationThreadsQueryDto;
class ThreadMessageResponseDto {
    messageId;
    threadId;
    role;
    content;
    timestamp;
    createdBy;
}
exports.ThreadMessageResponseDto = ThreadMessageResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique message identifier',
        example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ThreadMessageResponseDto.prototype, "messageId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thread ID this message belongs to',
        example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ThreadMessageResponseDto.prototype, "threadId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Role of the message sender',
        example: 'user',
        enum: ['user', 'assistant'],
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ThreadMessageResponseDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Message content in JSONB format',
        example: { text: 'Hello, how can I help you?', type: 'text' },
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], ThreadMessageResponseDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Message timestamp (epoch milliseconds)',
        example: 1749269123456,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], ThreadMessageResponseDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID who created this message',
        example: 123,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], ThreadMessageResponseDto.prototype, "createdBy", void 0);
class GetThreadMessagesQueryDto extends query_dto_1.QueryDto {
    role;
    limit = 50;
}
exports.GetThreadMessagesQueryDto = GetThreadMessagesQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by message role',
        example: 'user',
        enum: ['user', 'assistant'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetThreadMessagesQueryDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of messages per page',
        example: 50,
        default: 50,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Max)(200),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], GetThreadMessagesQueryDto.prototype, "limit", void 0);
//# sourceMappingURL=conversation-thread.dto.js.map