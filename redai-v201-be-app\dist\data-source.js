"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppDataSource = void 0;
const typeorm_1 = require("typeorm");
const typeorm_naming_strategies_1 = require("typeorm-naming-strategies");
const dotenv = require("dotenv");
dotenv.config();
exports.AppDataSource = new typeorm_1.DataSource({
    type: 'postgres',
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '5432'),
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
    ssl: process.env.DB_SSL === 'true' ? {
        rejectUnauthorized: false,
    } : false,
    entities: [],
    migrations: ['src/database/migrations/*.ts'],
    namingStrategy: new typeorm_naming_strategies_1.SnakeNamingStrategy(),
    logging: true,
});
//# sourceMappingURL=data-source.js.map