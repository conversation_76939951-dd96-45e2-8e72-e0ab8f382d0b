"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequireDynamicPermission = exports.RequirePermissionEnum = exports.PERMISSIONS_KEY = void 0;
const common_1 = require("@nestjs/common");
exports.PERMISSIONS_KEY = 'permissions';
const RequirePermissionEnum = (...permissions) => {
    return (0, common_1.SetMetadata)(exports.PERMISSIONS_KEY, permissions);
};
exports.RequirePermissionEnum = RequirePermissionEnum;
const RequireDynamicPermission = (permissionSelector) => {
    return (target, key, descriptor) => {
        (0, common_1.SetMetadata)(exports.PERMISSIONS_KEY + '_SELECTOR', permissionSelector)(target, key, descriptor);
        (0, common_1.SetMetadata)(exports.PERMISSIONS_KEY + '_DYNAMIC', true)(target, key, descriptor);
        return descriptor;
    };
};
exports.RequireDynamicPermission = RequireDynamicPermission;
//# sourceMappingURL=require-module-action.decorator.js.map