"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var GenericPageSubmissionRepository_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericPageSubmissionRepository = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const generic_page_submission_entity_1 = require("../entities/generic-page-submission.entity");
const common_2 = require("../../../common");
const generic_page_error_code_1 = require("../exceptions/generic-page-error.code");
let GenericPageSubmissionRepository = GenericPageSubmissionRepository_1 = class GenericPageSubmissionRepository extends typeorm_1.Repository {
    dataSource;
    logger = new common_1.Logger(GenericPageSubmissionRepository_1.name);
    constructor(dataSource) {
        super(generic_page_submission_entity_1.GenericPageSubmission, dataSource.createEntityManager());
        this.dataSource = dataSource;
    }
    createBaseQuery() {
        return this.createQueryBuilder('submission');
    }
    async findById(id) {
        try {
            const submission = await this.createBaseQuery()
                .where('submission.id = :id', { id })
                .getOne();
            if (!submission) {
                throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_SUBMISSION_NOT_FOUND, `Không tìm thấy dữ liệu gửi với ID ${id}`);
            }
            return submission;
        }
        catch (error) {
            if (error instanceof common_2.AppException) {
                throw error;
            }
            this.logger.error(`Error finding submission by ID: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_SUBMISSION_NOT_FOUND, `Lỗi khi tìm dữ liệu gửi với ID ${id}`);
        }
    }
    async findByPageId(pageId) {
        try {
            return await this.createBaseQuery()
                .where('submission.pageId = :pageId', { pageId })
                .orderBy('submission.createdAt', 'DESC')
                .getMany();
        }
        catch (error) {
            this.logger.error(`Error finding submissions by page ID: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_SUBMISSION_NOT_FOUND, `Lỗi khi tìm dữ liệu gửi cho trang với ID ${pageId}`);
        }
    }
    async updateStatus(id, status) {
        try {
            const submission = await this.findById(id);
            submission.status = status;
            submission.updatedAt = Date.now();
            return await this.save(submission);
        }
        catch (error) {
            if (error instanceof common_2.AppException) {
                throw error;
            }
            this.logger.error(`Error updating submission status: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_SUBMISSION_CREATE_ERROR, `Lỗi khi cập nhật trạng thái dữ liệu gửi với ID ${id}`);
        }
    }
};
exports.GenericPageSubmissionRepository = GenericPageSubmissionRepository;
exports.GenericPageSubmissionRepository = GenericPageSubmissionRepository = GenericPageSubmissionRepository_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], GenericPageSubmissionRepository);
//# sourceMappingURL=generic-page-submission.repository.js.map