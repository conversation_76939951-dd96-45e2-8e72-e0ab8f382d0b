"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelInfoResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const type_provider_util_1 = require("../../../../../shared/services/ai/utils/type-provider.util");
let ModelInfoResponseDto = class ModelInfoResponseDto {
    modelId;
    name;
    provider;
    description;
    version;
    inputCostPer1kTokens;
    outputCostPer1kTokens;
    contextLength;
    maxOutputTokens;
    capabilities;
    supportedLanguages;
    trainingDataCutoff;
    parameters;
    architecture;
    availability;
    rateLimits;
    source;
    userKeyInfo;
    recommendations;
    metadata;
};
exports.ModelInfoResponseDto = ModelInfoResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Model ID từ nhà cung cấp',
        example: 'gpt-4-turbo-preview',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ModelInfoResponseDto.prototype, "modelId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên hiển thị của model',
        example: 'GPT-4 Turbo Preview',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ModelInfoResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nhà cung cấp model',
        enum: type_provider_util_1.ProviderEnumq,
        example: type_provider_util_1.ProviderEnumq.OPENAI,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ModelInfoResponseDto.prototype, "provider", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả chi tiết về model',
        example: 'GPT-4 Turbo with improved instruction following, JSON mode, and reproducible outputs',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ModelInfoResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Version của model',
        example: '2024-01-25',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ModelInfoResponseDto.prototype, "version", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Chi phí input per 1k tokens (USD)',
        example: 0.01,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], ModelInfoResponseDto.prototype, "inputCostPer1kTokens", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Chi phí output per 1k tokens (USD)',
        example: 0.03,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], ModelInfoResponseDto.prototype, "outputCostPer1kTokens", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Độ dài context tối đa',
        example: 128000,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], ModelInfoResponseDto.prototype, "contextLength", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Max output tokens',
        example: 4096,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], ModelInfoResponseDto.prototype, "maxOutputTokens", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Capabilities của model',
        example: ['text-generation', 'function-calling', 'json-mode'],
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Array)
], ModelInfoResponseDto.prototype, "capabilities", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Supported languages',
        example: ['en', 'vi', 'zh', 'ja', 'ko', 'fr', 'de', 'es'],
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Array)
], ModelInfoResponseDto.prototype, "supportedLanguages", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Training data cutoff',
        example: '2023-04',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ModelInfoResponseDto.prototype, "trainingDataCutoff", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Model parameters (nếu có thông tin)',
        example: '175B',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ModelInfoResponseDto.prototype, "parameters", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Architecture type',
        example: 'transformer',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ModelInfoResponseDto.prototype, "architecture", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Availability status',
        example: {
            isAvailable: true,
            lastChecked: 1640995200000,
            responseTime: 250,
            status: 'operational'
        },
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], ModelInfoResponseDto.prototype, "availability", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Rate limits (nếu có)',
        example: {
            requestsPerMinute: 3500,
            tokensPerMinute: 90000,
            requestsPerDay: 10000
        },
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], ModelInfoResponseDto.prototype, "rateLimits", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Source của model info',
        enum: ['admin', 'user-key', 'registry'],
        example: 'admin',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ModelInfoResponseDto.prototype, "source", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin user key (nếu source là user-key)',
        example: {
            keyId: '123e4567-e89b-12d3-a456-426614174000',
            keyName: 'My OpenAI Key',
            lastTested: 1640995200000
        },
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], ModelInfoResponseDto.prototype, "userKeyInfo", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Usage recommendations',
        example: {
            bestFor: ['chat', 'code-generation', 'analysis'],
            notRecommendedFor: ['real-time', 'streaming'],
            tips: ['Use system messages for better results', 'Enable JSON mode for structured output']
        },
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], ModelInfoResponseDto.prototype, "recommendations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Metadata bổ sung',
        example: {
            multimodal: false,
            streaming: true,
            functionCalling: true
        },
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], ModelInfoResponseDto.prototype, "metadata", void 0);
exports.ModelInfoResponseDto = ModelInfoResponseDto = __decorate([
    (0, class_transformer_1.Exclude)()
], ModelInfoResponseDto);
//# sourceMappingURL=model-info-response.dto.js.map