"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var GenericPageTemplateRepository_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericPageTemplateRepository = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const generic_page_template_entity_1 = require("../entities/generic-page-template.entity");
const common_2 = require("../../../common");
const generic_page_error_code_1 = require("../exceptions/generic-page-error.code");
let GenericPageTemplateRepository = GenericPageTemplateRepository_1 = class GenericPageTemplateRepository extends typeorm_1.Repository {
    dataSource;
    logger = new common_1.Logger(GenericPageTemplateRepository_1.name);
    constructor(dataSource) {
        super(generic_page_template_entity_1.GenericPageTemplate, dataSource.createEntityManager());
        this.dataSource = dataSource;
    }
    createBaseQuery() {
        return this.createQueryBuilder('template');
    }
    async findById(id) {
        try {
            const template = await this.createBaseQuery()
                .where('template.id = :id', { id })
                .getOne();
            if (!template) {
                throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_NOT_FOUND, `Không tìm thấy mẫu trang với ID ${id}`);
            }
            return template;
        }
        catch (error) {
            if (error instanceof common_2.AppException) {
                throw error;
            }
            this.logger.error(`Error finding template by ID: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_NOT_FOUND, `Lỗi khi tìm mẫu trang với ID ${id}`);
        }
    }
    async findByCategory(category) {
        try {
            return await this.createBaseQuery()
                .where('template.category = :category', { category })
                .getMany();
        }
        catch (error) {
            this.logger.error(`Error finding templates by category: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_NOT_FOUND, `Lỗi khi tìm mẫu trang theo danh mục ${category}`);
        }
    }
    async findByTag(tag) {
        try {
            return await this.createBaseQuery()
                .innerJoin('generic_page_template_tags', 'tags', 'tags.template_id = template.id')
                .where('tags.tag = :tag', { tag })
                .getMany();
        }
        catch (error) {
            this.logger.error(`Error finding templates by tag: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_NOT_FOUND, `Lỗi khi tìm mẫu trang theo tag ${tag}`);
        }
    }
};
exports.GenericPageTemplateRepository = GenericPageTemplateRepository;
exports.GenericPageTemplateRepository = GenericPageTemplateRepository = GenericPageTemplateRepository_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], GenericPageTemplateRepository);
//# sourceMappingURL=generic-page-template.repository.js.map