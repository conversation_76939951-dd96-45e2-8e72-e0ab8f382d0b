"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CONNECTION_ERROR_CODES = void 0;
const common_1 = require("../../../common");
const common_2 = require("@nestjs/common");
exports.CONNECTION_ERROR_CODES = {
    CONNECTION_NOT_FOUND: new common_1.ErrorCode(10200, 'Không tìm thấy kết nối', common_2.HttpStatus.NOT_FOUND),
    CONNECTION_CREATION_FAILED: new common_1.ErrorCode(10201, 'Tạo kết nối thất bại', common_2.HttpStatus.INTERNAL_SERVER_ERROR),
    CONNECTION_UPDATE_FAILED: new common_1.ErrorCode(10202, 'Cập nhật kết nối thất bại', common_2.HttpStatus.INTERNAL_SERVER_ERROR),
    CONNECTION_DELETE_FAILED: new common_1.ErrorCode(10203, 'Xóa kết nối thất bại', common_2.HttpStatus.INTERNAL_SERVER_ERROR),
    CONNECTION_FETCH_FAILED: new common_1.ErrorCode(10204, 'Lấy thông tin kết nối thất bại', common_2.HttpStatus.INTERNAL_SERVER_ERROR),
    CONNECTION_UNAUTHORIZED: new common_1.ErrorCode(10210, 'Không có quyền truy cập kết nối này', common_2.HttpStatus.FORBIDDEN),
    CONNECTION_INVALID_DATA: new common_1.ErrorCode(10220, 'Dữ liệu kết nối không hợp lệ', common_2.HttpStatus.BAD_REQUEST),
    CONNECTION_SAME_STEP: new common_1.ErrorCode(10221, 'Không thể kết nối một bước với chính nó', common_2.HttpStatus.BAD_REQUEST),
    CONNECTION_FIELD_REQUIRED: new common_1.ErrorCode(10222, 'Tên trường kết nối là bắt buộc', common_2.HttpStatus.BAD_REQUEST),
    CONNECTION_FIELD_TOO_LONG: new common_1.ErrorCode(10223, 'Tên trường kết nối quá dài (tối đa 255 ký tự)', common_2.HttpStatus.BAD_REQUEST),
    CONNECTION_STEP_NOT_FOUND: new common_1.ErrorCode(10224, 'Không tìm thấy bước để kết nối', common_2.HttpStatus.NOT_FOUND),
    CONNECTION_STEPS_DIFFERENT_TASKS: new common_1.ErrorCode(10225, 'Các bước phải thuộc cùng một nhiệm vụ', common_2.HttpStatus.BAD_REQUEST),
    CONNECTION_CIRCULAR_REFERENCE: new common_1.ErrorCode(10230, 'Phát hiện tham chiếu vòng tròn trong kết nối', common_2.HttpStatus.BAD_REQUEST),
    CONNECTION_DUPLICATE: new common_1.ErrorCode(10231, 'Kết nối này đã tồn tại', common_2.HttpStatus.BAD_REQUEST),
    CONNECTION_INCOMPATIBLE_TYPES: new common_1.ErrorCode(10232, 'Kiểu dữ liệu của các trường kết nối không tương thích', common_2.HttpStatus.BAD_REQUEST),
    CONNECTION_LIMIT_EXCEEDED: new common_1.ErrorCode(10240, 'Đã vượt quá giới hạn số lượng kết nối', common_2.HttpStatus.BAD_REQUEST),
};
//# sourceMappingURL=connection.exceptions.js.map