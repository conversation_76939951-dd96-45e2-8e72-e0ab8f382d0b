"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlogListResponseSchema = exports.BlogSchema = void 0;
const swagger_1 = require("@nestjs/swagger");
const enums_1 = require("../enums");
class BlogSchema {
    id;
    title;
    content;
    point;
    viewCount;
    thumbnailUrl;
    tags;
    createdAt;
    updatedAt;
    userId;
    employeeId;
    employeeModerator;
    authorType;
    status;
    enable;
    like;
    constructor(partial) {
        Object.assign(this, partial);
    }
}
exports.BlogSchema = BlogSchema;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của bài viết',
        example: 1,
    }),
    __metadata("design:type", Number)
], BlogSchema.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tiêu đề bài viết',
        example: 'Hướng dẫn sử dụng NestJS',
        nullable: true,
    }),
    __metadata("design:type", String)
], BlogSchema.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Đường link của file content trên CDN',
        example: 'https://cdn.example.com/blogs/content-123.html',
        nullable: true,
    }),
    __metadata("design:type", String)
], BlogSchema.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số point để mua bài viết',
        example: 100,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogSchema.prototype, "point", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số lượt xem',
        example: 1000,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogSchema.prototype, "viewCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Ảnh tiêu đề',
        example: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
        nullable: true,
    }),
    __metadata("design:type", String)
], BlogSchema.prototype, "thumbnailUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nhãn',
        example: ['nestjs', 'typescript', 'backend'],
        nullable: true,
    }),
    __metadata("design:type", Array)
], BlogSchema.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo (Unix timestamp)',
        example: 1625097600000,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogSchema.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian cập nhật (Unix timestamp)',
        example: 1625097600000,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogSchema.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của tác giả nếu đây là bài viết của người dùng',
        example: 1,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogSchema.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mã của nhân viên nếu đây là bài viết của hệ thống',
        example: 1,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogSchema.prototype, "employeeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nhân viên kiểm duyệt bài viết nếu đây là bài viết của người dùng',
        example: 1,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogSchema.prototype, "employeeModerator", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại của tác giả bài viết',
        example: enums_1.AuthorTypeEnum.USER,
        enum: enums_1.AuthorTypeEnum,
        nullable: true,
    }),
    __metadata("design:type", String)
], BlogSchema.prototype, "authorType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái của bài viết',
        example: enums_1.BlogStatusEnum.APPROVED,
        enum: enums_1.BlogStatusEnum,
    }),
    __metadata("design:type", String)
], BlogSchema.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái hiển thị của bài viết',
        example: true,
    }),
    __metadata("design:type", Boolean)
], BlogSchema.prototype, "enable", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số lượt like',
        example: 500,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogSchema.prototype, "like", void 0);
class BlogListResponseSchema {
    items;
    meta;
}
exports.BlogListResponseSchema = BlogListResponseSchema;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách bài viết',
        type: [BlogSchema],
    }),
    __metadata("design:type", Array)
], BlogListResponseSchema.prototype, "items", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin phân trang',
        type: 'object',
        properties: {
            totalItems: {
                type: 'number',
                example: 100,
                description: 'Tổng số bài viết',
            },
            itemCount: {
                type: 'number',
                example: 10,
                description: 'Số bài viết trên trang hiện tại',
            },
            itemsPerPage: {
                type: 'number',
                example: 10,
                description: 'Số bài viết trên mỗi trang',
            },
            totalPages: {
                type: 'number',
                example: 10,
                description: 'Tổng số trang',
            },
            currentPage: {
                type: 'number',
                example: 1,
                description: 'Trang hiện tại',
            },
        },
    }),
    __metadata("design:type", Object)
], BlogListResponseSchema.prototype, "meta", void 0);
//# sourceMappingURL=blog.schema.js.map