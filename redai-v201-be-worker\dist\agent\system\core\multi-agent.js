"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "workflow", {
    enumerable: true,
    get: function() {
        return workflow;
    }
});
const _langgraph = require("@langchain/langgraph");
const _reactagentexecutor = require("./react-agent-executor");
const _messages = require("@langchain/core/messages");
const _constants = require("./constants");
const _messagetrimmer = require("./message-trimmer");
const _checkpointsaver = require("./checkpoint-saver");
const _common = require("@nestjs/common");
const _universal = require("langchain/chat_models/universal");
const logger = new _common.Logger('MultiAgent');
const supervisorGraph = (0, _reactagentexecutor.createReactAgent)({
    checkpointSaver: _checkpointsaver.saver
}).withConfig({
    tags: [
        _constants.SUPERVISOR_TAG
    ]
});
const workerGraph = (0, _reactagentexecutor.createReactAgent)({
    checkpointSaver: _checkpointsaver.saver
}).withConfig({
    tags: [
        _constants.WORKER_TAG
    ]
});
function makeCallWorker(worker) {
    return async (state, config)=>{
        const activeAgentId = state.activeAgent;
        const agentConfig = config?.configurable?.agentConfigMap?.[activeAgentId];
        if (!agentConfig) {
            throw new Error(`No configuration found for agent: ${activeAgentId}`);
        }
        const input = state.messages.filter((msg)=>((0, _messages.isAIMessage)(msg) || (0, _messages.isToolMessage)(msg)) && msg.response_metadata?.invoker === agentConfig.id);
        const workerState = {
            messages: input,
            activeAgent: activeAgentId,
            metadata: state.metadata
        };
        const output = await worker.invoke(workerState, config);
        return {
            messages: [
                output.messages.at(-1),
                new _messages.HumanMessage(`Worker finishes, decide whether to continue working with the workers or response to the user`)
            ],
            activeAgent: config.configurable?.supervisorAgentId,
            isSupervisor: true
        };
    };
}
const messageTrimmerNode = async (state, config)=>{
    logger.debug('reached mesage trimmer node');
    logger.debug(`before trimming: ${state.messages.length}`);
    const { messages } = state;
    logger.warn('extract messages state');
    const activeAgentId = state.activeAgent;
    const agentConfig = config?.configurable?.agentConfigMap?.[activeAgentId];
    if (!agentConfig) {
        logger.debug('no worker config, skip');
        return {
            messages: []
        }; // Return empty messages array instead of empty object
    }
    if (agentConfig.id !== config?.configurable?.supervisorAgentId) {
        logger.debug('not supervisor, skip');
        return {
            messages: []
        }; // Return empty messages array instead of empty object
    }
    const { type = _constants.DEFAULT_TRIMMING_STRATEGY, threshold = _constants.DEFAULT_TRIMMING_THRESHOLD } = agentConfig.trimmingConfig;
    const model = await (0, _universal.initChatModel)(`${agentConfig.model.provider.toLowerCase()}:${agentConfig.model.name}`, {});
    const strategy = await (0, _messagetrimmer.trimMessagesWithStrategy)(messages, type, threshold, model);
    // Ensure strategy has a valid messages array
    if (!strategy || !strategy.messages) {
        logger.warn('Trimming strategy returned invalid result, using empty messages array');
        return {
            messages: []
        };
    }
    logger.debug(`removed ${strategy.messages ? strategy.messages.filter((x)=>x instanceof _messages.RemoveMessage).length : 0} messages`);
    // Final safety check: ensure we're returning a valid object with messages array
    if (!strategy || typeof strategy !== 'object' || !Array.isArray(strategy.messages)) {
        logger.error('Message trimmer returned invalid format, using empty messages array');
        return {
            messages: []
        };
    }
    return strategy;
};
const workflow = new _langgraph.StateGraph(_reactagentexecutor.GraphState).addNode('supervisor', supervisorGraph, {
    ends: [
        'worker',
        'messageTrimmer'
    ]
}).addNode('worker', makeCallWorker(workerGraph), {
    subgraphs: [
        workerGraph
    ]
}).addNode('messageTrimmer', messageTrimmerNode).addEdge(_langgraph.START, 'supervisor').addEdge('worker', 'supervisor').addEdge('supervisor', 'messageTrimmer').addEdge('messageTrimmer', _langgraph.END).compile({
    checkpointer: _checkpointsaver.saver
});

//# sourceMappingURL=multi-agent.js.map