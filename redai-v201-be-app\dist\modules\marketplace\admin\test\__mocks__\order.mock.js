"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mockPaginatedOrderResponseDto = exports.mockOrderResponseDto = exports.mockOrders = exports.mockOrder = void 0;
exports.mockOrder = {
    id: 1,
    userId: 1,
    totalPoint: 1600,
    user: {
        id: 1,
        fullName: 'Nguyễn <PERSON>n <PERSON>',
        email: '<EMAIL>',
    },
    orderLines: [
        {
            id: 1,
            productId: 1,
            point: 800,
            productName: 'Sản phẩm 1',
            platformFeePercent: 5.0,
            sellerReceivePrice: 760,
            quantity: 2,
            createdAt: 1625097600000,
            updatedAt: 1625097600000,
            product: {
                id: 1,
                name: 'Sản phẩm 1',
                description: '<PERSON><PERSON> tả sản phẩm 1',
                listedPrice: 1000,
                discountedPrice: 800,
                category: 'AGENT',
                status: 'APPROVED',
                userId: 2,
                user: {
                    id: 2,
                    fullName: '<PERSON><PERSON><PERSON><PERSON> b<PERSON>',
                    email: '<EMAIL>',
                },
                employee: {},
                images: [{ key: 'image1.jpg', position: 0 }],
                createdAt: 1625097600000,
                updatedAt: 1625097600000,
            },
            order: {},
        },
    ],
    createdAt: 1625097600000,
    updatedAt: 1625097600000,
};
exports.mockOrders = [
    exports.mockOrder,
    {
        id: 2,
        userId: 2,
        totalPoint: 2000,
        user: {
            id: 2,
            fullName: 'Nguyễn Văn B',
            email: '<EMAIL>',
        },
        orderLines: [],
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
    },
];
exports.mockOrderResponseDto = {
    id: 1,
    user: {
        id: 1,
        name: 'Nguyễn Văn A',
        email: '<EMAIL>',
        avatar: null,
    },
    orderLines: [
        {
            id: 1,
            productId: 1,
            productName: 'Sản phẩm 1',
            point: 800,
            quantity: 2,
            platformFeePercent: 5.0,
        },
    ],
    totalAmount: 1600,
    createdAt: 1625097600000,
};
exports.mockPaginatedOrderResponseDto = {
    items: [exports.mockOrderResponseDto],
    meta: {
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
    },
};
//# sourceMappingURL=order.mock.js.map