{"version": 3, "sources": ["../../../src/agent/mcp/index.ts"], "sourcesContent": ["import { type StreamableHTTPConnection } from './client.js';\r\n\r\nexport {\r\n  type Connection,\r\n  type StdioConnection,\r\n  type StreamableHTTPConnection,\r\n} from './client.js';\r\n\r\n/**\r\n * Type alias for backward compatibility with previous versions of the package.\r\n */\r\nexport type SSEConnection = StreamableHTTPConnection;\r\n\r\nexport { loadMcpTools } from './tools.js';\r\n"], "names": ["loadMcpTools"], "mappings": ";;;;+BAaSA;;;eAAAA,mBAAY;;;uBAAQ"}