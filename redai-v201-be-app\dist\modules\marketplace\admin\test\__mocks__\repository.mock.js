"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MockRepository = exports.MockSelectQueryBuilder = void 0;
class MockSelectQueryBuilder {
    aliasName;
    constructor(aliasName) {
        this.aliasName = aliasName;
    }
    select() {
        return this;
    }
    addSelect() {
        return this;
    }
    where() {
        return this;
    }
    andWhere() {
        return this;
    }
    orWhere() {
        return this;
    }
    leftJoin() {
        return this;
    }
    leftJoinAndSelect() {
        return this;
    }
    innerJoin() {
        return this;
    }
    innerJoinAndSelect() {
        return this;
    }
    orderBy() {
        return this;
    }
    addOrderBy() {
        return this;
    }
    skip() {
        return this;
    }
    take() {
        return this;
    }
    getOne() {
        return Promise.resolve(null);
    }
    getMany() {
        return Promise.resolve([]);
    }
    getManyAndCount() {
        return Promise.resolve([[], 0]);
    }
    getCount() {
        return Promise.resolve(0);
    }
}
exports.MockSelectQueryBuilder = MockSelectQueryBuilder;
class MockRepository {
    createQueryBuilder(alias) {
        return new MockSelectQueryBuilder(alias);
    }
    findOne() {
        return Promise.resolve(null);
    }
    find() {
        return Promise.resolve([]);
    }
    save(entity) {
        return Promise.resolve(entity);
    }
    create(entityLike) {
        return entityLike;
    }
    delete() {
        return Promise.resolve({ affected: 1 });
    }
    update() {
        return Promise.resolve({ affected: 1 });
    }
}
exports.MockRepository = MockRepository;
//# sourceMappingURL=repository.mock.js.map