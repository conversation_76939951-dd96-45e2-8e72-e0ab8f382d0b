"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CHAT_ERROR_CODES = void 0;
const common_1 = require("@nestjs/common");
const exceptions_1 = require("../../../common/exceptions");
exports.CHAT_ERROR_CODES = {
    INVALID_INPUT: new exceptions_1.ErrorCode(40000, 'Invalid input data for chat operation', common_1.HttpStatus.BAD_REQUEST),
    THREAD_VALIDATION_FAILED: new exceptions_1.ErrorCode(40001, 'Thread validation failed', common_1.HttpStatus.BAD_REQUEST),
    THREAD_NOT_FOUND: new exceptions_1.ErrorCode(40002, 'Conversation thread not found', common_1.HttpStatus.NOT_FOUND),
    THREAD_FETCH_FAILED: new exceptions_1.ErrorCode(40003, 'Failed to fetch conversation thread', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    THREAD_CREATION_FAILED: new exceptions_1.ErrorCode(40004, 'Failed to create conversation thread', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    THREAD_UPDATE_FAILED: new exceptions_1.ErrorCode(40005, 'Failed to update conversation thread', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    THREAD_DELETE_FAILED: new exceptions_1.ErrorCode(40006, 'Failed to delete conversation thread', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    THREAD_ACCESS_DENIED: new exceptions_1.ErrorCode(40007, 'Access denied to conversation thread', common_1.HttpStatus.FORBIDDEN),
    MESSAGES_FETCH_FAILED: new exceptions_1.ErrorCode(40008, 'Failed to fetch thread messages', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    MESSAGE_NOT_FOUND: new exceptions_1.ErrorCode(40009, 'Message not found', common_1.HttpStatus.NOT_FOUND),
    STREAM_CONNECTION_FAILED: new exceptions_1.ErrorCode(40010, 'Failed to establish stream connection', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    STREAM_READ_FAILED: new exceptions_1.ErrorCode(40011, 'Failed to read from stream', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
};
//# sourceMappingURL=chat-error-codes.js.map