"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserDataFineTuneMapper = void 0;
const utils_1 = require("../../../../shared/utils");
class UserDataFineTuneMapper {
    static toResponseDto(entity, cdnService) {
        return {
            id: entity.id,
            name: entity.name,
            description: entity.description,
            createdAt: entity.createdAt,
            status: entity.status,
        };
    }
    static async toDetailResponseDto(entity, cdnService) {
        let trainDatasetUrl = '';
        let validDatasetUrl = null;
        try {
            if (entity.trainDataset) {
                const trainUrl = cdnService.generateUrlView(entity.trainDataset, utils_1.TimeIntervalEnum.ONE_HOUR);
                trainDatasetUrl = trainUrl || '';
            }
            if (entity.validDataset) {
                const validUrl = cdnService.generateUrlView(entity.validDataset, utils_1.TimeIntervalEnum.ONE_HOUR);
                validDatasetUrl = validUrl || null;
            }
        }
        catch (error) {
            console.warn(`Lỗi tạo URL CDN cho dataset ${entity.id}:`, error.message);
        }
        return {
            id: entity.id,
            name: entity.name,
            description: entity.description,
            createdAt: entity.createdAt,
            status: entity.status,
            trainDatasetUrl,
            validDatasetUrl,
        };
    }
    static toResponseDtoList(entities, cdnService) {
        return entities.map(entity => this.toResponseDto(entity, cdnService));
    }
}
exports.UserDataFineTuneMapper = UserDataFineTuneMapper;
//# sourceMappingURL=user-data-fine-tune.mapper.js.map