"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FormStatus = exports.ComponentVariant = exports.ComponentSize = exports.ComponentType = void 0;
var ComponentType;
(function (ComponentType) {
    ComponentType["TEXT"] = "text";
    ComponentType["TEXTAREA"] = "textarea";
    ComponentType["SELECT"] = "select";
    ComponentType["MULTISELECT"] = "multiselect";
    ComponentType["CHECKBOX"] = "checkbox";
    ComponentType["RADIO"] = "radio";
    ComponentType["DATE"] = "date";
    ComponentType["TIME"] = "time";
    ComponentType["DATETIME"] = "datetime";
    ComponentType["NUMBER"] = "number";
    ComponentType["SLIDER"] = "slider";
    ComponentType["SWITCH"] = "switch";
    ComponentType["FILE"] = "file";
    ComponentType["COLOR"] = "color";
    ComponentType["AUTOCOMPLETE"] = "autocomplete";
    ComponentType["RATING"] = "rating";
    ComponentType["BUTTON"] = "button";
})(ComponentType || (exports.ComponentType = ComponentType = {}));
var ComponentSize;
(function (ComponentSize) {
    ComponentSize["SMALL"] = "small";
    ComponentSize["MEDIUM"] = "medium";
    ComponentSize["LARGE"] = "large";
})(ComponentSize || (exports.ComponentSize = ComponentSize = {}));
var ComponentVariant;
(function (ComponentVariant) {
    ComponentVariant["STANDARD"] = "standard";
    ComponentVariant["OUTLINED"] = "outlined";
    ComponentVariant["FILLED"] = "filled";
})(ComponentVariant || (exports.ComponentVariant = ComponentVariant = {}));
var FormStatus;
(function (FormStatus) {
    FormStatus["DRAFT"] = "draft";
    FormStatus["SUBMITTED"] = "submitted";
    FormStatus["APPROVED"] = "approved";
    FormStatus["REJECTED"] = "rejected";
})(FormStatus || (exports.FormStatus = FormStatus = {}));
//# sourceMappingURL=generic.dto.js.map