"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnowledgeFileUserServiceMock = void 0;
const knowledge_file_status_enum_1 = require("./__mocks__/@modules/data/knowledge-files/enums/knowledge-file-status.enum");
const owner_type_enum_1 = require("./__mocks__/@modules/email/entitys/enums/owner-type.enum");
const file_media_type_util_1 = require("./__mocks__/@shared/utils/file/file-media-type.util");
const file_1 = require("./__mocks__/@shared/utils/file");
const common_1 = require("@nestjs/common");
const app_exception_1 = require("./__mocks__/@shared/exceptions/app.exception");
const exceptions_1 = require("./__mocks__/@modules/data/knowledge-files/user/exceptions");
class KnowledgeFileUserServiceMock {
    async batchCreateFiles(dto, userId) {
        const files = dto.files;
        if (files.some(file => file.mime === 'invalid/mime')) {
            throw new common_1.BadRequestException('Loại file không được hỗ trợ');
        }
        if (files.some(file => file.name === 'error.pdf')) {
            throw new app_exception_1.AppException(exceptions_1.KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_CREATE_ERROR);
        }
        return {
            success: true,
            data: files.map((file, index) => ({
                id: `file${index + 1}`,
                name: file.name,
                storage: file.storage,
                status: knowledge_file_status_enum_1.KnowledgeFileStatus.DRAFT,
                ownerType: owner_type_enum_1.OwnerType.USER,
                ownedBy: userId,
                createdAt: Date.now(),
            })),
        };
    }
    async getFiles(queryDto, userId) {
        const mockFiles = [
            {
                id: 'file1',
                name: 'test.pdf',
                storageKey: 'knowledge/test.pdf',
                ownerType: owner_type_enum_1.OwnerType.USER,
                ownedBy: userId,
                storage: 1024,
                createdAt: Date.now(),
                status: knowledge_file_status_enum_1.KnowledgeFileStatus.DRAFT,
                extension: 'pdf',
                viewUrl: 'https://cdn.example.com/knowledge/test.pdf',
            },
            {
                id: 'file2',
                name: 'test.docx',
                storageKey: 'knowledge/test.docx',
                ownerType: owner_type_enum_1.OwnerType.USER,
                ownedBy: userId,
                storage: 2048,
                createdAt: Date.now(),
                status: knowledge_file_status_enum_1.KnowledgeFileStatus.DRAFT,
                extension: 'docx',
                viewUrl: 'https://cdn.example.com/knowledge/test.docx',
            },
        ];
        if (queryDto.search) {
            return {
                items: mockFiles.filter(file => file.name.includes(queryDto.search)),
                meta: {
                    totalItems: mockFiles.filter(file => file.name.includes(queryDto.search)).length,
                    itemsPerPage: queryDto.limit,
                    currentPage: queryDto.page,
                },
            };
        }
        if (queryDto.extension) {
            return {
                items: mockFiles.filter(file => file.name.endsWith(queryDto.extension)),
                meta: {
                    totalItems: mockFiles.filter(file => file.name.endsWith(queryDto.extension)).length,
                    itemsPerPage: queryDto.limit,
                    currentPage: queryDto.page,
                },
            };
        }
        if (queryDto.vectorStoreId === 'error') {
            throw new app_exception_1.AppException(exceptions_1.KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_LIST_ERROR);
        }
        return {
            items: mockFiles,
            meta: {
                totalItems: mockFiles.length,
                itemsPerPage: queryDto.limit,
                currentPage: queryDto.page,
            },
        };
    }
    async deleteFile(fileId, userId) {
        if (fileId === 'non_existent') {
            throw new common_1.NotFoundException(`Không tìm thấy file với id ${fileId}`);
        }
        if (fileId === 'vector_store_error') {
            throw new app_exception_1.AppException(exceptions_1.KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_DELETE_ERROR, 'Lỗi khi xóa liên kết vector store');
        }
        if (fileId === 'openai_error') {
            throw new app_exception_1.AppException(exceptions_1.KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_DELETE_ERROR, 'Lỗi khi xóa file từ OpenAI');
        }
        if (fileId === 's3_error') {
            throw new app_exception_1.AppException(exceptions_1.KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_DELETE_ERROR, 'Lỗi khi xóa file từ S3');
        }
        if (fileId === 'repository_error') {
            throw new app_exception_1.AppException(exceptions_1.KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_DELETE_ERROR, 'Lỗi khi xóa file từ repository');
        }
        return { success: true };
    }
    getFileExtensionFromMime(mimeType) {
        switch (mimeType) {
            case file_media_type_util_1.FileTypeEnum.PDF:
                return '.pdf';
            case file_media_type_util_1.FileTypeEnum.DOCX:
                return '.docx';
            case file_media_type_util_1.FileTypeEnum.TXT:
                return '.txt';
            default:
                throw new common_1.BadRequestException('Loại file không được hỗ trợ');
        }
    }
    getCategoryFromMimeType(mimeType) {
        switch (mimeType) {
            case file_media_type_util_1.FileTypeEnum.PDF:
            case file_media_type_util_1.FileTypeEnum.DOCX:
            case file_media_type_util_1.FileTypeEnum.TXT:
                return file_1.CategoryFolderEnum.DOCUMENT;
            default:
                throw new common_1.BadRequestException('Loại file không được hỗ trợ');
        }
    }
    async checkVectorStoreExists(vectorStoreId) {
        if (vectorStoreId === 'non_existent_vs') {
            throw new common_1.NotFoundException(`Không tìm thấy vector store với id ${vectorStoreId}`);
        }
        if (vectorStoreId === 'db_error') {
            throw new Error('Database connection error');
        }
        return { id: vectorStoreId, name: 'Test Vector Store' };
    }
    getSortColumn(sortField) {
        if (!sortField) {
            return 'createdAt';
        }
        const lowerCaseField = sortField.toLowerCase();
        switch (lowerCaseField) {
            case 'name':
                return 'name';
            case 'createdat':
                return 'createdAt';
            case 'storage':
                return 'storage';
            default:
                return 'createdAt';
        }
    }
}
exports.KnowledgeFileUserServiceMock = KnowledgeFileUserServiceMock;
//# sourceMappingURL=knowledge-file-user.service.mock.js.map