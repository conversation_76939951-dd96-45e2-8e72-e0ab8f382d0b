"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runPatternMatchingBenchmark = runPatternMatchingBenchmark;
const core_1 = require("@nestjs/core");
const app_module_1 = require("../../../app.module");
const pattern_matching_engine_service_1 = require("../services/pattern-matching-engine.service");
const performance_monitor_service_1 = require("../services/performance-monitor.service");
async function runPatternMatchingBenchmark() {
    console.log('🚀 Starting Pattern Matching Performance Benchmark...\n');
    try {
        const app = await core_1.NestFactory.createApplicationContext(app_module_1.AppModule);
        const patternEngine = app.get(pattern_matching_engine_service_1.PatternMatchingEngineService);
        const performanceMonitor = app.get(performance_monitor_service_1.PerformanceMonitorService);
        console.log('✅ Application context initialized\n');
        const testScenarios = [
            { modelCount: 10, patternCount: 5, description: 'Small scale' },
            { modelCount: 50, patternCount: 10, description: 'Medium scale' },
            { modelCount: 100, patternCount: 20, description: 'Large scale' },
            { modelCount: 500, patternCount: 50, description: 'Very large scale' }
        ];
        for (const scenario of testScenarios) {
            console.log(`📊 Testing ${scenario.description}: ${scenario.modelCount} models vs ${scenario.patternCount} patterns`);
            const models = generateTestModels(scenario.modelCount);
            const patterns = generateTestPatterns(scenario.patternCount);
            const oldResult = await testOldPatternMatching(models, patterns, performanceMonitor, scenario.description);
            const newResult = await testNewPatternMatching(models, patterns, patternEngine, performanceMonitor, scenario.description);
            compareResults(oldResult, newResult, scenario);
            console.log('---\n');
        }
        console.log('✅ Pattern matching benchmark completed successfully!');
    }
    catch (error) {
        console.error('❌ Benchmark failed:', error.message);
        console.error(error.stack);
    }
}
async function testOldPatternMatching(models, patterns, monitor, scenario) {
    const operationId = `old_pattern_matching_${scenario}_${Date.now()}`;
    monitor.startMonitoring(operationId, {
        testType: 'old_pattern_matching',
        scenario,
        modelCount: models.length,
        patternCount: patterns.length
    });
    const matches = [];
    for (const modelId of models) {
        const matchedPatterns = patterns
            .filter(pattern => isModelMatchPatternOld(modelId, pattern.pattern))
            .sort((a, b) => a.pattern.length - b.pattern.length);
        const bestMatch = matchedPatterns[0];
        matches.push({
            modelId,
            registryId: bestMatch?.id || null,
            matchedPattern: bestMatch?.pattern || null
        });
    }
    const metrics = monitor.stopMonitoring(operationId);
    return {
        duration: metrics?.duration || 0,
        matches,
        complexity: 'O(N×M×log M)'
    };
}
async function testNewPatternMatching(models, patterns, engine, monitor, scenario) {
    const operationId = `new_pattern_matching_${scenario}_${Date.now()}`;
    monitor.startMonitoring(operationId, {
        testType: 'new_pattern_matching',
        scenario,
        modelCount: models.length,
        patternCount: patterns.length
    });
    const compiledPatterns = engine.compilePatterns(patterns.map(p => ({ id: p.id, modelNamePattern: p.pattern })));
    const matches = engine.matchModels(models, compiledPatterns);
    const metrics = monitor.stopMonitoring(operationId);
    return {
        duration: metrics?.duration || 0,
        matches,
        complexity: 'O(N×log M)'
    };
}
function isModelMatchPatternOld(modelId, pattern) {
    const regexPattern = pattern
        .replace(/\*/g, '.*')
        .replace(/\?/g, '.');
    const regex = new RegExp(`^${regexPattern}$`, 'i');
    return regex.test(modelId);
}
function compareResults(oldResult, newResult, scenario) {
    const speedup = oldResult.duration / newResult.duration;
    const oldMatches = oldResult.matches.filter((m) => m.registryId !== null).length;
    const newMatches = newResult.matches.size;
    console.log(`📈 Results for ${scenario.description}:`);
    console.log(`   Old Implementation (${oldResult.complexity}):`);
    console.log(`     Duration: ${oldResult.duration}ms`);
    console.log(`     Matches: ${oldMatches}/${scenario.modelCount}`);
    console.log(`     Avg time per model: ${(oldResult.duration / scenario.modelCount).toFixed(2)}ms`);
    console.log(`   New Implementation (${newResult.complexity}):`);
    console.log(`     Duration: ${newResult.duration}ms`);
    console.log(`     Matches: ${newMatches}/${scenario.modelCount}`);
    console.log(`     Avg time per model: ${(newResult.duration / scenario.modelCount).toFixed(2)}ms`);
    console.log(`   🚀 Performance Improvement:`);
    console.log(`     Speedup: ${speedup.toFixed(2)}x faster`);
    console.log(`     Time saved: ${(oldResult.duration - newResult.duration)}ms`);
    console.log(`     Efficiency gain: ${((speedup - 1) * 100).toFixed(1)}%`);
    if (oldMatches === newMatches) {
        console.log(`   ✅ Correctness: Both implementations found same number of matches`);
    }
    else {
        console.log(`   ⚠️  Correctness: Match count differs (old: ${oldMatches}, new: ${newMatches})`);
    }
}
function generateTestModels(count) {
    const models = [];
    const prefixes = ['gpt', 'claude', 'llama', 'gemini', 'mistral', 'palm', 'cohere'];
    const versions = ['1', '2', '3', '4', 'turbo', 'pro', 'ultra', 'mini'];
    const suffixes = ['', '-chat', '-instruct', '-base', '-preview'];
    for (let i = 0; i < count; i++) {
        const prefix = prefixes[i % prefixes.length];
        const version = versions[i % versions.length];
        const suffix = suffixes[i % suffixes.length];
        models.push(`${prefix}-${version}${suffix}`);
    }
    return models;
}
function generateTestPatterns(count) {
    const patterns = [];
    const basePatterns = [
        'gpt-*',
        'claude-*',
        'llama-*',
        'gemini-*',
        'mistral-*',
        'gpt-4*',
        'claude-3*',
        '*-chat',
        '*-instruct',
        '*-turbo'
    ];
    for (let i = 0; i < count; i++) {
        const basePattern = basePatterns[i % basePatterns.length];
        patterns.push({
            id: `pattern-${i}`,
            pattern: i < basePatterns.length ? basePattern : `custom-pattern-${i}-*`
        });
    }
    return patterns;
}
if (require.main === module) {
    runPatternMatchingBenchmark()
        .then(() => {
        console.log('🎉 Pattern matching benchmark completed!');
        process.exit(0);
    })
        .catch((error) => {
        console.error('💥 Benchmark failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=pattern-matching-benchmark.js.map