"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelFeature = exports.SamplingParameter = exports.OutputModality = exports.InputModality = exports.ModelProviderEnum = void 0;
var ModelProviderEnum;
(function (ModelProviderEnum) {
    ModelProviderEnum["OPENAI"] = "OPENAI";
    ModelProviderEnum["XAI"] = "XAI";
    ModelProviderEnum["ANTHROPIC"] = "ANTHROPIC";
    ModelProviderEnum["GOOGLE"] = "GOOGLE";
    ModelProviderEnum["DEEPSEEK"] = "DEEPSEEK";
})(ModelProviderEnum || (exports.ModelProviderEnum = ModelProviderEnum = {}));
var InputModality;
(function (InputModality) {
    InputModality["TEXT"] = "text";
    InputModality["IMAGE"] = "image";
    InputModality["AUDIO"] = "audio";
    InputModality["VIDEO"] = "video";
})(InputModality || (exports.InputModality = InputModality = {}));
var OutputModality;
(function (OutputModality) {
    OutputModality["TEXT"] = "text";
    OutputModality["IMAGE"] = "image";
    OutputModality["AUDIO"] = "audio";
    OutputModality["VIDEO"] = "video";
})(OutputModality || (exports.OutputModality = OutputModality = {}));
var SamplingParameter;
(function (SamplingParameter) {
    SamplingParameter["TEMPERATURE"] = "temperature";
    SamplingParameter["TOP_P"] = "top_p";
    SamplingParameter["TOP_K"] = "top_k";
    SamplingParameter["MAX_TOKENS"] = "max_tokens";
    SamplingParameter["MAX_OUTPUT_TOKENS"] = "max_output_tokens";
})(SamplingParameter || (exports.SamplingParameter = SamplingParameter = {}));
var ModelFeature;
(function (ModelFeature) {
    ModelFeature["TOOL_CALL"] = "tool_call";
    ModelFeature["PARALLEL_TOOL_CALL"] = "parallel_tool_call";
})(ModelFeature || (exports.ModelFeature = ModelFeature = {}));
//# sourceMappingURL=system-agent-config.interface.js.map