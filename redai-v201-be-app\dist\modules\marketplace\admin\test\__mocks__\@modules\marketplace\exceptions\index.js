"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MARKETPLACE_ERROR_CODES = void 0;
const common_1 = require("@nestjs/common");
const exceptions_1 = require("../../../../../../../../common/exceptions");
exports.MARKETPLACE_ERROR_CODES = {
    PRODUCT_NOT_FOUND: new exceptions_1.ErrorCode(12001, 'Sản phẩm không tồn tại', common_1.HttpStatus.NOT_FOUND),
    INVALID_STATUS: new exceptions_1.ErrorCode(12003, 'Trạng thái sản phẩm không hợp lệ', common_1.HttpStatus.BAD_REQUEST),
    INVALID_PRICE: new exceptions_1.ErrorCode(12003, '<PERSON><PERSON><PERSON> không hợp lệ', common_1.HttpStatus.BAD_REQUEST),
    PRODUCT_FETCH_ERROR: new exceptions_1.ErrorCode(12007, 'Lỗi khi lấy thông tin sản phẩm', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    PRODUCT_STATUS_CHANGE_FAILED: new exceptions_1.ErrorCode(12033, 'Thay đổi trạng thái sản phẩm thất bại', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    PRODUCT_CREATION_FAILED: new exceptions_1.ErrorCode(12008, 'Tạo sản phẩm thất bại', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    PRODUCT_UPDATE_FAILED: new exceptions_1.ErrorCode(12011, 'Cập nhật sản phẩm thất bại', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    PRODUCT_DELETED: new exceptions_1.ErrorCode(12012, 'Sản phẩm đã bị xóa', common_1.HttpStatus.BAD_REQUEST),
    MISSING_REQUIRED_FIELDS: new exceptions_1.ErrorCode(12009, 'Thiếu thông tin bắt buộc', common_1.HttpStatus.BAD_REQUEST),
    FILE_UPLOAD_FAILED: new exceptions_1.ErrorCode(12010, 'Tải lên tệp thất bại', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    ORDER_NOT_FOUND: new exceptions_1.ErrorCode(12002, 'Đơn hàng không tồn tại', common_1.HttpStatus.NOT_FOUND),
    CART_NOT_FOUND: new exceptions_1.ErrorCode(12004, 'Giỏ hàng không tồn tại', common_1.HttpStatus.NOT_FOUND),
    CART_RETRIEVAL_FAILED: new exceptions_1.ErrorCode(12005, 'Lỗi khi lấy thông tin giỏ hàng', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    GENERAL_ERROR: new exceptions_1.ErrorCode(12099, 'Lỗi hệ thống', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
};
//# sourceMappingURL=index.js.map