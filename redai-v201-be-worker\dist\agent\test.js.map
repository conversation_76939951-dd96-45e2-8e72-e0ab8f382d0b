{"version": 3, "sources": ["../../src/agent/test.ts"], "sourcesContent": ["import {\r\n  Annotation,\r\n  interrupt,\r\n  MemorySaver,\r\n  Messages,\r\n  messagesStateReducer,\r\n  START,\r\n  StateGraph,\r\n} from '@langchain/langgraph';\r\nimport { BaseMessage } from '@langchain/core/messages';\r\nimport { Logger } from '@nestjs/common';\r\n\r\nconst logger = new Logger('Test');\r\n\r\nconst graphState = Annotation.Root({\r\n  messages: Annotation<BaseMessage[], Messages>({\r\n    reducer: messagesStateReducer,\r\n    default: () => [],\r\n  }),\r\n});\r\n\r\nfunction sleep(ms: number) {\r\n  return new Promise((resolve) => setTimeout(resolve, ms));\r\n}\r\n\r\nconst stateGraph = new StateGraph(graphState)\r\n  .addNode('a', async () => {\r\n    logger.log('a');\r\n    await sleep(1000);\r\n    return { messages: [{ role: 'user', content: 'a' }] };\r\n  })\r\n  .addNode('b', async () => {\r\n    logger.log('b');\r\n    await sleep(1000);\r\n    return { messages: [{ role: 'user', content: 'b' }] };\r\n  })\r\n  .addNode('c', async () => {\r\n    logger.log('c');\r\n    await sleep(1000);\r\n    return { messages: [{ role: 'user', content: 'c' }] };\r\n  })\r\n  .addNode('d', async () => {\r\n    logger.log('d');\r\n    await sleep(1000);\r\n    return { messages: [{ role: 'user', content: 'd' }] };\r\n  })\r\n  .addNode('e', async () => {\r\n    logger.log('e');\r\n    await sleep(1000);\r\n    return { messages: [{ role: 'user', content: 'e' }] };\r\n  })\r\n  .addNode('f', async () => {\r\n    logger.log('f');\r\n    await sleep(1000);\r\n    return { messages: [{ role: 'user', content: 'f' }] };\r\n  })\r\n  .addNode('g', async () => {\r\n    logger.log('g');\r\n    await sleep(1000);\r\n    return { messages: [{ role: 'user', content: 'g' }] };\r\n  })\r\n  .addNode('h', async () => {\r\n    logger.log('h');\r\n    interrupt(\r\n      'funny businessfunny businessfunny businessfunny businessfunny businessfunny businessfunny businessfunny businessfunny businessfunny business',\r\n    );\r\n    await sleep(1000);\r\n    return { messages: [{ role: 'user', content: 'h' }] };\r\n  })\r\n  .addNode('i', async () => {\r\n    logger.log('i');\r\n    await sleep(1000);\r\n    return { messages: [{ role: 'user', content: 'i' }] };\r\n  })\r\n  .addNode('j', async () => {\r\n    logger.log('j');\r\n    await sleep(1000);\r\n    return { messages: [{ role: 'user', content: 'j' }] };\r\n  })\r\n  .addEdge(START, 'a')\r\n  .addEdge('a', 'b')\r\n  .addEdge('b', 'c')\r\n  .addEdge('c', 'd')\r\n  .addEdge('d', 'e')\r\n  .addEdge('e', 'f')\r\n  .addEdge('f', 'g')\r\n  .addEdge('g', 'h')\r\n  .addEdge('h', 'i')\r\n  .addEdge('i', 'j')\r\n  .compile({\r\n    checkpointer: new MemorySaver(),\r\n  });\r\n\r\nasync function main() {\r\n  const abortController = new AbortController();\r\n  const threadId = '123';\r\n  let count = 0;\r\n  try {\r\n    const stream = stateGraph.streamEvents(\r\n      { messages: [] },\r\n      {\r\n        configurable: {\r\n          thread_id: threadId,\r\n        },\r\n        version: 'v2',\r\n        signal: abortController.signal,\r\n      },\r\n    );\r\n    for await (const evt of stream) {\r\n      ++count;\r\n    }\r\n  } catch (e) {\r\n    logger.debug('aborted');\r\n  }\r\n  const state = await stateGraph.getState({\r\n    configurable: {\r\n      thread_id: threadId,\r\n    },\r\n  });\r\n  const checkpointId = state?.config?.configurable?.['checkpoint_id'];\r\n  const resumeStream = stateGraph.streamEvents(null, {\r\n    configurable: {\r\n      thread_id: threadId,\r\n      checkpoint_id: checkpointId,\r\n    },\r\n    version: 'v2',\r\n  });\r\n  for await (const evt of resumeStream) {\r\n    const state = await stateGraph.getState({\r\n      configurable: {\r\n        thread_id: threadId,\r\n      },\r\n    });\r\n    // logger.debug(state);\r\n  }\r\n}\r\n\r\n"], "names": ["logger", "<PERSON><PERSON>", "graphState", "Annotation", "Root", "messages", "reducer", "messagesStateReducer", "default", "sleep", "ms", "Promise", "resolve", "setTimeout", "stateGraph", "StateGraph", "addNode", "log", "role", "content", "interrupt", "addEdge", "START", "compile", "checkpointer", "MemorySaver", "main", "abortController", "AbortController", "threadId", "count", "stream", "streamEvents", "configurable", "thread_id", "version", "signal", "evt", "e", "debug", "state", "getState", "checkpointId", "config", "resumeStream", "checkpoint_id"], "mappings": ";;;;2BAQO;wBAEgB;AAEvB,MAAMA,SAAS,IAAIC,cAAM,CAAC;AAE1B,MAAMC,aAAaC,qBAAU,CAACC,IAAI,CAAC;IACjCC,UAAUF,IAAAA,qBAAU,EAA0B;QAC5CG,SAASC,+BAAoB;QAC7BC,SAAS,IAAM,EAAE;IACnB;AACF;AAEA,SAASC,MAAMC,EAAU;IACvB,OAAO,IAAIC,QAAQ,CAACC,UAAYC,WAAWD,SAASF;AACtD;AAEA,MAAMI,aAAa,IAAIC,qBAAU,CAACb,YAC/Bc,OAAO,CAAC,KAAK;IACZhB,OAAOiB,GAAG,CAAC;IACX,MAAMR,MAAM;IACZ,OAAO;QAAEJ,UAAU;YAAC;gBAAEa,MAAM;gBAAQC,SAAS;YAAI;SAAE;IAAC;AACtD,GACCH,OAAO,CAAC,KAAK;IACZhB,OAAOiB,GAAG,CAAC;IACX,MAAMR,MAAM;IACZ,OAAO;QAAEJ,UAAU;YAAC;gBAAEa,MAAM;gBAAQC,SAAS;YAAI;SAAE;IAAC;AACtD,GACCH,OAAO,CAAC,KAAK;IACZhB,OAAOiB,GAAG,CAAC;IACX,MAAMR,MAAM;IACZ,OAAO;QAAEJ,UAAU;YAAC;gBAAEa,MAAM;gBAAQC,SAAS;YAAI;SAAE;IAAC;AACtD,GACCH,OAAO,CAAC,KAAK;IACZhB,OAAOiB,GAAG,CAAC;IACX,MAAMR,MAAM;IACZ,OAAO;QAAEJ,UAAU;YAAC;gBAAEa,MAAM;gBAAQC,SAAS;YAAI;SAAE;IAAC;AACtD,GACCH,OAAO,CAAC,KAAK;IACZhB,OAAOiB,GAAG,CAAC;IACX,MAAMR,MAAM;IACZ,OAAO;QAAEJ,UAAU;YAAC;gBAAEa,MAAM;gBAAQC,SAAS;YAAI;SAAE;IAAC;AACtD,GACCH,OAAO,CAAC,KAAK;IACZhB,OAAOiB,GAAG,CAAC;IACX,MAAMR,MAAM;IACZ,OAAO;QAAEJ,UAAU;YAAC;gBAAEa,MAAM;gBAAQC,SAAS;YAAI;SAAE;IAAC;AACtD,GACCH,OAAO,CAAC,KAAK;IACZhB,OAAOiB,GAAG,CAAC;IACX,MAAMR,MAAM;IACZ,OAAO;QAAEJ,UAAU;YAAC;gBAAEa,MAAM;gBAAQC,SAAS;YAAI;SAAE;IAAC;AACtD,GACCH,OAAO,CAAC,KAAK;IACZhB,OAAOiB,GAAG,CAAC;IACXG,IAAAA,oBAAS,EACP;IAEF,MAAMX,MAAM;IACZ,OAAO;QAAEJ,UAAU;YAAC;gBAAEa,MAAM;gBAAQC,SAAS;YAAI;SAAE;IAAC;AACtD,GACCH,OAAO,CAAC,KAAK;IACZhB,OAAOiB,GAAG,CAAC;IACX,MAAMR,MAAM;IACZ,OAAO;QAAEJ,UAAU;YAAC;gBAAEa,MAAM;gBAAQC,SAAS;YAAI;SAAE;IAAC;AACtD,GACCH,OAAO,CAAC,KAAK;IACZhB,OAAOiB,GAAG,CAAC;IACX,MAAMR,MAAM;IACZ,OAAO;QAAEJ,UAAU;YAAC;gBAAEa,MAAM;gBAAQC,SAAS;YAAI;SAAE;IAAC;AACtD,GACCE,OAAO,CAACC,gBAAK,EAAE,KACfD,OAAO,CAAC,KAAK,KACbA,OAAO,CAAC,KAAK,KACbA,OAAO,CAAC,KAAK,KACbA,OAAO,CAAC,KAAK,KACbA,OAAO,CAAC,KAAK,KACbA,OAAO,CAAC,KAAK,KACbA,OAAO,CAAC,KAAK,KACbA,OAAO,CAAC,KAAK,KACbA,OAAO,CAAC,KAAK,KACbE,OAAO,CAAC;IACPC,cAAc,IAAIC,sBAAW;AAC/B;AAEF,eAAeC;IACb,MAAMC,kBAAkB,IAAIC;IAC5B,MAAMC,WAAW;IACjB,IAAIC,QAAQ;IACZ,IAAI;QACF,MAAMC,SAASjB,WAAWkB,YAAY,CACpC;YAAE3B,UAAU,EAAE;QAAC,GACf;YACE4B,cAAc;gBACZC,WAAWL;YACb;YACAM,SAAS;YACTC,QAAQT,gBAAgBS,MAAM;QAChC;QAEF,WAAW,MAAMC,OAAON,OAAQ;YAC9B,EAAED;QACJ;IACF,EAAE,OAAOQ,GAAG;QACVtC,OAAOuC,KAAK,CAAC;IACf;IACA,MAAMC,QAAQ,MAAM1B,WAAW2B,QAAQ,CAAC;QACtCR,cAAc;YACZC,WAAWL;QACb;IACF;IACA,MAAMa,eAAeF,OAAOG,QAAQV,cAAc,CAAC,gBAAgB;IACnE,MAAMW,eAAe9B,WAAWkB,YAAY,CAAC,MAAM;QACjDC,cAAc;YACZC,WAAWL;YACXgB,eAAeH;QACjB;QACAP,SAAS;IACX;IACA,WAAW,MAAME,OAAOO,aAAc;QACpC,MAAMJ,QAAQ,MAAM1B,WAAW2B,QAAQ,CAAC;YACtCR,cAAc;gBACZC,WAAWL;YACb;QACF;IACA,uBAAuB;IACzB;AACF"}