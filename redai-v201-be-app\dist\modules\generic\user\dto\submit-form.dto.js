"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubmitFormDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class SubmitFormDto {
    data;
}
exports.SubmitFormDto = SubmitFormDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Dữ liệu form được gửi',
        example: {
            name: 'Nguyễn Văn A',
            email: '<EMAIL>',
            phone: '0901234567',
            message: 'Tôi muốn biết thêm thông tin về sản phẩm của công ty.',
        },
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Dữ liệu form không được để trống' }),
    (0, class_validator_1.IsObject)({ message: 'Dữ liệu form phải là đối tượng JSON' }),
    (0, class_transformer_1.Type)(() => Object),
    __metadata("design:type", Object)
], SubmitFormDto.prototype, "data", void 0);
//# sourceMappingURL=submit-form.dto.js.map