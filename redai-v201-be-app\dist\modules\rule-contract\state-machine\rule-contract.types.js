"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RuleContractEvent = exports.RuleContractState = void 0;
exports.mapStateToStatus = mapStateToStatus;
exports.mapStatusToState = mapStatusToState;
const rule_contract_entity_1 = require("../entities/rule-contract.entity");
var RuleContractState;
(function (RuleContractState) {
    RuleContractState["DRAFT"] = "draft";
    RuleContractState["PENDING_APPROVAL"] = "pendingApproval";
    RuleContractState["APPROVED"] = "approved";
    RuleContractState["REJECTED"] = "rejected";
})(RuleContractState || (exports.RuleContractState = RuleContractState = {}));
var RuleContractEvent;
(function (RuleContractEvent) {
    RuleContractEvent["CREATE"] = "CREATE";
    RuleContractEvent["SIGN_BY_USER"] = "SIGN_BY_USER";
    RuleContractEvent["APPROVE"] = "APPROVE";
    RuleContractEvent["REJECT"] = "REJECT";
    RuleContractEvent["RESUBMIT"] = "RESUBMIT";
    RuleContractEvent["UPGRADE_TO_BUSINESS"] = "UPGRADE_TO_BUSINESS";
})(RuleContractEvent || (exports.RuleContractEvent = RuleContractEvent = {}));
function mapStateToStatus(state) {
    switch (state) {
        case RuleContractState.DRAFT:
            return rule_contract_entity_1.ContractStatusEnum.DRAFT;
        case RuleContractState.PENDING_APPROVAL:
            return rule_contract_entity_1.ContractStatusEnum.PENDING_APPROVAL;
        case RuleContractState.APPROVED:
            return rule_contract_entity_1.ContractStatusEnum.APPROVED;
        case RuleContractState.REJECTED:
            return rule_contract_entity_1.ContractStatusEnum.REJECTED;
        default:
            return rule_contract_entity_1.ContractStatusEnum.DRAFT;
    }
}
function mapStatusToState(status) {
    switch (status) {
        case rule_contract_entity_1.ContractStatusEnum.DRAFT:
            return RuleContractState.DRAFT;
        case rule_contract_entity_1.ContractStatusEnum.PENDING_APPROVAL:
            return RuleContractState.PENDING_APPROVAL;
        case rule_contract_entity_1.ContractStatusEnum.APPROVED:
            return RuleContractState.APPROVED;
        case rule_contract_entity_1.ContractStatusEnum.REJECTED:
            return RuleContractState.REJECTED;
        default:
            return RuleContractState.DRAFT;
    }
}
//# sourceMappingURL=rule-contract.types.js.map