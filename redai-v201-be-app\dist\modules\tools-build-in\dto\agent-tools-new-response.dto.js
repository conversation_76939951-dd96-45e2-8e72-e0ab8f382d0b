"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentToolsNewResponseDto = exports.NewToolResponseDto = exports.ToolExtraDto = exports.ToolParameterDto = exports.ToolTypeEnum = void 0;
const swagger_1 = require("@nestjs/swagger");
var ToolTypeEnum;
(function (ToolTypeEnum) {
    ToolTypeEnum["IN_SYSTEM"] = "IN_SYSTEM";
    ToolTypeEnum["OUT_SYSTEM"] = "OUT_SYSTEM";
})(ToolTypeEnum || (exports.ToolTypeEnum = ToolTypeEnum = {}));
class ToolParameterDto {
    name;
    description;
    inputSchema;
}
exports.ToolParameterDto = ToolParameterDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên tool',
        example: 'tool_search',
    }),
    __metadata("design:type", String)
], ToolParameterDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả tool',
        example: 'Công cụ giúp tìm kiếm thông tin từ nhiều nguồn',
    }),
    __metadata("design:type", String)
], ToolParameterDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Schema đầu vào của tool',
        example: {
            type: 'object',
            required: ['query'],
            properties: {
                query_param: {
                    type: 'object',
                    description: '',
                    properties: {
                        search: {
                            type: 'string',
                            description: 'search của cart item; example: product 1',
                        },
                        require: ['search'],
                    },
                },
                path_param: {
                    type: 'object',
                    description: '',
                    properties: {
                        id: {
                            type: 'number',
                            description: 'search của cart item; example: product 1',
                        },
                        require: ['search'],
                    },
                },
                body: {
                    type: 'object',
                    description: '',
                    properties: {
                        toolId: {
                            type: 'array',
                            description: 'search của cart item; example: product 1',
                            items: {
                                type: 'string',
                            },
                        },
                        require: ['search'],
                    },
                },
            },
            description: 'Tham số đầu vào cho công cụ tìm kiếm',
        },
    }),
    __metadata("design:type", Object)
], ToolParameterDto.prototype, "inputSchema", void 0);
class ToolExtraDto {
    url;
    headers;
    method;
}
exports.ToolExtraDto = ToolExtraDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'URL đầy đủ của API',
        example: 'http://localhost:3003/api/v1/agent',
    }),
    __metadata("design:type", String)
], ToolExtraDto.prototype, "url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Headers cần thiết',
        example: {
            'x-api-key': 'redai_uuXkRZH0AIjWeqWUTv+5GA==:e9jt8wvIRDDVJkCBSkIWFZrhtJc4oA2TdAHhrUhJBJF4ZM8H3Yx9oYLP1zQQszhT1SrPn6312rMdK+uT/Zkhsg==',
        },
        required: false,
    }),
    __metadata("design:type", Object)
], ToolExtraDto.prototype, "headers", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Phương thức HTTP',
        example: 'GET',
    }),
    __metadata("design:type", String)
], ToolExtraDto.prototype, "method", void 0);
class NewToolResponseDto {
    parameter;
    extra;
    typeTool;
}
exports.NewToolResponseDto = NewToolResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin về tham số của tool',
        type: ToolParameterDto,
    }),
    __metadata("design:type", ToolParameterDto)
], NewToolResponseDto.prototype, "parameter", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin bổ sung của tool',
        type: ToolExtraDto,
    }),
    __metadata("design:type", ToolExtraDto)
], NewToolResponseDto.prototype, "extra", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của tool',
        example: '550e8400-e29b-41d4-a716-446655440000',
    }),
    __metadata("design:type", String)
], NewToolResponseDto.prototype, "typeTool", void 0);
class AgentToolsNewResponseDto {
    tools;
}
exports.AgentToolsNewResponseDto = AgentToolsNewResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách tool',
        type: [NewToolResponseDto],
    }),
    __metadata("design:type", Array)
], AgentToolsNewResponseDto.prototype, "tools", void 0);
//# sourceMappingURL=agent-tools-new-response.dto.js.map