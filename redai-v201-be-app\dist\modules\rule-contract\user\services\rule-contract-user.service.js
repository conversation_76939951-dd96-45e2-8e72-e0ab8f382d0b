"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RuleContractUserService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RuleContractUserService = void 0;
const common_1 = require("@nestjs/common");
const exceptions_1 = require("../../../../common/exceptions");
const repositories_1 = require("../../repositories");
const dto_1 = require("../dto");
const errors_1 = require("../../errors");
const typeorm_transactional_1 = require("typeorm-transactional");
const cdn_service_1 = require("../../../../shared/services/cdn.service");
const time_interval_util_1 = require("../../../../shared/utils/time/time-interval.util");
const rule_contract_entity_1 = require("../../entities/rule-contract.entity");
const rule_contract_state_service_1 = require("../../state-machine/rule-contract-state.service");
const rule_contract_types_1 = require("../../state-machine/rule-contract.types");
let RuleContractUserService = RuleContractUserService_1 = class RuleContractUserService {
    ruleContractRepository;
    cdnService;
    ruleContractStateService;
    logger = new common_1.Logger(RuleContractUserService_1.name);
    constructor(ruleContractRepository, cdnService, ruleContractStateService) {
        this.ruleContractRepository = ruleContractRepository;
        this.cdnService = cdnService;
        this.ruleContractStateService = ruleContractStateService;
    }
    async registerTypeRuleContract(userId, dto) {
        try {
            const createData = {
                userId,
                contractType: dto.type,
            };
            await this.ruleContractStateService.createContract(userId, createData);
            return {
                status: (0, rule_contract_types_1.mapStateToStatus)(rule_contract_types_1.RuleContractState.DRAFT),
                type: dto.type,
            };
        }
        catch (error) {
            this.logger.error(`Error registering rule contract for user ${userId}: ${error.message}`, error.stack);
            if (error instanceof exceptions_1.AppException) {
                throw error;
            }
            throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.CONTRACT_RETRIEVAL_FAILED, 'Lỗi khi đăng ký hợp đồng nguyên tắc');
        }
    }
    async getContracts(userId, queryDto) {
        try {
            const { items, meta } = await this.ruleContractRepository.findWithPaginationForUser(userId, queryDto);
            const contractDtos = await Promise.all(items.map(contract => this.mapContractToDto(contract)));
            return {
                items: contractDtos,
                meta,
            };
        }
        catch (error) {
            this.logger.error(`Error getting contracts for user ${userId}: ${error.message}`, error.stack);
            if (error instanceof exceptions_1.AppException) {
                throw error;
            }
            throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.CONTRACT_RETRIEVAL_FAILED, 'Lỗi khi lấy danh sách hợp đồng nguyên tắc');
        }
    }
    async getContractById(userId, id) {
        try {
            const contract = await this.ruleContractRepository.findById(id);
            if (!contract) {
                throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND, `Không tìm thấy hợp đồng nguyên tắc với ID ${id}`);
            }
            if (contract.userId !== userId) {
                throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.UNAUTHORIZED_ACCESS, 'Bạn không có quyền truy cập hợp đồng này');
            }
            return this.mapContractToDto(contract);
        }
        catch (error) {
            this.logger.error(`Error getting contract for user ${userId}: ${error.message}`, error.stack);
            if (error instanceof exceptions_1.AppException) {
                throw error;
            }
            throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.CONTRACT_RETRIEVAL_FAILED, 'Lỗi khi lấy thông tin chi tiết hợp đồng nguyên tắc');
        }
    }
    async createIndividualRuleContract(userId, dto) {
        try {
            this.logger.log(`Tạo hợp đồng nguyên tắc cho cá nhân, userId: ${userId}`);
            const individualData = {
                name: dto.name,
                address: dto.address,
                phone: dto.phone,
                dateOfBirth: dto.dateOfBirth,
                cccd: dto.cccd,
                issuePlace: dto.issuePlace,
                issueDate: dto.issueDate,
                taxCode: dto.taxCode,
            };
            const result = await this.ruleContractStateService.createIndividualContract(userId, individualData);
            return {
                status: rule_contract_entity_1.ContractStatusEnum.DRAFT,
                type: rule_contract_entity_1.ContractTypeEnum.INDIVIDUAL,
                contractBase64: result.contractBase64,
                contractUrl: result.contractUrl || '',
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo hợp đồng nguyên tắc cho cá nhân, userId: ${userId}: ${error.message}`, error.stack);
            if (error instanceof exceptions_1.AppException) {
                throw error;
            }
            throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.CONTRACT_RETRIEVAL_FAILED, 'Lỗi khi tạo hợp đồng nguyên tắc cho cá nhân');
        }
    }
    async getLatestContractStatus(userId) {
        try {
            const latestContract = await this.ruleContractRepository.findLatestByUserId(userId);
            if (!latestContract) {
                return null;
            }
            return {
                status: latestContract.status,
                type: latestContract.type,
            };
        }
        catch (error) {
            this.logger.error(`Error getting latest contract status for user ${userId}: ${error.message}`, error.stack);
            if (error instanceof exceptions_1.AppException) {
                throw error;
            }
            throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.CONTRACT_RETRIEVAL_FAILED, 'Lỗi khi lấy trạng thái hợp đồng nguyên tắc mới nhất');
        }
    }
    mapContractToDto(contract) {
        let contractUrl = '';
        if (contract.contractUrlPdf) {
            const generatedUrl = this.cdnService.generateUrlView(contract.contractUrlPdf, time_interval_util_1.TimeIntervalEnum.ONE_HOUR);
            if (generatedUrl) {
                contractUrl = generatedUrl;
            }
        }
        return {
            id: contract.id,
            contractCode: `HD-${contract.id}`,
            status: contract.status,
            type: contract.type,
            contractUrl,
            createdAt: contract.createdAt,
            userSignatureAt: contract.userSignatureAt,
            adminSignatureAt: contract.adminSignatureAt,
        };
    }
};
exports.RuleContractUserService = RuleContractUserService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.RegisterRuleContractDto]),
    __metadata("design:returntype", Promise)
], RuleContractUserService.prototype, "registerTypeRuleContract", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.RuleContractQueryDto]),
    __metadata("design:returntype", Promise)
], RuleContractUserService.prototype, "getContracts", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], RuleContractUserService.prototype, "getContractById", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.CreateIndividualRuleContractDto]),
    __metadata("design:returntype", Promise)
], RuleContractUserService.prototype, "createIndividualRuleContract", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RuleContractUserService.prototype, "getLatestContractStatus", null);
exports.RuleContractUserService = RuleContractUserService = RuleContractUserService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.RuleContractRepository,
        cdn_service_1.CdnService,
        rule_contract_state_service_1.RuleContractStateService])
], RuleContractUserService);
//# sourceMappingURL=rule-contract-user.service.js.map