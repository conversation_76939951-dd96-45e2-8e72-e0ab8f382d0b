{"version": 3, "file": "common-interfaces.mock.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/subscription/user/tests/mocks/common-interfaces.mock.ts"], "names": [], "mappings": ";;;AAuBA,IAAY,aAGX;AAHD,WAAY,aAAa;IACvB,4BAAW,CAAA;IACX,8BAAa,CAAA;AACf,CAAC,EAHW,aAAa,6BAAb,aAAa,QAGxB;AAOD,MAAa,WAAW;IACtB,IAAI,CAAS;IACb,OAAO,CAAS;IAChB,MAAM,CAAI;IAEV,YAAY,IAAY,EAAE,OAAe,EAAE,MAAS;QAClD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,MAAM,CAAC,OAAO,CAAI,IAAO,EAAE,UAAkB,SAAS;QACpD,OAAO,IAAI,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED,MAAM,CAAC,OAAO,CAAI,IAAO,EAAE,UAAkB,sBAAsB;QACjE,OAAO,IAAI,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,OAAe,EAAE,OAAe,GAAG;QAC9C,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAC9C,CAAC;CACF;AAtBD,kCAsBC"}