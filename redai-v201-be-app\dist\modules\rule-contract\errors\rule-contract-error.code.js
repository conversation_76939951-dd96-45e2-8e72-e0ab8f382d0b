"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RULE_CONTRACT_ERROR_CODES = void 0;
const common_1 = require("@nestjs/common");
const app_exception_1 = require("../../../common/exceptions/app.exception");
exports.RULE_CONTRACT_ERROR_CODES = {
    CONTRACT_NOT_FOUND: new app_exception_1.ErrorCode(15000, 'Không tìm thấy hợp đồng nguyên tắc', common_1.HttpStatus.NOT_FOUND),
    CONTRACT_RETRIEVAL_FAILED: new app_exception_1.ErrorCode(15001, 'Lỗi khi lấy thông tin hợp đồng nguyên tắc', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    UNAUTHORIZED_ACCESS: new app_exception_1.ErrorCode(15002, 'Không có quyền truy cập hợp đồng này', common_1.HttpStatus.FORBIDDEN),
    PERMISSION_DENIED: new app_exception_1.ErrorCode(15003, 'Không có quyền thực hiện hành động này', common_1.HttpStatus.FORBIDDEN),
    INVALID_OPERATION: new app_exception_1.ErrorCode(15004, 'Thao tác không hợp lệ', common_1.HttpStatus.BAD_REQUEST),
    INVALID_SIGNATURE: new app_exception_1.ErrorCode(15005, 'Chữ ký không hợp lệ', common_1.HttpStatus.BAD_REQUEST),
    INVALID_CONTRACT_STATE: new app_exception_1.ErrorCode(15006, 'Trạng thái hợp đồng không hợp lệ', common_1.HttpStatus.BAD_REQUEST),
    CONTRACT_CREATION_FAILED: new app_exception_1.ErrorCode(15007, 'Lỗi khi tạo hợp đồng', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    CONTRACT_UPDATE_FAILED: new app_exception_1.ErrorCode(15008, 'Lỗi khi cập nhật hợp đồng', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    CONTRACT_SIGNING_FAILED: new app_exception_1.ErrorCode(15009, 'Lỗi khi ký hợp đồng', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    CONTRACT_APPROVAL_FAILED: new app_exception_1.ErrorCode(15010, 'Lỗi khi phê duyệt hợp đồng', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    CONTRACT_REJECTION_FAILED: new app_exception_1.ErrorCode(15011, 'Lỗi khi từ chối hợp đồng', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    INVALID_DATA: new app_exception_1.ErrorCode(15012, 'Dữ liệu không hợp lệ', common_1.HttpStatus.BAD_REQUEST),
};
//# sourceMappingURL=rule-contract-error.code.js.map