{"version": 3, "sources": ["../../../src/common/dto/query.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport { IsEnum, IsInt, IsOptional, Min } from 'class-validator';\r\nimport { Type } from 'class-transformer';\r\n\r\n/**\r\n * Enum cho thứ tự sắp xếp\r\n */\r\nexport enum SortDirection {\r\n  ASC = 'ASC',\r\n  DESC = 'DESC',\r\n}\r\n\r\n/**\r\n * DTO cơ bản cho query với phân trang và sắp xếp\r\n */\r\nexport class QueryDto {\r\n  /**\r\n   * Trang hiện tại (bắt đầu từ 1)\r\n   * @example 1\r\n   */\r\n  @ApiProperty({\r\n    description: 'Trang hiện tại (bắt đầu từ 1)',\r\n    example: 1,\r\n    default: 1,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsInt({ message: 'Trang phải là số nguyên' })\r\n  @Min(1, { message: 'Trang phải lớn hơn hoặc bằng 1' })\r\n  @Type(() => Number)\r\n  page?: number = 1;\r\n\r\n  /**\r\n   * Số lượng item trên mỗi trang\r\n   * @example 10\r\n   */\r\n  @ApiProperty({\r\n    description: 'Số lượng item trên mỗi trang',\r\n    example: 10,\r\n    default: 10,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsInt({ message: 'Số lượng item phải là số nguyên' })\r\n  @Min(1, { message: 'Số lượng item phải lớn hơn hoặc bằng 1' })\r\n  @Type(() => Number)\r\n  limit?: number = 10;\r\n\r\n  /**\r\n   * Trường sắp xếp\r\n   * @example \"createdAt\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Trường sắp xếp',\r\n    example: 'createdAt',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  sortBy?: string;\r\n\r\n  /**\r\n   * Thứ tự sắp xếp\r\n   * @example \"DESC\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thứ tự sắp xếp',\r\n    enum: SortDirection,\r\n    example: SortDirection.DESC,\r\n    default: SortDirection.DESC,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsEnum(SortDirection, {\r\n    message: `Thứ tự sắp xếp phải là một trong các giá trị: ${Object.values(SortDirection).join(', ')}`,\r\n  })\r\n  sortDirection?: SortDirection = SortDirection.DESC;\r\n}\r\n"], "names": ["QueryDto", "SortDirection", "page", "limit", "sortDirection", "description", "example", "default", "required", "message", "Number", "enum", "Object", "values", "join"], "mappings": ";;;;;;;;;;;QAeaA;eAAAA;;QARDC;eAAAA;;;yBAPgB;gCACmB;kCAC1B;;;;;;;;;;AAKd,IAAA,AAAKA,uCAAAA;;;WAAAA;;AAQL,IAAA,AAAMD,WAAN,MAAMA;;QACX;;;GAGC,QAWDE,OAAgB;QAEhB;;;GAGC,QAWDC,QAAiB;QAcjB;;;GAGC,QAYDC;;AACF;;;QAvDIC,aAAa;QACbC,SAAS;QACTC,SAAS;QACTC,UAAU;;;;QAGHC,SAAS;;;QACRA,SAAS;;oCACPC;;;;;QAQVL,aAAa;QACbC,SAAS;QACTC,SAAS;QACTC,UAAU;;;;QAGHC,SAAS;;;QACRA,SAAS;;oCACPC;;;;;QAQVL,aAAa;QACbC,SAAS;QACTE,UAAU;;;;;;;QAUVH,aAAa;QACbM,MAAMV;QACNK,OAAO;QACPC,OAAO;QACPC,UAAU;;;;QAIVC,SAAS,CAAC,8CAA8C,EAAEG,OAAOC,MAAM,CAACZ,eAAea,IAAI,CAAC,OAAO"}