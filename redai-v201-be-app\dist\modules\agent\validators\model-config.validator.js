"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IsValidModelConfig = IsValidModelConfig;
exports.IsValidAdminModelConfig = IsValidAdminModelConfig;
exports.IsValidUserPersonalModel = IsValidUserPersonalModel;
const class_validator_1 = require("class-validator");
function IsValidModelConfig(validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            name: 'isValidModelConfig',
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            validator: {
                validate(value, args) {
                    const obj = args.object;
                    if (obj.model_base_id) {
                        return true;
                    }
                    if (obj.model_finetuning_id) {
                        return true;
                    }
                    if (obj.model_id && obj.provider_id) {
                        return true;
                    }
                    return false;
                },
                defaultMessage(args) {
                    return '<PERSON><PERSON>i cung cấp ít nhất một trong các options: model_base_id, model_finetuning_id, hoặc cả model_id và provider_id';
                },
            },
        });
    };
}
function IsValidAdminModelConfig(validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            name: 'isValidAdminModelConfig',
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            validator: {
                validate(value, args) {
                    const obj = args.object;
                    if (obj.model_id || obj.provider_id) {
                        return false;
                    }
                    if (obj.model_base_id || obj.model_finetuning_id) {
                        return true;
                    }
                    return false;
                },
                defaultMessage(args) {
                    return 'Admin chỉ có thể sử dụng model_base_id hoặc model_finetuning_id, không được sử dụng model_id/provider_id';
                },
            },
        });
    };
}
function IsValidUserPersonalModel(validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            name: 'isValidUserPersonalModel',
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            validator: {
                validate(value, args) {
                    const obj = args.object;
                    if (obj.model_id && !obj.provider_id) {
                        return false;
                    }
                    if (obj.provider_id && !obj.model_id) {
                        return false;
                    }
                    return true;
                },
                defaultMessage(args) {
                    return 'model_id và provider_id phải được cung cấp cùng nhau';
                },
            },
        });
    };
}
//# sourceMappingURL=model-config.validator.js.map