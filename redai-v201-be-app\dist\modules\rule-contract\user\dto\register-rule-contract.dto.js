"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RegisterRuleContractDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const rule_contract_entity_1 = require("../../entities/rule-contract.entity");
class RegisterRuleContractDto {
    type;
}
exports.RegisterRuleContractDto = RegisterRuleContractDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại hợp đồng',
        enum: rule_contract_entity_1.ContractTypeEnum,
        example: rule_contract_entity_1.ContractTypeEnum.INDIVIDUAL,
        required: true,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Loại hợp đồng không được để trống' }),
    (0, class_validator_1.IsEnum)(rule_contract_entity_1.ContractTypeEnum, { message: 'Loại hợp đồng không hợp lệ' }),
    __metadata("design:type", String)
], RegisterRuleContractDto.prototype, "type", void 0);
//# sourceMappingURL=register-rule-contract.dto.js.map