"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var GenericPageTemplateAdminService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericPageTemplateAdminService = void 0;
const common_1 = require("@nestjs/common");
const common_2 = require("../../../../common");
const generic_page_error_code_1 = require("../../exceptions/generic-page-error.code");
const generic_page_template_repository_1 = require("../../repositories/generic-page-template.repository");
const generic_page_template_tag_repository_1 = require("../../repositories/generic-page-template-tag.repository");
const generic_page_template_entity_1 = require("../../entities/generic-page-template.entity");
const dto_1 = require("../dto");
let GenericPageTemplateAdminService = GenericPageTemplateAdminService_1 = class GenericPageTemplateAdminService {
    genericPageTemplateRepository;
    genericPageTemplateTagRepository;
    logger = new common_1.Logger(GenericPageTemplateAdminService_1.name);
    constructor(genericPageTemplateRepository, genericPageTemplateTagRepository) {
        this.genericPageTemplateRepository = genericPageTemplateRepository;
        this.genericPageTemplateTagRepository = genericPageTemplateTagRepository;
    }
    async createGenericPageTemplate(createGenericPageTemplateDto, employeeId) {
        try {
            const genericPageTemplate = new generic_page_template_entity_1.GenericPageTemplate();
            genericPageTemplate.name = createGenericPageTemplateDto.name;
            genericPageTemplate.description = createGenericPageTemplateDto.description || '';
            genericPageTemplate.category = createGenericPageTemplateDto.category || '';
            genericPageTemplate.thumbnail = createGenericPageTemplateDto.thumbnail || '';
            genericPageTemplate.config = createGenericPageTemplateDto.config;
            genericPageTemplate.createdAt = Date.now();
            genericPageTemplate.updatedAt = Date.now();
            genericPageTemplate.createdBy = employeeId;
            genericPageTemplate.updatedBy = employeeId;
            const savedGenericPageTemplate = await this.genericPageTemplateRepository.save(genericPageTemplate);
            if (createGenericPageTemplateDto.tags && createGenericPageTemplateDto.tags.length > 0) {
                await this.genericPageTemplateTagRepository.saveTags(savedGenericPageTemplate.id, createGenericPageTemplateDto.tags);
            }
            const tags = await this.genericPageTemplateTagRepository.findTagsByTemplateId(savedGenericPageTemplate.id);
            return this.mapToResponseDto(savedGenericPageTemplate, tags);
        }
        catch (error) {
            if (error instanceof common_2.AppException) {
                throw error;
            }
            this.logger.error(`Error creating generic page template: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_CREATE_ERROR, 'Lỗi khi tạo mẫu trang');
        }
    }
    async updateGenericPageTemplate(id, updateGenericPageTemplateDto, employeeId) {
        try {
            const genericPageTemplate = await this.genericPageTemplateRepository.findById(id);
            if (updateGenericPageTemplateDto.name) {
                genericPageTemplate.name = updateGenericPageTemplateDto.name;
            }
            if (updateGenericPageTemplateDto.description !== undefined) {
                genericPageTemplate.description = updateGenericPageTemplateDto.description;
            }
            if (updateGenericPageTemplateDto.category !== undefined) {
                genericPageTemplate.category = updateGenericPageTemplateDto.category;
            }
            if (updateGenericPageTemplateDto.thumbnail !== undefined) {
                genericPageTemplate.thumbnail = updateGenericPageTemplateDto.thumbnail;
            }
            if (updateGenericPageTemplateDto.config) {
                genericPageTemplate.config = updateGenericPageTemplateDto.config;
            }
            genericPageTemplate.updatedAt = Date.now();
            genericPageTemplate.updatedBy = employeeId;
            const savedGenericPageTemplate = await this.genericPageTemplateRepository.save(genericPageTemplate);
            if (updateGenericPageTemplateDto.tags !== undefined) {
                await this.genericPageTemplateTagRepository.saveTags(savedGenericPageTemplate.id, updateGenericPageTemplateDto.tags);
            }
            const tags = await this.genericPageTemplateTagRepository.findTagsByTemplateId(savedGenericPageTemplate.id);
            return this.mapToResponseDto(savedGenericPageTemplate, tags);
        }
        catch (error) {
            if (error instanceof common_2.AppException) {
                throw error;
            }
            this.logger.error(`Error updating generic page template: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_UPDATE_ERROR, `Lỗi khi cập nhật mẫu trang với ID ${id}`);
        }
    }
    async getGenericPageTemplateById(id) {
        try {
            const genericPageTemplate = await this.genericPageTemplateRepository.findById(id);
            const tags = await this.genericPageTemplateTagRepository.findTagsByTemplateId(id);
            return this.mapToResponseDto(genericPageTemplate, tags);
        }
        catch (error) {
            if (error instanceof common_2.AppException) {
                throw error;
            }
            this.logger.error(`Error getting generic page template by ID: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_NOT_FOUND, `Lỗi khi lấy thông tin mẫu trang với ID ${id}`);
        }
    }
    async deleteGenericPageTemplate(id) {
        try {
            const genericPageTemplate = await this.genericPageTemplateRepository.findById(id);
            await this.genericPageTemplateTagRepository.deleteTagsByTemplateId(id);
            await this.genericPageTemplateRepository.remove(genericPageTemplate);
        }
        catch (error) {
            if (error instanceof common_2.AppException) {
                throw error;
            }
            this.logger.error(`Error deleting generic page template: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_DELETE_ERROR, `Lỗi khi xóa mẫu trang với ID ${id}`);
        }
    }
    mapToResponseDto(genericPageTemplate, tags) {
        const responseDto = new dto_1.GenericPageTemplateResponseDto();
        responseDto.id = genericPageTemplate.id;
        responseDto.name = genericPageTemplate.name;
        responseDto.description = genericPageTemplate.description;
        responseDto.category = genericPageTemplate.category;
        responseDto.thumbnail = genericPageTemplate.thumbnail;
        responseDto.tags = tags || [];
        responseDto.config = genericPageTemplate.config;
        responseDto.createdAt = genericPageTemplate.createdAt;
        responseDto.updatedAt = genericPageTemplate.updatedAt;
        responseDto.createdBy = genericPageTemplate.createdBy;
        responseDto.updatedBy = genericPageTemplate.updatedBy;
        return responseDto;
    }
};
exports.GenericPageTemplateAdminService = GenericPageTemplateAdminService;
exports.GenericPageTemplateAdminService = GenericPageTemplateAdminService = GenericPageTemplateAdminService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [generic_page_template_repository_1.GenericPageTemplateRepository,
        generic_page_template_tag_repository_1.GenericPageTemplateTagRepository])
], GenericPageTemplateAdminService);
//# sourceMappingURL=generic-page-template-admin.service.js.map