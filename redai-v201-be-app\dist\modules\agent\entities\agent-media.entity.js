"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentMedia = void 0;
const typeorm_1 = require("typeorm");
let AgentMedia = class AgentMedia {
    id;
    agentId;
    mediaId;
    mediaType;
    filePath;
    originalName;
    fileSize;
    mimeType;
    active;
    createdAt;
    updatedAt;
};
exports.AgentMedia = AgentMedia;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], AgentMedia.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'integer',
        name: 'agent_id',
        comment: 'ID của agent sở hữu media'
    }),
    __metadata("design:type", Number)
], AgentMedia.prototype, "agentId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'integer',
        name: 'media_id',
        comment: 'ID của media'
    }),
    __metadata("design:type", Number)
], AgentMedia.prototype, "mediaId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        name: 'media_type',
        comment: 'Loại media (image, video, audio, document)'
    }),
    __metadata("design:type", String)
], AgentMedia.prototype, "mediaType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 500,
        name: 'file_path',
        comment: 'Đường dẫn file media'
    }),
    __metadata("design:type", String)
], AgentMedia.prototype, "filePath", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 255,
        name: 'original_name',
        comment: 'Tên file gốc'
    }),
    __metadata("design:type", String)
], AgentMedia.prototype, "originalName", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'bigint',
        name: 'file_size',
        comment: 'Kích thước file (bytes)'
    }),
    __metadata("design:type", Number)
], AgentMedia.prototype, "fileSize", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        name: 'mime_type',
        comment: 'MIME type của file'
    }),
    __metadata("design:type", String)
], AgentMedia.prototype, "mimeType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'boolean',
        default: true,
        comment: 'Trạng thái hoạt động'
    }),
    __metadata("design:type", Boolean)
], AgentMedia.prototype, "active", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        type: 'timestamp',
        name: 'created_at',
        comment: 'Thời gian tạo'
    }),
    __metadata("design:type", Date)
], AgentMedia.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        type: 'timestamp',
        name: 'updated_at',
        comment: 'Thời gian cập nhật lần cuối'
    }),
    __metadata("design:type", Date)
], AgentMedia.prototype, "updatedAt", void 0);
exports.AgentMedia = AgentMedia = __decorate([
    (0, typeorm_1.Entity)('agent_media')
], AgentMedia);
//# sourceMappingURL=agent-media.entity.js.map