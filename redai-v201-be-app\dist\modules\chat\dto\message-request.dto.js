"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageRequestDto = exports.ContentBlockDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class ContentBlockDto {
    type;
    decision;
    content;
    fileId;
}
exports.ContentBlockDto = ContentBlockDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of content block',
        enum: ['text', 'image', 'file', 'modify_last_text', 'tool_call_decision', 'cancel'],
        example: 'text'
    }),
    (0, class_validator_1.IsEnum)(['text', 'image', 'file', 'modify_last_text', 'tool_call_decision', 'cancel']),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ContentBlockDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Decision for tool call (if type is tool_call_decision)',
        enum: ['yes', 'no', 'always'],
        example: 'yes'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['yes', 'no', 'always']),
    __metadata("design:type", String)
], ContentBlockDto.prototype, "decision", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Content (if type is text)',
        example: 'Hello, how can I help you?'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContentBlockDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'File ID (if type is file)',
        example: 'file_123456'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContentBlockDto.prototype, "fileId", void 0);
class MessageRequestDto {
    agentId;
    contentBlocks;
    threadId;
    alwaysApproveToolCall;
}
exports.MessageRequestDto = MessageRequestDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the agent to process the message',
        example: 'agent_123456'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], MessageRequestDto.prototype, "agentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Content blocks for the message (single block or array of blocks)',
        type: [ContentBlockDto],
        example: [{ type: 'text', content: 'Hello, how can I help you?' }]
    }),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => ContentBlockDto),
    __metadata("design:type", Object)
], MessageRequestDto.prototype, "contentBlocks", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional thread ID to continue existing conversation',
        example: 'thread_123456'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], MessageRequestDto.prototype, "threadId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether to always approve tool calls without user confirmation',
        example: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], MessageRequestDto.prototype, "alwaysApproveToolCall", void 0);
//# sourceMappingURL=message-request.dto.js.map