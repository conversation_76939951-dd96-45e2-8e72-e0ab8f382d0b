"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UserKeyLlmService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserKeyLlmService = void 0;
const ai_provider_helper_1 = require("../../../../shared/services/ai/helpers/ai-provider.helper");
const exceptions_1 = require("../../../../common/exceptions");
const response_1 = require("../../../../common/response");
const common_1 = require("@nestjs/common");
const typeorm_transactional_1 = require("typeorm-transactional");
const exceptions_2 = require("../../exceptions");
const api_key_encryption_helper_1 = require("../../helpers/api-key-encryption.helper");
const user_key_llm_repository_1 = require("../../repositories/user-key-llm.repository");
const user_model_sync_service_1 = require("../../services/user-model-sync.service");
const user_key_llm_1 = require("../dto/user-key-llm");
const user_key_llm_mapper_1 = require("../mappers/user-key-llm.mapper");
let UserKeyLlmService = UserKeyLlmService_1 = class UserKeyLlmService {
    userKeyLlmRepository;
    apiKeyEncryptionHelper;
    aiProviderService;
    userModelSyncService;
    logger = new common_1.Logger(UserKeyLlmService_1.name);
    constructor(userKeyLlmRepository, apiKeyEncryptionHelper, aiProviderService, userModelSyncService) {
        this.userKeyLlmRepository = userKeyLlmRepository;
        this.apiKeyEncryptionHelper = apiKeyEncryptionHelper;
        this.aiProviderService = aiProviderService;
        this.userModelSyncService = userModelSyncService;
    }
    async create(userId, createDto) {
        this.logger.log(`Creating user key LLM for user ${userId}`);
        if (!user_key_llm_mapper_1.UserKeyLlmMapper.validateKeyName(createDto.name)) {
            throw new exceptions_1.AppException(exceptions_2.MODELS_ERROR_CODES.USER_KEY_LLM_INVALID_NAME);
        }
        const existsByName = await this.userKeyLlmRepository.existsByNameAndUserId(createDto.name, userId);
        if (existsByName) {
            throw new exceptions_1.AppException(exceptions_2.MODELS_ERROR_CODES.USER_KEY_LLM_NAME_EXISTS);
        }
        const encryptedApiKey = this.apiKeyEncryptionHelper.encryptUserApiKey(createDto.apiKey, userId);
        const testResult = await this.aiProviderService.testConnection(encryptedApiKey, createDto.provider, false, userId);
        if (!testResult.success) {
            throw new exceptions_1.AppException(exceptions_2.MODELS_ERROR_CODES.USER_KEY_LLM_CONNECTION_FAILED, testResult.error);
        }
        const userKeyLlm = this.userKeyLlmRepository.create({
            name: createDto.name,
            provider: createDto.provider,
            apiKey: encryptedApiKey,
            userId,
            createdAt: Date.now(),
            updatedAt: Date.now()
        });
        const savedUserKeyLlm = await this.userKeyLlmRepository.save(userKeyLlm);
        let modelDiscovery;
        try {
            this.logger.log(`Starting model discovery for user key ${savedUserKeyLlm.id}`);
            const syncResult = await this.userModelSyncService.syncModels({
                keyId: savedUserKeyLlm.id,
                provider: createDto.provider,
                encryptedApiKey: encryptedApiKey,
                userId: userId
            });
            modelDiscovery = {
                totalModelsFound: syncResult.discoveryResult.totalModelsFound,
                modelsMatched: syncResult.discoveryResult.modelsMatched,
                newModelsCreated: syncResult.discoveryResult.newModelsCreated,
                existingModelsFound: syncResult.discoveryResult.existingModelsFound,
                mappingsCreated: syncResult.discoveryResult.mappingsCreated,
                discoveryTime: syncResult.syncedAt,
                success: syncResult.success,
                message: syncResult.message,
                errors: syncResult.discoveryResult.errors
            };
            this.logger.log(`Model discovery completed for user key ${savedUserKeyLlm.id}: ${syncResult.message}`);
        }
        catch (error) {
            this.logger.error(`Model discovery failed for user key ${savedUserKeyLlm.id}: ${error.message}`, error.stack);
            modelDiscovery = {
                totalModelsFound: 0,
                modelsMatched: 0,
                newModelsCreated: 0,
                existingModelsFound: 0,
                mappingsCreated: 0,
                discoveryTime: Date.now(),
                success: false,
                message: `Model discovery thất bại: ${error.message}`,
                errors: [error.message]
            };
        }
        this.logger.log(`Created user key LLM ${userKeyLlm.id} successfully`);
        const response = {
            id: savedUserKeyLlm.id,
            connectionError: testResult.error,
            modelDiscovery
        };
        return response_1.ApiResponseDto.success(response);
    }
    async findAll(userId, queryDto) {
        this.logger.log(`Getting user key LLM list for user ${userId}`);
        const result = await this.userKeyLlmRepository.findWithPagination(queryDto, userId);
        const items = user_key_llm_mapper_1.UserKeyLlmMapper.toResponseDtoArray(result.items);
        return response_1.ApiResponseDto.paginated({
            items,
            meta: result.meta
        });
    }
    async update(userId, id, updateDto) {
        this.logger.log(`Updating user key LLM ${id} for user ${userId}`);
        const existingUserKeyLlm = await this.userKeyLlmRepository.findByIdAndUserId(id, userId);
        if (!existingUserKeyLlm) {
            throw new exceptions_1.AppException(exceptions_2.MODELS_ERROR_CODES.USER_KEY_LLM_NOT_FOUND);
        }
        if (updateDto.name && !user_key_llm_mapper_1.UserKeyLlmMapper.validateKeyName(updateDto.name)) {
            throw new exceptions_1.AppException(exceptions_2.MODELS_ERROR_CODES.USER_KEY_LLM_INVALID_NAME);
        }
        if (updateDto.name && updateDto.name !== existingUserKeyLlm.name) {
            const existsByName = await this.userKeyLlmRepository.existsByNameAndUserId(updateDto.name, userId, id);
            if (existsByName) {
                throw new exceptions_1.AppException(exceptions_2.MODELS_ERROR_CODES.USER_KEY_LLM_NAME_EXISTS);
            }
        }
        let encryptedApiKey;
        let connectionError;
        let hasApiKeyChange = false;
        if (updateDto.apiKey) {
            encryptedApiKey = this.apiKeyEncryptionHelper.encryptUserApiKey(updateDto.apiKey, userId);
            hasApiKeyChange = true;
            const testResult = await this.aiProviderService.testConnection(encryptedApiKey, existingUserKeyLlm.provider, false, userId);
            if (!testResult.success) {
                throw new exceptions_1.AppException(exceptions_2.MODELS_ERROR_CODES.USER_KEY_LLM_CONNECTION_FAILED, testResult.error);
            }
            connectionError = testResult.error;
        }
        if (updateDto.name !== undefined) {
            existingUserKeyLlm.name = updateDto.name;
        }
        if (updateDto.apiKey !== undefined && encryptedApiKey) {
            existingUserKeyLlm.apiKey = encryptedApiKey;
        }
        existingUserKeyLlm.updatedAt = Date.now();
        await this.userKeyLlmRepository.save(existingUserKeyLlm);
        let modelDiscovery;
        if (hasApiKeyChange && encryptedApiKey) {
            try {
                this.logger.log(`Starting model discovery for updated user key ${id}`);
                const syncResult = await this.userModelSyncService.syncModels({
                    keyId: id,
                    provider: existingUserKeyLlm.provider,
                    encryptedApiKey: encryptedApiKey,
                    userId: userId
                });
                modelDiscovery = {
                    totalModelsFound: syncResult.discoveryResult.totalModelsFound,
                    modelsMatched: syncResult.discoveryResult.modelsMatched,
                    newModelsCreated: syncResult.discoveryResult.newModelsCreated,
                    existingModelsFound: syncResult.discoveryResult.existingModelsFound,
                    mappingsCreated: syncResult.discoveryResult.mappingsCreated,
                    discoveryTime: syncResult.syncedAt,
                    success: syncResult.success,
                    message: syncResult.message,
                    errors: syncResult.discoveryResult.errors
                };
                this.logger.log(`Model discovery completed for updated user key ${id}: ${syncResult.message}`);
            }
            catch (error) {
                this.logger.error(`Model discovery failed for updated user key ${id}: ${error.message}`, error.stack);
                modelDiscovery = {
                    totalModelsFound: 0,
                    modelsMatched: 0,
                    newModelsCreated: 0,
                    existingModelsFound: 0,
                    mappingsCreated: 0,
                    discoveryTime: Date.now(),
                    success: false,
                    message: `Model discovery thất bại: ${error.message}`,
                    errors: [error.message]
                };
            }
        }
        this.logger.log(`Updated user key LLM ${id} successfully`);
        const response = {
            id: existingUserKeyLlm.id,
            connectionError,
            modelDiscovery
        };
        return response_1.ApiResponseDto.success(response);
    }
    async remove(userId, id) {
        this.logger.log(`Removing user key LLM ${id} for user ${userId}`);
        const existingUserKeyLlm = await this.userKeyLlmRepository.findByIdAndUserId(id, userId);
        if (!existingUserKeyLlm) {
            throw new exceptions_1.AppException(exceptions_2.MODELS_ERROR_CODES.USER_KEY_LLM_NOT_FOUND);
        }
        try {
            this.logger.log(`Clearing model mappings for user key ${id}`);
            const clearResult = await this.userModelSyncService.clearModels(id, userId);
            this.logger.log(`Cleared ${clearResult.deletedMappings} model mappings for user key ${id}`);
        }
        catch (error) {
            this.logger.error(`Failed to clear model mappings for user key ${id}: ${error.message}`, error.stack);
        }
        const deleted = await this.userKeyLlmRepository.softDeleteUserKeyLlm(id, userId);
        if (!deleted) {
            throw new exceptions_1.AppException(exceptions_2.MODELS_ERROR_CODES.USER_KEY_LLM_DELETE_FAILED);
        }
        this.logger.log(`Soft deleted user key LLM ${id} successfully`);
        return response_1.ApiResponseDto.success({ message: 'Xóa user key LLM thành công' });
    }
    async reloadModels(userId, keyId) {
        this.logger.log(`Reloading models for user key ${keyId}, userId: ${userId}`);
        try {
            const syncResult = await this.userModelSyncService.reloadModels(keyId, userId);
            const response = {
                keyId: syncResult.keyId,
                provider: syncResult.provider,
                userId: syncResult.userId,
                modelDiscovery: {
                    totalModelsFound: syncResult.discoveryResult.totalModelsFound,
                    modelsMatched: syncResult.discoveryResult.modelsMatched,
                    newModelsCreated: syncResult.discoveryResult.newModelsCreated,
                    existingModelsFound: syncResult.discoveryResult.existingModelsFound,
                    mappingsCreated: syncResult.discoveryResult.mappingsCreated,
                    discoveryTime: syncResult.syncedAt,
                    success: syncResult.success,
                    message: syncResult.message,
                    errors: syncResult.discoveryResult.errors
                }
            };
            return response_1.ApiResponseDto.success(response);
        }
        catch (error) {
            this.logger.error(`Failed to reload models for user key ${keyId}: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.UserKeyLlmService = UserKeyLlmService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, user_key_llm_1.CreateUserKeyLlmDto]),
    __metadata("design:returntype", Promise)
], UserKeyLlmService.prototype, "create", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String, user_key_llm_1.UpdateUserKeyLlmDto]),
    __metadata("design:returntype", Promise)
], UserKeyLlmService.prototype, "update", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], UserKeyLlmService.prototype, "remove", null);
exports.UserKeyLlmService = UserKeyLlmService = UserKeyLlmService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [user_key_llm_repository_1.UserKeyLlmRepository,
        api_key_encryption_helper_1.ApiKeyEncryptionHelper,
        ai_provider_helper_1.AiProviderHelper,
        user_model_sync_service_1.UserModelSyncService])
], UserKeyLlmService);
//# sourceMappingURL=user-key-llm.service.js.map