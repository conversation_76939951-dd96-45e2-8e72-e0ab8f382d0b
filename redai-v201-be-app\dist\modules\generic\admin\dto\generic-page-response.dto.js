"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericPageResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const generic_page_enum_1 = require("../../constants/generic-page.enum");
class GenericPageResponseDto {
    id;
    name;
    description;
    path;
    config;
    status;
    createdAt;
    updatedAt;
    publishedAt;
    createdBy;
    updatedBy;
}
exports.GenericPageResponseDto = GenericPageResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của trang',
        example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    }),
    __metadata("design:type", String)
], GenericPageResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên của trang',
        example: 'Trang liên hệ',
    }),
    __metadata("design:type", String)
], GenericPageResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả về trang',
        example: 'Form liên hệ cho khách hàng',
        nullable: true,
    }),
    __metadata("design:type", Object)
], GenericPageResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Đường dẫn URL của trang',
        example: 'lien-he',
    }),
    __metadata("design:type", String)
], GenericPageResponseDto.prototype, "path", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cấu hình trang dạng JSON',
        example: {
            formId: 'contact-form',
            title: 'Liên hệ với chúng tôi',
            subtitle: 'Hãy để lại thông tin, chúng tôi sẽ liên hệ lại với bạn',
            groups: [],
        },
    }),
    __metadata("design:type", Object)
], GenericPageResponseDto.prototype, "config", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái của trang',
        enum: generic_page_enum_1.GenericPageStatusEnum,
        example: generic_page_enum_1.GenericPageStatusEnum.PUBLISHED,
    }),
    __metadata("design:type", String)
], GenericPageResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời điểm tạo trang (Unix timestamp)',
        example: 1672567200000,
    }),
    __metadata("design:type", Number)
], GenericPageResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời điểm cập nhật trang gần nhất (Unix timestamp)',
        example: 1673363400000,
    }),
    __metadata("design:type", Number)
], GenericPageResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời điểm xuất bản trang (Unix timestamp)',
        example: 1673363400000,
        nullable: true,
    }),
    __metadata("design:type", Object)
], GenericPageResponseDto.prototype, "publishedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của người tạo trang',
        example: 'admin-user-id',
    }),
    __metadata("design:type", String)
], GenericPageResponseDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của người cập nhật trang gần nhất',
        example: 'admin-user-id',
    }),
    __metadata("design:type", String)
], GenericPageResponseDto.prototype, "updatedBy", void 0);
//# sourceMappingURL=generic-page-response.dto.js.map