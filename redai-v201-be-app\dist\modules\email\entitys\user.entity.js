"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserEntity = void 0;
const enums_1 = require("../../user/enums");
const typeorm_1 = require("typeorm");
let UserEntity = class UserEntity {
    id;
    fullName;
    email;
    phoneNumber;
    isActive;
    isVerifyEmail;
    isVerifyPhone;
    createdAt;
    updatedAt;
    citizenIssuePlace;
    citizenIssueDate;
    isFirstPasswordChange;
    country;
    address;
    taxCode;
    pointsBalance;
    type;
    platform;
    citizenId;
    avatar;
    password;
    dateOfBirth;
    gender;
    bankCode;
    accountNumber;
    accountHolder;
    bankBranch;
    role;
    alertThreshold;
    wasRpointAlerted;
    countryCode;
};
exports.UserEntity = UserEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], UserEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'full_name', type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], UserEntity.prototype, "fullName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, unique: true, nullable: true }),
    __metadata("design:type", String)
], UserEntity.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'phone_number',
        type: 'varchar',
        length: 45,
        unique: true,
        nullable: true,
    }),
    __metadata("design:type", String)
], UserEntity.prototype, "phoneNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_active', type: 'boolean', default: true }),
    __metadata("design:type", Boolean)
], UserEntity.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_verify_email', type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], UserEntity.prototype, "isVerifyEmail", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_verify_phone', type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], UserEntity.prototype, "isVerifyPhone", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_at', type: 'bigint', nullable: true }),
    __metadata("design:type", Number)
], UserEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_at', type: 'bigint', nullable: true }),
    __metadata("design:type", Number)
], UserEntity.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'citizen_issue_place',
        type: 'varchar',
        length: 100,
        nullable: true,
    }),
    __metadata("design:type", String)
], UserEntity.prototype, "citizenIssuePlace", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'citizen_issue_date', type: 'date', nullable: true }),
    __metadata("design:type", Date)
], UserEntity.prototype, "citizenIssueDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_first_password_change', type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], UserEntity.prototype, "isFirstPasswordChange", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], UserEntity.prototype, "country", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 1000, nullable: true }),
    __metadata("design:type", String)
], UserEntity.prototype, "address", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'tax_code', type: 'varchar', length: 20, nullable: true }),
    __metadata("design:type", String)
], UserEntity.prototype, "taxCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'points_balance', type: 'bigint', default: 0 }),
    __metadata("design:type", Number)
], UserEntity.prototype, "pointsBalance", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: enums_1.UserTypeEnum,
        default: enums_1.UserTypeEnum.INDIVIDUAL,
    }),
    __metadata("design:type", String)
], UserEntity.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 20, nullable: true }),
    __metadata("design:type", String)
], UserEntity.prototype, "platform", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'citizen_id', type: 'varchar', length: 20, nullable: true }),
    __metadata("design:type", String)
], UserEntity.prototype, "citizenId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], UserEntity.prototype, "avatar", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 1000, nullable: true }),
    __metadata("design:type", String)
], UserEntity.prototype, "password", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'date_of_birth', type: 'date', nullable: true }),
    __metadata("design:type", Date)
], UserEntity.prototype, "dateOfBirth", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: enums_1.GenderEnum,
        nullable: true,
    }),
    __metadata("design:type", String)
], UserEntity.prototype, "gender", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'bank_code', type: 'varchar', length: 20, nullable: true }),
    __metadata("design:type", String)
], UserEntity.prototype, "bankCode", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'account_number',
        type: 'varchar',
        length: 50,
        nullable: true,
    }),
    __metadata("design:type", String)
], UserEntity.prototype, "accountNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'account_holder',
        type: 'varchar',
        length: 255,
        nullable: true,
    }),
    __metadata("design:type", String)
], UserEntity.prototype, "accountHolder", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'bank_branch', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], UserEntity.prototype, "bankBranch", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 20 }),
    __metadata("design:type", String)
], UserEntity.prototype, "role", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'alert_threshold', type: 'bigint' }),
    __metadata("design:type", Number)
], UserEntity.prototype, "alertThreshold", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'was_rpoint_alerted', type: 'boolean' }),
    __metadata("design:type", Number)
], UserEntity.prototype, "wasRpointAlerted", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'country_code', type: 'varchar', length: 10, default: '+84', nullable: true }),
    __metadata("design:type", String)
], UserEntity.prototype, "countryCode", void 0);
exports.UserEntity = UserEntity = __decorate([
    (0, typeorm_1.Entity)({ name: 'users' })
], UserEntity);
//# sourceMappingURL=user.entity.js.map