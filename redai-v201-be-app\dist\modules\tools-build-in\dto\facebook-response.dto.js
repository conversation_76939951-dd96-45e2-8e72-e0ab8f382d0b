"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FacebookAnalyticsResponseDto = exports.FacebookMessageResponseDto = exports.FacebookPageResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class FacebookPageResponseDto {
    pageId;
    pageName;
    avatarUrl;
    followers;
    lastUpdated;
}
exports.FacebookPageResponseDto = FacebookPageResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của trang Facebook',
        example: '123456789012345'
    }),
    __metadata("design:type", String)
], FacebookPageResponseDto.prototype, "pageId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên trang Facebook',
        example: 'Trang Kinh Doanh ABC'
    }),
    __metadata("design:type", String)
], FacebookPageResponseDto.prototype, "pageName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'URL avatar của trang Facebook',
        example: 'https://platform-lookaside.fbsbx.com/platform/profilepic/?psid=123456789012345',
        nullable: true
    }),
    __metadata("design:type", String)
], FacebookPageResponseDto.prototype, "avatarUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số lượng người theo dõi',
        example: 1500,
        nullable: true
    }),
    __metadata("design:type", Number)
], FacebookPageResponseDto.prototype, "followers", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian cập nhật gần nhất',
        example: '2023-01-01T00:00:00.000Z'
    }),
    __metadata("design:type", String)
], FacebookPageResponseDto.prototype, "lastUpdated", void 0);
class FacebookMessageResponseDto {
    messageId;
    senderId;
    content;
    timestamp;
    attachments;
}
exports.FacebookMessageResponseDto = FacebookMessageResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của tin nhắn',
        example: 'm_abcdefghijklmnop'
    }),
    __metadata("design:type", String)
], FacebookMessageResponseDto.prototype, "messageId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của người gửi',
        example: '123456789012345'
    }),
    __metadata("design:type", String)
], FacebookMessageResponseDto.prototype, "senderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nội dung tin nhắn',
        example: 'Xin chào, tôi cần hỗ trợ về sản phẩm của bạn'
    }),
    __metadata("design:type", String)
], FacebookMessageResponseDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian gửi tin nhắn',
        example: '2023-01-01T00:00:00.000Z'
    }),
    __metadata("design:type", String)
], FacebookMessageResponseDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Các tệp đính kèm (nếu có)',
        example: [{ type: 'image', url: 'https://example.com/image.jpg' }],
        nullable: true
    }),
    __metadata("design:type", Array)
], FacebookMessageResponseDto.prototype, "attachments", void 0);
class FacebookAnalyticsResponseDto {
    reach;
    engagement;
    messages;
    averageResponseTime;
}
exports.FacebookAnalyticsResponseDto = FacebookAnalyticsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tổng số lượt tiếp cận',
        example: 5000
    }),
    __metadata("design:type", Number)
], FacebookAnalyticsResponseDto.prototype, "reach", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tổng số lượt tương tác',
        example: 1200
    }),
    __metadata("design:type", Number)
], FacebookAnalyticsResponseDto.prototype, "engagement", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tổng số tin nhắn',
        example: 350
    }),
    __metadata("design:type", Number)
], FacebookAnalyticsResponseDto.prototype, "messages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian phản hồi trung bình (phút)',
        example: 15.5
    }),
    __metadata("design:type", Number)
], FacebookAnalyticsResponseDto.prototype, "averageResponseTime", void 0);
//# sourceMappingURL=facebook-response.dto.js.map