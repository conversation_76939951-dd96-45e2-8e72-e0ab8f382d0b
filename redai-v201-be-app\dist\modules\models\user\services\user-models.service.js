"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UserModelsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserModelsService = void 0;
const exceptions_1 = require("../../../../common/exceptions");
const response_1 = require("../../../../common/response");
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const models_exception_1 = require("../../exceptions/models.exception");
const system_models_repository_1 = require("../../repositories/system-models.repository");
const user_data_fine_tune_repository_1 = require("../../repositories/user-data-fine-tune.repository");
const user_key_llm_repository_1 = require("../../repositories/user-key-llm.repository");
const user_model_fine_tune_repository_1 = require("../../repositories/user-model-fine-tune.repository");
const user_models_repository_1 = require("../../repositories/user-models.repository");
const mappers_1 = require("../mappers");
const cdn_service_1 = require("../../../../shared/services/cdn.service");
let UserModelsService = UserModelsService_1 = class UserModelsService {
    userModelsRepository;
    systemModelsRepository;
    userDataFineTuneRepository;
    userKeyLlmRepository;
    userModelFineTuneRepository;
    cdnService;
    logger = new common_1.Logger(UserModelsService_1.name);
    constructor(userModelsRepository, systemModelsRepository, userDataFineTuneRepository, userKeyLlmRepository, userModelFineTuneRepository, cdnService) {
        this.userModelsRepository = userModelsRepository;
        this.systemModelsRepository = systemModelsRepository;
        this.userDataFineTuneRepository = userDataFineTuneRepository;
        this.userKeyLlmRepository = userKeyLlmRepository;
        this.userModelFineTuneRepository = userModelFineTuneRepository;
        this.cdnService = cdnService;
    }
    isValidUUID(uuid) {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        return uuidRegex.test(uuid);
    }
    async validateUserKeyLlm(userId, keyllmId) {
        try {
            if (!this.isValidUUID(keyllmId)) {
                this.logger.warn(`Invalid UUID format for keyllmId: ${keyllmId}`);
                throw new exceptions_1.AppException(models_exception_1.MODELS_ERROR_CODES.USER_KEY_LLM_INVALID_UUID);
            }
            const userKeyLlm = await this.userKeyLlmRepository.findOne({
                where: {
                    id: keyllmId,
                    userId: userId,
                    deletedAt: (0, typeorm_1.IsNull)()
                }
            });
            if (!userKeyLlm) {
                this.logger.warn(`Invalid keyllmId ${keyllmId} for user ${userId}`);
                throw new exceptions_1.AppException(models_exception_1.MODELS_ERROR_CODES.USER_KEY_LLM_NOT_FOUND);
            }
            this.logger.log(`Validated keyllmId ${keyllmId} for user ${userId}`);
        }
        catch (error) {
            if (error instanceof exceptions_1.AppException) {
                throw error;
            }
            this.logger.error(`Failed to validate keyllmId ${keyllmId} for user ${userId}: ${error.message}`, error.stack);
            throw new exceptions_1.AppException(exceptions_1.ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể xác thực LLM Key');
        }
    }
    async getUserModelsByKeys(userId, keyllmId, queryDto) {
        try {
            this.logger.log(`Getting user models by keys for user ${userId}, keyllmId ${keyllmId}`);
            await this.validateUserKeyLlm(userId, keyllmId);
            const result = await this.userModelsRepository.findByUserIdWithPagination(userId, keyllmId, queryDto);
            const items = mappers_1.UserModelsMapper.toResponseDtoList(result.items);
            const hasItems = result.meta.totalItems > 0;
            return response_1.ApiResponseDto.paginated({
                items,
                meta: {
                    ...result.meta,
                    hasItems,
                }
            }, 'Lấy danh sách models theo keys thành công');
        }
        catch (error) {
            this.logger.error(`Failed to get user models by keys for user ${userId}: ${error.message}`, error.stack);
            throw new exceptions_1.AppException(exceptions_1.ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể lấy danh sách models theo keys');
        }
    }
    async getSystemModels(queryDto) {
        try {
            this.logger.log('Getting active system models');
            const result = await this.systemModelsRepository.findActiveWithPagination(queryDto);
            const items = mappers_1.SystemModelsMapper.toResponseDtoList(result.items);
            const hasItems = result.meta.totalItems > 0;
            return response_1.ApiResponseDto.paginated({
                items,
                meta: {
                    ...result.meta,
                    hasItems,
                }
            }, 'Lấy danh sách system models thành công');
        }
        catch (error) {
            this.logger.error(`Failed to get system models: ${error.message}`, error.stack);
            throw new exceptions_1.AppException(exceptions_1.ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể lấy danh sách system models');
        }
    }
    async getUserFineTuneDatasets(userId, queryDto) {
        try {
            this.logger.log(`Getting user model fine-tune for user ${userId}`);
            const result = await this.userModelFineTuneRepository.findByUserIdWithPagination(userId, queryDto);
            const items = mappers_1.UserModelFineTuneMapper.toResponseDtoList(result.items);
            const hasItems = result.meta.totalItems > 0;
            return response_1.ApiResponseDto.paginated({
                items,
                meta: {
                    ...result.meta,
                    hasItems,
                }
            }, 'Lấy danh sách user model fine-tune thành công');
        }
        catch (error) {
            this.logger.error(`Failed to get user model fine-tune for user ${userId}: ${error.message}`, error.stack);
            throw new exceptions_1.AppException(exceptions_1.ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể lấy danh sách user model fine-tune');
        }
    }
};
exports.UserModelsService = UserModelsService;
exports.UserModelsService = UserModelsService = UserModelsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [user_models_repository_1.UserModelsRepository,
        system_models_repository_1.SystemModelsRepository,
        user_data_fine_tune_repository_1.UserDataFineTuneRepository,
        user_key_llm_repository_1.UserKeyLlmRepository,
        user_model_fine_tune_repository_1.UserModelFineTuneRepository,
        cdn_service_1.CdnService])
], UserModelsService);
//# sourceMappingURL=user-models.service.js.map