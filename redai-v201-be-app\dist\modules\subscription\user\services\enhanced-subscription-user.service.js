"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedSubscriptionUserService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const subscription_entity_1 = require("../../entities/subscription.entity");
const plan_pricing_entity_1 = require("../../entities/plan-pricing.entity");
const plan_entity_1 = require("../../entities/plan.entity");
const order_plan_history_entity_1 = require("../../entities/order-plan-history.entity");
const subscription_repository_1 = require("../../repositories/subscription.repository");
const entities_1 = require("../../../user/entities");
const enums_1 = require("../../enums");
let EnhancedSubscriptionUserService = class EnhancedSubscriptionUserService {
    subscriptionRepository;
    planPricingRepository;
    planRepository;
    userRepository;
    orderPlanHistoryRepository;
    subscriptionCustomRepository;
    dataSource;
    constructor(subscriptionRepository, planPricingRepository, planRepository, userRepository, orderPlanHistoryRepository, subscriptionCustomRepository, dataSource) {
        this.subscriptionRepository = subscriptionRepository;
        this.planPricingRepository = planPricingRepository;
        this.planRepository = planRepository;
        this.userRepository = userRepository;
        this.orderPlanHistoryRepository = orderPlanHistoryRepository;
        this.subscriptionCustomRepository = subscriptionCustomRepository;
        this.dataSource = dataSource;
    }
    async createSubscription(userId, planPricingId, autoRenew = true) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const planPricing = await this.planPricingRepository.findOne({
                where: { id: planPricingId, isActive: true },
            });
            if (!planPricing) {
                throw new common_1.NotFoundException(`PlanPricing with ID ${planPricingId} not found or not active`);
            }
            const plan = await this.planRepository.findOne({
                where: { id: planPricing.planId },
            });
            if (!plan) {
                throw new common_1.NotFoundException(`Plan with ID ${planPricing.planId} not found`);
            }
            const user = await this.userRepository.findOne({
                where: { id: userId },
            });
            if (!user) {
                throw new common_1.NotFoundException(`User with ID ${userId} not found`);
            }
            const activeSubscriptions = await this.subscriptionRepository.find({
                where: { userId, status: enums_1.SubscriptionStatus.ACTIVE },
            });
            if (activeSubscriptions.length > 0) {
                throw new common_1.BadRequestException('User already has active subscriptions');
            }
            const pointsRequired = Number(planPricing.price);
            if (user.pointsBalance < pointsRequired) {
                throw new common_1.BadRequestException(`User does not have enough points. Required: ${pointsRequired}, Available: ${user.pointsBalance}`);
            }
            const now = Date.now();
            let endDate;
            if (plan.packageType === enums_1.PackageType.TIME_ONLY ||
                plan.packageType === enums_1.PackageType.HYBRID) {
                switch (planPricing.billingCycle) {
                    case 'MONTHLY':
                        endDate = now + 30 * 24 * 60 * 60 * 1000;
                        break;
                    case 'QUARTERLY':
                        endDate = now + 90 * 24 * 60 * 60 * 1000;
                        break;
                    case 'YEARLY':
                        endDate = now + 365 * 24 * 60 * 60 * 1000;
                        break;
                    default:
                        endDate = now + 30 * 24 * 60 * 60 * 1000;
                }
            }
            else {
                endDate = now + 365 * 5 * 24 * 60 * 60 * 1000;
            }
            let usageLimit = 0;
            let remainingValue = 0;
            if (plan.packageType === enums_1.PackageType.USAGE_BASED ||
                plan.packageType === enums_1.PackageType.HYBRID) {
                usageLimit = planPricing.usageLimit;
                remainingValue = planPricing.usageLimit;
            }
            const subscription = this.subscriptionRepository.create({
                userId,
                planPricingId,
                startDate: now,
                endDate,
                autoRenew,
                status: enums_1.SubscriptionStatus.ACTIVE,
                usageLimit,
                currentUsage: 0,
                remainingValue,
                usageUnit: planPricing.usageUnit,
                createdAt: now,
                updatedAt: now,
            });
            const savedSubscription = await queryRunner.manager.save(subscription);
            user.pointsBalance -= pointsRequired;
            await queryRunner.manager.save(user);
            const orderHistory = this.orderPlanHistoryRepository.create({
                userId,
                planId: plan.id,
                planPricingId,
                subscriptionId: savedSubscription.id,
                planName: plan.name,
                point: pointsRequired.toString(),
                billingCycle: planPricing.billingCycle,
                usageLimit: planPricing.usageLimit
                    ? planPricing.usageLimit.toString()
                    : null,
                usageUnit: planPricing.usageUnit,
                createdAt: now.toString(),
            });
            await queryRunner.manager.save(orderHistory);
            await queryRunner.commitTransaction();
            return savedSubscription;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
};
exports.EnhancedSubscriptionUserService = EnhancedSubscriptionUserService;
exports.EnhancedSubscriptionUserService = EnhancedSubscriptionUserService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(subscription_entity_1.Subscription)),
    __param(1, (0, typeorm_1.InjectRepository)(plan_pricing_entity_1.PlanPricing)),
    __param(2, (0, typeorm_1.InjectRepository)(plan_entity_1.Plan)),
    __param(3, (0, typeorm_1.InjectRepository)(entities_1.User)),
    __param(4, (0, typeorm_1.InjectRepository)(order_plan_history_entity_1.OrderPlanHistory)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        subscription_repository_1.SubscriptionRepository,
        typeorm_2.DataSource])
], EnhancedSubscriptionUserService);
//# sourceMappingURL=enhanced-subscription-user.service.js.map