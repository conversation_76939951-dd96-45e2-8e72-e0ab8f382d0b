"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorStoreListResponseSchema = exports.VectorStoreSchema = void 0;
const swagger_1 = require("@nestjs/swagger");
class VectorStoreSchema {
    id;
    name;
    storage;
    ownerType;
    ownerId;
    createdAt;
    updateAt;
    constructor(partial) {
        Object.assign(this, partial);
    }
}
exports.VectorStoreSchema = VectorStoreSchema;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của vector store',
        example: 'vs_123e4567-e89b-12d3-a456-426614174000',
    }),
    __metadata("design:type", String)
], VectorStoreSchema.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên vector store',
        example: 'Kho vector tài liệu NestJS',
    }),
    __metadata("design:type", String)
], VectorStoreSchema.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Dung lượng đã dùng (bytes/tokens)',
        example: 1024000,
    }),
    __metadata("design:type", Number)
], VectorStoreSchema.prototype, "storage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại chủ sở hữu',
        example: 'user',
        enum: ['user', 'employee'],
    }),
    __metadata("design:type", String)
], VectorStoreSchema.prototype, "ownerType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của chủ sở hữu',
        example: 1,
    }),
    __metadata("design:type", Number)
], VectorStoreSchema.prototype, "ownerId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời điểm tạo (unix timestamp)',
        example: 1625097600000,
    }),
    __metadata("design:type", Number)
], VectorStoreSchema.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời điểm cập nhật (unix timestamp)',
        example: 1625097600000,
    }),
    __metadata("design:type", Number)
], VectorStoreSchema.prototype, "updateAt", void 0);
class VectorStoreListResponseSchema {
    items;
    meta;
}
exports.VectorStoreListResponseSchema = VectorStoreListResponseSchema;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách vector store',
        type: [VectorStoreSchema],
    }),
    __metadata("design:type", Array)
], VectorStoreListResponseSchema.prototype, "items", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin phân trang',
        type: 'object',
        properties: {
            totalItems: {
                type: 'number',
                example: 100,
                description: 'Tổng số vector store',
            },
            itemCount: {
                type: 'number',
                example: 10,
                description: 'Số vector store trên trang hiện tại',
            },
            itemsPerPage: {
                type: 'number',
                example: 10,
                description: 'Số vector store trên mỗi trang',
            },
            totalPages: {
                type: 'number',
                example: 10,
                description: 'Tổng số trang',
            },
            currentPage: {
                type: 'number',
                example: 1,
                description: 'Trang hiện tại',
            },
        },
    }),
    __metadata("design:type", Object)
], VectorStoreListResponseSchema.prototype, "meta", void 0);
//# sourceMappingURL=vector-store.schema.js.map