{"version": 3, "sources": ["../src/app.module.ts"], "sourcesContent": ["import { Modu<PERSON> } from '@nestjs/common';\r\nimport { BullModule } from '@nestjs/bullmq';\r\nimport { env } from './config';\r\nimport { QueueModule } from './queue';\r\nimport { AgentModule } from './agent';\r\nimport { InfraModule } from './infra';\r\nimport { SharedModule } from './shared/shared.module';\r\nimport Redis from 'ioredis';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { databaseConfig } from './config';\r\nimport { DatabaseModule } from './modules/database';\r\nimport { EmailSystemModule } from './modules/email-system/email-system.module';\r\nimport { EmailMarketingModule } from './modules/marketing/email/email-marketing.module';\r\n\r\n@Module({\r\n  imports: [\r\n    SharedModule,\r\n    QueueModule,\r\n    InfraModule,\r\n    AgentModule,\r\n    DatabaseModule,\r\n    EmailSystemModule,\r\n    EmailMarketingModule,\r\n    TypeOrmModule.forRoot(databaseConfig),\r\n    BullModule.forRoot({\r\n      connection: {\r\n        url: env.external.REDIS_URL,\r\n      },\r\n    }),\r\n  ],\r\n  providers: [\r\n    {\r\n      provide: Redis,\r\n      useFactory: () => {\r\n        return new Redis(env.external.REDIS_URL);\r\n      },\r\n    },\r\n  ],\r\n})\r\nexport class AppModule {}\r\n"], "names": ["AppModule", "imports", "SharedModule", "QueueModule", "InfraModule", "AgentModule", "DatabaseModule", "EmailSystemModule", "EmailMarketingModule", "TypeOrmModule", "forRoot", "databaseConfig", "BullModule", "connection", "url", "env", "external", "REDIS_URL", "providers", "provide", "Redis", "useFactory"], "mappings": ";;;;+BAuCaA;;;eAAAA;;;wBAvCU;wBACI;wBACP;uBACQ;uBACA;uBACA;8BACC;gEACX;yBACY;0BAEC;mCACG;sCACG;;;;;;;;;;;;AA2B9B,IAAA,AAAMA,YAAN,MAAMA;AAAW;;;QAxBtBC,SAAS;YACPC,0BAAY;YACZC,kBAAW;YACXC,kBAAW;YACXC,kBAAW;YACXC,wBAAc;YACdC,oCAAiB;YACjBC,0CAAoB;YACpBC,sBAAa,CAACC,OAAO,CAACC,sBAAc;YACpCC,kBAAU,CAACF,OAAO,CAAC;gBACjBG,YAAY;oBACVC,KAAKC,WAAG,CAACC,QAAQ,CAACC,SAAS;gBAC7B;YACF;SACD;QACDC,WAAW;YACT;gBACEC,SAASC,gBAAK;gBACdC,YAAY;oBACV,OAAO,IAAID,gBAAK,CAACL,WAAG,CAACC,QAAQ,CAACC,SAAS;gBACzC;YACF;SACD"}