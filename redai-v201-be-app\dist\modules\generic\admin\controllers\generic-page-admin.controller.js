"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericPageAdminController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const guards_1 = require("../../../auth/guards");
const current_employee_decorator_1 = require("../../../auth/decorators/current-employee.decorator");
const response_1 = require("../../../../common/response");
const services_1 = require("../services");
const dto_1 = require("../dto");
let GenericPageAdminController = class GenericPageAdminController {
    genericPageAdminService;
    constructor(genericPageAdminService) {
        this.genericPageAdminService = genericPageAdminService;
    }
    async createGenericPage(createGenericPageDto, employee) {
        const result = await this.genericPageAdminService.createGenericPage(createGenericPageDto, String(employee.id));
        return response_1.ApiResponseDto.created(result);
    }
    async updateGenericPage(id, updateGenericPageDto, employee) {
        const result = await this.genericPageAdminService.updateGenericPage(id, updateGenericPageDto, String(employee.id));
        return response_1.ApiResponseDto.success(result);
    }
    async getGenericPageById(id) {
        const result = await this.genericPageAdminService.getGenericPageById(id);
        return response_1.ApiResponseDto.success(result);
    }
    async getGenericPageByPath(path) {
        const result = await this.genericPageAdminService.getGenericPageByPath(path);
        return response_1.ApiResponseDto.success(result);
    }
    async publishGenericPage(id, employee) {
        const result = await this.genericPageAdminService.publishGenericPage(id, String(employee.id));
        return response_1.ApiResponseDto.success(result);
    }
    async unpublishGenericPage(id, employee) {
        const result = await this.genericPageAdminService.unpublishGenericPage(id, String(employee.id));
        return response_1.ApiResponseDto.success(result);
    }
    async deleteGenericPage(id) {
        await this.genericPageAdminService.deleteGenericPage(id);
        return response_1.ApiResponseDto.success(null, 'Trang đã được xóa thành công');
    }
};
exports.GenericPageAdminController = GenericPageAdminController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo trang mới' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Trang đã được tạo',
        schema: response_1.ApiResponseDto.getSchema(dto_1.GenericPageResponseDto),
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_employee_decorator_1.CurrentEmployee)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateGenericPageDto, Object]),
    __metadata("design:returntype", Promise)
], GenericPageAdminController.prototype, "createGenericPage", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật trang' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID của trang' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Trang đã được cập nhật',
        schema: response_1.ApiResponseDto.getSchema(dto_1.GenericPageResponseDto),
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_employee_decorator_1.CurrentEmployee)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateGenericPageDto, Object]),
    __metadata("design:returntype", Promise)
], GenericPageAdminController.prototype, "updateGenericPage", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin trang theo ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID của trang' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Thông tin trang',
        schema: response_1.ApiResponseDto.getSchema(dto_1.GenericPageResponseDto),
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], GenericPageAdminController.prototype, "getGenericPageById", null);
__decorate([
    (0, common_1.Get)('by-path/:path'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin trang theo đường dẫn' }),
    (0, swagger_1.ApiParam)({ name: 'path', description: 'Đường dẫn của trang' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Thông tin trang',
        schema: response_1.ApiResponseDto.getSchema(dto_1.GenericPageResponseDto),
    }),
    __param(0, (0, common_1.Param)('path')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], GenericPageAdminController.prototype, "getGenericPageByPath", null);
__decorate([
    (0, common_1.Post)(':id/publish'),
    (0, swagger_1.ApiOperation)({ summary: 'Xuất bản trang' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID của trang' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Trang đã được xuất bản',
        schema: response_1.ApiResponseDto.getSchema(dto_1.GenericPageResponseDto),
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_employee_decorator_1.CurrentEmployee)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], GenericPageAdminController.prototype, "publishGenericPage", null);
__decorate([
    (0, common_1.Post)(':id/unpublish'),
    (0, swagger_1.ApiOperation)({ summary: 'Hủy xuất bản trang' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID của trang' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Trang đã được hủy xuất bản',
        schema: response_1.ApiResponseDto.getSchema(dto_1.GenericPageResponseDto),
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_employee_decorator_1.CurrentEmployee)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], GenericPageAdminController.prototype, "unpublishGenericPage", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Xóa trang' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID của trang' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Trang đã được xóa',
        schema: response_1.ApiResponseDto.getSchema(null),
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], GenericPageAdminController.prototype, "deleteGenericPage", null);
exports.GenericPageAdminController = GenericPageAdminController = __decorate([
    (0, swagger_1.ApiTags)('Admin Generic Page'),
    (0, common_1.Controller)('admin/generic-pages'),
    (0, common_1.UseGuards)(guards_1.JwtEmployeeGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiExtraModels)(response_1.ApiResponseDto, dto_1.GenericPageResponseDto, response_1.PaginatedResult),
    __metadata("design:paramtypes", [services_1.GenericPageAdminService])
], GenericPageAdminController);
//# sourceMappingURL=generic-page-admin.controller.js.map