{"version": 3, "sources": ["../../../src/agent/controllers/redis-event.controller.ts"], "sourcesContent": ["import { <PERSON>, Logger } from '@nestjs/common';\r\nimport { EventPattern, Payload } from '@nestjs/microservices';\r\nimport { UserAgentRunsQueries, UserMessagesQueries } from '../database';\r\nimport { REDIS_EVENTS, RunTriggerEvent, RunCancelEvent } from '../constants';\r\nimport { RedisService } from '../../infra/redis';\r\nimport { CustomConfigurableType } from '../system/core/react-agent-executor';\r\nimport { HumanMessage, ToolMessage } from '@langchain/core/messages';\r\nimport { Command } from '@langchain/langgraph';\r\nimport { workflow } from '../system/core/multi-agent';\r\nimport {\r\n  SUPERVISOR_TAG,\r\n  WORKER_TAG,\r\n  SUPERVISOR_TOOL_CALL_TAG,\r\n  WORKER_TOOL_CALL_TAG\r\n} from '../system/core/constants';\r\nimport { ApiKeyEncryptionHelper } from '../helper/api-key-encryption.helper';\r\n\r\n// Role tags for event transformation\r\nconst ROLE_TAGS = [SUPERVISOR_TAG, WORKER_TAG];\r\nconst TOOL_CALL_TAGS = [SUPERVISOR_TOOL_CALL_TAG, WORKER_TOOL_CALL_TAG];\r\n\r\n/**\r\n * Redis Event Controller for Worker\r\n *\r\n * This controller handles incoming Redis pub/sub events from the backend API\r\n * and processes agent runs using LangGraph thread-based architecture.\r\n *\r\n * LangGraph Architecture:\r\n * - Processes runs by threadId, not runId\r\n * - Uses AbortController for cancellation\r\n * - Maintains global threadId → AbortController mapping\r\n * - Cancellation by threadId ignores non-existent threads (as per LangGraph pattern)\r\n *\r\n * Note: @EventPattern handlers MUST be inside a @Controller() class in NestJS microservices.\r\n */\r\n@Controller()\r\nexport class RedisEventController {\r\n  private readonly logger = new Logger(RedisEventController.name);\r\n\r\n  // Global thread management for LangGraph\r\n  private readonly activeThreads = new Map<string, AbortController>(); // threadId → AbortController\r\n  private readonly threadToRun = new Map<string, string>(); // threadId → runId (for logging)\r\n\r\n  constructor(\r\n    private readonly userAgentRunsQueries: UserAgentRunsQueries,\r\n    private readonly userMessagesQueries: UserMessagesQueries,\r\n    private readonly redisService: RedisService,\r\n    private readonly apiKeyEncryptionHelper: ApiKeyEncryptionHelper,\r\n  ) {}\r\n\r\n  /**\r\n   * Handle run trigger events from backend\r\n   * @param data Run trigger event payload\r\n   */\r\n  @EventPattern(REDIS_EVENTS.RUN_TRIGGER)\r\n  async handleRunTrigger(@Payload() data: RunTriggerEvent): Promise<void> {\r\n    try {\r\n      this.logger.log(`Received run trigger event for thread ${data.threadId}, run ${data.runId}`);\r\n\r\n      // Validate event payload\r\n      if (!this.validateRunTriggerEvent(data)) {\r\n        this.logger.error(`Invalid run trigger event payload for thread ${data?.['threadId']}`);\r\n        return;\r\n      }\r\n\r\n      // Check if thread is already being processed\r\n      if (this.activeThreads.has(data.threadId)) {\r\n        this.logger.warn(`Thread ${data.threadId} is already being processed, ignoring duplicate trigger`);\r\n        return;\r\n      }\r\n\r\n      // Create AbortController for this thread (LangGraph pattern)\r\n      const abortController = new AbortController();\r\n      this.activeThreads.set(data.threadId, abortController);\r\n      this.threadToRun.set(data.threadId, data.runId);\r\n\r\n      // Fetch run data from database\r\n      const runData = await this.userAgentRunsQueries.getRunById(data.runId);\r\n\r\n      if (!runData) {\r\n        this.logger.error(`Run ${data.runId} not found in database for thread ${data.threadId}`);\r\n        this.cleanupThread(data.threadId);\r\n        return;\r\n      }\r\n\r\n      this.logger.log(`Processing thread ${data.threadId} for agent ${data.agentId} (run ${data.runId}) with JWT`);\r\n\r\n      // TODO: Integrate with LangGraph AgentWorker for actual processing\r\n      // This will be implemented in Task 11\r\n      await this.processAgentThread(data.threadId, runData, abortController, data.jwt);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Error handling run trigger for thread ${data.threadId}:`, {\r\n        message: error.message,\r\n        stack: error.stack,\r\n        name: error.name,\r\n        threadId: data.threadId,\r\n        runId: data.runId,\r\n        error: error,\r\n      });\r\n      this.cleanupThread(data.threadId);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handle run cancel events from backend\r\n   * @param data Run cancel event payload\r\n   */\r\n  @EventPattern(REDIS_EVENTS.RUN_CANCEL)\r\n  async handleRunCancel(@Payload() data: RunCancelEvent): Promise<void> {\r\n    try {\r\n      this.logger.log(`Received run cancel event for thread ${data.threadId}: ${data.reason}`);\r\n\r\n      // Validate event payload\r\n      if (!this.validateRunCancelEvent(data)) {\r\n        this.logger.error(`Invalid run cancel event payload for thread ${data?.['threadId']}`);\r\n        return;\r\n      }\r\n\r\n      // LangGraph pattern: Cancel by threadId, ignore if not active\r\n      const abortController = this.activeThreads.get(data.threadId);\r\n      if (!abortController) {\r\n        this.logger.log(`Thread ${data.threadId} is not active, ignoring cancel request (this is normal)`);\r\n        return;\r\n      }\r\n\r\n      // Abort the thread using LangGraph AbortController pattern\r\n      abortController.abort();\r\n      this.logger.log(`Cancelled thread ${data.threadId}: ${data.reason}`);\r\n\r\n      // Emit cancellation event to Redis Streams\r\n      try {\r\n        const producer = this.redisService.getRawClient();\r\n        await this.emitStream(producer, {\r\n          threadId: data.threadId,\r\n          event: 'stream_cancelled',\r\n          data: { reason: data.reason, timestamp: Date.now() },\r\n        });\r\n      } catch (error) {\r\n        this.logger.warn(`Failed to emit cancellation event for thread ${data.threadId}:`, error);\r\n      }\r\n\r\n      // Cleanup thread resources\r\n      this.cleanupThread(data.threadId);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Error handling run cancel for thread ${data.threadId}:`, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate run trigger event payload\r\n   * @param data Event data to validate\r\n   * @returns True if valid\r\n   */\r\n  private validateRunTriggerEvent(data: any): data is RunTriggerEvent {\r\n    return (\r\n      data &&\r\n      typeof data.runId === 'string' &&\r\n      typeof data.threadId === 'string' &&\r\n      typeof data.agentId === 'string' &&\r\n      typeof data.userId === 'number' &&\r\n      typeof data.jwt === 'string' &&\r\n      typeof data.timestamp === 'number' &&\r\n      data.eventType === REDIS_EVENTS.RUN_TRIGGER\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Validate run cancel event payload\r\n   * @param data Event data to validate\r\n   * @returns True if valid\r\n   */\r\n  private validateRunCancelEvent(data: any): data is RunCancelEvent {\r\n    return (\r\n      data &&\r\n      typeof data.threadId === 'string' &&\r\n      typeof data.reason === 'string' &&\r\n      typeof data.timestamp === 'number' &&\r\n      data.eventType === REDIS_EVENTS.RUN_CANCEL\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Process agent thread with LangGraph (placeholder for Task 19)\r\n   * @param threadId LangGraph thread ID\r\n   * @param runData Run data from database\r\n   * @param abortController AbortController for cancellation\r\n   * @param jwt JWT token for authenticated API calls\r\n   */\r\n  private async processAgentThread(\r\n    threadId: string,\r\n    runData: any,\r\n    abortController: AbortController,\r\n    jwt: string\r\n  ): Promise<void> {\r\n    this.logger.debug(`Processing LangGraph thread ${threadId} with run ${runData.id}:`, {\r\n      threadId,\r\n      runId: runData.id,\r\n      status: runData.status,\r\n      payloadSize: JSON.stringify(runData.payload).length,\r\n      aborted: abortController.signal.aborted,\r\n      hasJwt: !!jwt,\r\n      jwtLength: jwt.length,\r\n    });\r\n\r\n    // Get Redis client for streaming events\r\n    const producer = this.redisService.getRawClient();\r\n\r\n    // Build CustomConfigurableType from payload data (includes API key decryption)\r\n    const customConfig = this.buildCustomConfigurable(runData, threadId);\r\n\r\n    // Get decrypted payload for message extraction\r\n    const userId = runData.created_by;\r\n    const decryptedPayload = this.decryptApiKeysInPayload(runData.payload, userId);\r\n\r\n    this.logger.log(`Starting LangGraph streaming for thread ${threadId} (run ${runData.id})`);\r\n    this.logger.debug(`CustomConfigurableType built:`, {\r\n      alwaysApproveToolCall: customConfig.alwaysApproveToolCall,\r\n      thread_id: customConfig.thread_id,\r\n      supervisorAgentId: customConfig.supervisorAgentId,\r\n      agentConfigCount: Object.keys(customConfig.agentConfigMap || {}).length,\r\n    });\r\n\r\n    // Prepare to accumulate partial tokens\r\n    const partialTokens: string[] = [];\r\n\r\n    try {\r\n      // Check if already aborted before starting\r\n      if (abortController.signal.aborted) {\r\n        this.logger.log(`Thread ${threadId} was cancelled before processing started`);\r\n        return;\r\n      }\r\n\r\n      // Extract message data from decrypted payload\r\n      const messageContent = decryptedPayload?.message?.content || '';\r\n\r\n      // Validate message content\r\n      if (!messageContent || messageContent.trim() === '') {\r\n        this.logger.error(`Empty message content for thread ${threadId}`, {\r\n          hasPayload: !!decryptedPayload,\r\n          hasMessage: !!decryptedPayload?.message,\r\n          messageContent: messageContent,\r\n          payloadStructure: {\r\n            keys: decryptedPayload ? Object.keys(decryptedPayload) : [],\r\n            messageKeys: decryptedPayload?.message ? Object.keys(decryptedPayload.message) : [],\r\n            messageType: typeof decryptedPayload?.message,\r\n            contentType: typeof decryptedPayload?.message?.content,\r\n          },\r\n          rawPayloadSample: JSON.stringify(decryptedPayload).substring(0, 500),\r\n        });\r\n        throw new Error('Message content is empty or invalid');\r\n      }\r\n\r\n      // Build input for multi-agent LangGraph workflow\r\n      const humanMessage = new HumanMessage(messageContent);\r\n      const input = {\r\n        messages: [humanMessage],\r\n        activeAgent: customConfig.supervisorAgentId || 'supervisor',\r\n      };\r\n\r\n      this.logger.debug(`Starting LangGraph streamEvents with config:`, {\r\n        threadId,\r\n        activeAgent: input.activeAgent,\r\n        messageLength: messageContent.length,\r\n        messagePreview: messageContent.substring(0, 100),\r\n        alwaysApproveToolCall: customConfig.alwaysApproveToolCall,\r\n        humanMessageType: humanMessage.constructor.name,\r\n        hasValidMessage: !!humanMessage.content,\r\n      });\r\n\r\n      // Start multi-agent LangGraph streaming\r\n      const streaming = workflow.streamEvents(input, {\r\n        configurable: customConfig,\r\n        subgraphs: true,\r\n        recursionLimit: 1000,\r\n        version: 'v2' as const,\r\n        signal: abortController.signal,\r\n      });\r\n      const streamingIterator = streaming[Symbol.asyncIterator]();\r\n\r\n      // Set up abort signal listener for graceful cancellation\r\n      const abortListener = async () => {\r\n        this.logger.log(`Thread ${threadId} processing was cancelled`);\r\n        // Force-close the LLM stream generator\r\n        await streamingIterator?.return?.();\r\n      };\r\n      abortController.signal.addEventListener('abort', abortListener);\r\n\r\n      // Process LangGraph events and transform to frontend events\r\n      for await (const { event, data, tags = [] } of streamingIterator) {\r\n        if (abortController.signal.aborted) {\r\n          break;\r\n        }\r\n\r\n        // Chat model streaming events\r\n        if (\r\n          event === 'on_chat_model_stream' &&\r\n          tags.some((t: string) => ROLE_TAGS.includes(t))\r\n        ) {\r\n          const role = this.getRoleFromTags(tags, ROLE_TAGS)!;\r\n          if (data.chunk?.content) {\r\n            const text = data.chunk.content;\r\n            partialTokens.push(text); // Accumulate tokens for potential persistence\r\n\r\n            // Log actual text content with smart truncation\r\n            const displayText = text.length > 50 ? `${text.substring(0, 50)}...` : text;\r\n            const safeText = displayText.replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r');\r\n\r\n            this.logger.debug(`📝 Streaming text token [${role}]: \"${safeText}\" (${text.length} chars)`, {\r\n              threadId,\r\n              role,\r\n              textLength: text.length,\r\n              fullText: text.length <= 20 ? text : undefined, // Include full text for short tokens\r\n            });\r\n\r\n            await this.emitStream(producer, {\r\n              threadId,\r\n              event: 'stream_text_token',\r\n              data: { role, text },\r\n            });\r\n          } else {\r\n            this.logger.debug(`🔧 Streaming tool token [${role}]`, { threadId, role });\r\n            await this.emitStream(producer, {\r\n              threadId,\r\n              event: 'stream_tool_token',\r\n              data: { role },\r\n            });\r\n          }\r\n\r\n          // Tool start/end events\r\n        } else if (['on_tool_start', 'on_tool_end'].includes(event)) {\r\n          const role = this.getRoleFromTags(tags, TOOL_CALL_TAGS)!;\r\n          const eventType = event === 'on_tool_start' ? 'tool_call_start' : 'tool_call_end';\r\n\r\n          // Enhanced tool logging with tool name if available\r\n          let toolInfo = '';\r\n          let toolName: string | undefined;\r\n\r\n          if (data && typeof data === 'object' && 'name' in data) {\r\n            toolName = (data as any).name;\r\n            toolInfo = ` (${toolName})`;\r\n          }\r\n\r\n          this.logger.debug(`🔧 Tool ${eventType} [${role}]${toolInfo}`, {\r\n            threadId,\r\n            role,\r\n            toolName,\r\n            eventType,\r\n          });\r\n\r\n          await this.emitStream(producer, {\r\n            threadId,\r\n            event: eventType,\r\n            data: { role, toolName },\r\n          });\r\n\r\n          // Interrupt events (user decisions)\r\n        } else if (\r\n          event === 'on_chain_stream' &&\r\n          data.chunk?.[2]?.['__interrupt__']\r\n        ) {\r\n          const [{ value }] = data.chunk[2]['__interrupt__'];\r\n          const interruptValue = JSON.parse(value);\r\n\r\n          // Enhanced interrupt logging\r\n          const displayValue = JSON.stringify(interruptValue).length > 100\r\n            ? `${JSON.stringify(interruptValue).substring(0, 100)}...`\r\n            : JSON.stringify(interruptValue);\r\n\r\n          this.logger.debug(`⚠️ Tool call interrupt: ${displayValue}`, {\r\n            threadId,\r\n            interruptType: interruptValue.role || 'unknown',\r\n            hasPrompt: !!interruptValue.prompt,\r\n          });\r\n\r\n          await this.emitStream(producer, {\r\n            threadId,\r\n            event: 'tool_call_interrupt',\r\n            data: interruptValue,\r\n          });\r\n        }\r\n      }\r\n\r\n      // If not cancelled, emit the \"end of stream\" marker\r\n      if (!abortController.signal.aborted) {\r\n        // Log complete response summary\r\n        const completeResponse = partialTokens.join('');\r\n        const responsePreview = completeResponse.length > 200\r\n          ? `${completeResponse.substring(0, 200)}...`\r\n          : completeResponse;\r\n        const safePreview = responsePreview.replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r');\r\n\r\n        this.logger.log(`✅ LangGraph thread ${threadId} completed: \"${safePreview}\"`, {\r\n          threadId,\r\n          runId: runData.id,\r\n          totalTokens: partialTokens.length,\r\n          totalChars: completeResponse.length,\r\n          responsePreview: safePreview,\r\n        });\r\n\r\n        await this.emitStream(producer, {\r\n          threadId,\r\n          event: 'llm_stream_end',\r\n          data: { threadId, runId: runData.id },\r\n        });\r\n\r\n        // Save complete assistant response to database\r\n        if (partialTokens.length > 0) {\r\n          await this.saveCompleteResponse(threadId, partialTokens.join(''), userId);\r\n        }\r\n\r\n        this.cleanupThread(threadId);\r\n      }\r\n\r\n    } catch (error) {\r\n      const isAbortError = abortController.signal.aborted ||\r\n        (error instanceof TypeError && error.message.includes('Invalid state: The reader is not attached to a stream'));\r\n\r\n      if (isAbortError) {\r\n        // Swallow it: this is expected on cancel\r\n        this.logger.log('LangGraph stream aborted cleanly.');\r\n      } else {\r\n        // Unexpected error: log with full stack trace, emit error event, and cleanup\r\n        this.logger.error(`Error in LangGraph streaming for ${threadId}:`, {\r\n          message: error.message,\r\n          stack: error.stack,\r\n          name: error.name,\r\n          threadId,\r\n          runId: runData.id,\r\n          error: error,\r\n        });\r\n\r\n        // Emit error event to SSE stream so frontend knows about the failure\r\n        try {\r\n          await this.emitStream(producer, {\r\n            threadId,\r\n            event: 'stream_error',\r\n            data: {\r\n              error: error.message || 'Unknown streaming error',\r\n              errorName: error.name,\r\n              stack: error.stack,\r\n              threadId,\r\n              runId: runData.id,\r\n              timestamp: Date.now(),\r\n            },\r\n          });\r\n\r\n          // Also emit stream end to properly close the SSE connection\r\n          await this.emitStream(producer, {\r\n            threadId,\r\n            event: 'llm_stream_end',\r\n            data: { threadId, runId: runData.id, error: true },\r\n          });\r\n        } catch (emitError) {\r\n          this.logger.error(`Failed to emit error event for thread ${threadId}:`, emitError);\r\n        }\r\n\r\n        this.cleanupThread(threadId);\r\n        // Don't re-throw the error to prevent it from bubbling up to the event handler\r\n      }\r\n    } finally {\r\n      // On cancellation, persist the partial tokens\r\n      if (abortController.signal.aborted && partialTokens.length > 0) {\r\n        const partialText = partialTokens.join('');\r\n        const displayText = partialText.length > 100 ? `${partialText.substring(0, 100)}...` : partialText;\r\n        const safeText = displayText.replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r');\r\n\r\n        this.logger.log(`💾 Saving partial response for ${threadId}: \"${safeText}\"`, {\r\n          tokenCount: partialTokens.length,\r\n          textLength: partialText.length,\r\n          preview: safeText,\r\n        });\r\n        await this.savePartialResponse(threadId, partialText);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cleanup thread resources\r\n   * @param threadId Thread ID to cleanup\r\n   */\r\n  private cleanupThread(threadId: string): void {\r\n    const runId = this.threadToRun.get(threadId);\r\n    this.activeThreads.delete(threadId);\r\n    this.threadToRun.delete(threadId);\r\n\r\n    this.logger.debug(`Cleaned up thread ${threadId}${runId ? ` (run ${runId})` : ''}`);\r\n  }\r\n\r\n  /**\r\n   * Get active threads count for monitoring\r\n   * @returns Number of active threads\r\n   */\r\n  getActiveThreadsCount(): number {\r\n    return this.activeThreads.size;\r\n  }\r\n\r\n  /**\r\n   * Get list of active thread IDs for debugging\r\n   * @returns Array of active thread IDs\r\n   */\r\n  getActiveThreadIds(): string[] {\r\n    return Array.from(this.activeThreads.keys());\r\n  }\r\n\r\n  /**\r\n   * Check if a specific thread is active\r\n   * @param threadId Thread ID to check\r\n   * @returns True if thread is active\r\n   */\r\n  isThreadActive(threadId: string): boolean {\r\n    return this.activeThreads.has(threadId);\r\n  }\r\n\r\n  /**\r\n   * Get thread-to-run mapping for debugging\r\n   * @returns Map of threadId to runId\r\n   */\r\n  getThreadToRunMapping(): Record<string, string> {\r\n    return Object.fromEntries(this.threadToRun.entries());\r\n  }\r\n\r\n  /**\r\n   * Extract role from LangGraph event tags\r\n   * @param tags Array of tags from LangGraph event\r\n   * @param roleTags Array of role tags to match against\r\n   * @returns Role string or null if not found\r\n   */\r\n  private getRoleFromTags(tags: string[], roleTags: string[]): string | null {\r\n    for (const tag of tags) {\r\n      if (roleTags.includes(tag)) {\r\n        return tag === SUPERVISOR_TAG || tag === SUPERVISOR_TOOL_CALL_TAG ? 'supervisor' : 'worker';\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * Save complete assistant response when streaming finishes successfully\r\n   * @param threadId Thread ID for the response\r\n   * @param completeText Complete accumulated text\r\n   * @param userId User ID who created the run\r\n   */\r\n  private async saveCompleteResponse(threadId: string, completeText: string, userId: number): Promise<void> {\r\n    try {\r\n      this.logger.log(`💾 Saving complete assistant response for thread ${threadId}`, {\r\n        threadId,\r\n        textLength: completeText.length,\r\n        userId,\r\n        preview: completeText.substring(0, 100) + (completeText.length > 100 ? '...' : ''),\r\n      });\r\n\r\n      await this.userMessagesQueries.createMessage({\r\n        thread_id: threadId,\r\n        role: 'assistant',\r\n        content: {\r\n          contentBlocks: [\r\n            {\r\n              type: 'text',\r\n              content: completeText,\r\n            }\r\n          ],\r\n          complete: true,\r\n          tokenCount: completeText.length,\r\n          timestamp: Date.now(),\r\n        },\r\n        created_by: userId,\r\n      });\r\n\r\n      this.logger.log(`✅ Successfully saved complete assistant response for thread ${threadId}`);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to save complete response for thread ${threadId}:`, error);\r\n      // Don't throw - message saving should not fail the streaming completion\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Save partial response when streaming is cancelled\r\n   * @param threadId Thread ID for the partial response\r\n   * @param partialText Accumulated partial text\r\n   */\r\n  private async savePartialResponse(threadId: string, partialText: string): Promise<void> {\r\n    try {\r\n      // Get userId from the run data\r\n      const runId = this.threadToRun.get(threadId);\r\n      if (!runId) {\r\n        this.logger.warn(`No runId found for thread ${threadId}, cannot save partial response`);\r\n        return;\r\n      }\r\n\r\n      const runData = await this.userAgentRunsQueries.getRunById(runId);\r\n      if (!runData) {\r\n        this.logger.warn(`No run data found for runId ${runId}, cannot save partial response`);\r\n        return;\r\n      }\r\n\r\n      const userId = runData.created_by;\r\n\r\n      this.logger.log(`💾 Saving partial assistant response for thread ${threadId}`, {\r\n        threadId,\r\n        partialLength: partialText.length,\r\n        userId,\r\n        preview: partialText.substring(0, 100) + (partialText.length > 100 ? '...' : ''),\r\n      });\r\n\r\n      await this.userMessagesQueries.createMessage({\r\n        thread_id: threadId,\r\n        role: 'assistant',\r\n        content: {\r\n          contentBlocks: [\r\n            {\r\n              type: 'text',\r\n              content: partialText,\r\n            }\r\n          ],\r\n          partial: true,\r\n          cancelled: true,\r\n          tokenCount: partialText.length,\r\n          timestamp: Date.now(),\r\n        },\r\n        created_by: userId,\r\n      });\r\n\r\n      this.logger.log(`✅ Successfully saved partial assistant response for thread ${threadId}`);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to save partial response for thread ${threadId}:`, error);\r\n      // Don't throw - partial response saving should not fail the cancellation\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Decrypt API keys in the payload based on user type\r\n   * @param payload The payload containing encrypted API keys\r\n   * @param userId User ID for decryption (from created_by field)\r\n   * @returns Payload with decrypted API keys\r\n   */\r\n  private decryptApiKeysInPayload(payload: any, userId: number): any {\r\n    try {\r\n      if (!payload) {\r\n        return payload;\r\n      }\r\n\r\n      // Deep clone payload to avoid modifying original\r\n      const decryptedPayload = JSON.parse(JSON.stringify(payload));\r\n\r\n      // Decrypt API keys in agentConfigMap\r\n      if (decryptedPayload.agentConfigMap) {\r\n        for (const agentId in decryptedPayload.agentConfigMap) {\r\n          const agentConfig = decryptedPayload.agentConfigMap[agentId];\r\n\r\n          if (agentConfig?.model?.apiKeys && Array.isArray(agentConfig.model.apiKeys)) {\r\n            this.logger.debug(`Decrypting ${agentConfig.model.apiKeys.length} API keys for agent ${agentId}`);\r\n\r\n            agentConfig.model.apiKeys = agentConfig.model.apiKeys.map((encryptedKey: string) => {\r\n              try {\r\n                // Determine if this is admin or user key based on agent type\r\n                if (agentConfig.model.type === 'SYSTEM') {\r\n                  // System agents use admin keys\r\n                  return this.apiKeyEncryptionHelper.decryptAdminApiKey(encryptedKey);\r\n                } else {\r\n                  // User agents use user-specific keys\r\n                  return this.apiKeyEncryptionHelper.decryptUserApiKey(encryptedKey, userId);\r\n                }\r\n              } catch (error) {\r\n                this.logger.error(`Failed to decrypt API key for agent ${agentId}:`, error);\r\n                // Return original key if decryption fails (might already be decrypted)\r\n                return encryptedKey;\r\n              }\r\n            });\r\n          }\r\n        }\r\n      }\r\n\r\n      this.logger.debug(`Successfully decrypted API keys in payload for user ${userId}`);\r\n      return decryptedPayload;\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to decrypt API keys in payload for user ${userId}:`, error);\r\n      // Return original payload if decryption fails\r\n      return payload;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Map payload data to CustomConfigurableType for LangGraph configuration\r\n   * @param runData Run data from database containing payload\r\n   * @param threadId Thread ID for the configuration\r\n   * @returns CustomConfigurableType configuration object\r\n   */\r\n  private buildCustomConfigurable(runData: any, threadId: string): CustomConfigurableType {\r\n    try {\r\n      // Decrypt API keys in payload before processing\r\n      const userId = runData.created_by; // Get user ID from database record\r\n      const decryptedPayload = this.decryptApiKeysInPayload(runData.payload, userId);\r\n\r\n      // Extract configuration values from decrypted payload with fallbacks\r\n      const alwaysApproveToolCall = decryptedPayload?.processing?.alwaysApproveToolCall || false;\r\n      const agentConfigMap = decryptedPayload?.agentConfigMap || {};\r\n      const supervisorAgentId = decryptedPayload?.primaryAgentId || '';\r\n\r\n      const config: CustomConfigurableType = {\r\n        alwaysApproveToolCall,\r\n        thread_id: threadId,\r\n        agentConfigMap,\r\n        supervisorAgentId,\r\n        multiMcpClients: undefined, // Skip for now as requested\r\n      };\r\n\r\n      this.logger.debug(`Built CustomConfigurableType for thread ${threadId}:`, {\r\n        alwaysApproveToolCall,\r\n        thread_id: threadId,\r\n        supervisorAgentId,\r\n        agentConfigCount: Object.keys(agentConfigMap).length,\r\n        hasMultiMcp: false,\r\n        userId: userId,\r\n        apiKeysDecrypted: true,\r\n      });\r\n\r\n      return config;\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to build CustomConfigurableType for thread ${threadId}:`, error);\r\n\r\n      // Return minimal fallback configuration\r\n      return {\r\n        alwaysApproveToolCall: false,\r\n        thread_id: threadId,\r\n        agentConfigMap: {},\r\n        supervisorAgentId: '',\r\n        multiMcpClients: undefined,\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Emit streaming event to Redis Streams for backend SSE consumption\r\n   * @param producer Redis client for publishing\r\n   * @param event Streaming event data\r\n   */\r\n  async emitStream(producer: any, event: {\r\n    threadId: string;\r\n    event: string;\r\n    data: any;\r\n  }): Promise<void> {\r\n    try {\r\n      const streamKey = `agent_stream:${event.threadId}`;\r\n      const streamData = {\r\n        event: event.event,\r\n        data: JSON.stringify(event.data),\r\n        timestamp: Date.now().toString(),\r\n      };\r\n\r\n      // Publish to Redis Streams using xadd\r\n      await producer.xadd(streamKey, '*', ...Object.entries(streamData).flat());\r\n\r\n      // Also publish notification for real-time updates (following working pattern)\r\n      await producer.publish(streamKey, JSON.stringify({\r\n        event: event.event,\r\n        timestamp: Date.now(),\r\n      }));\r\n\r\n      this.logger.debug(`Emitted stream event ${event.event} for thread ${event.threadId}`);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to emit stream event ${event.event} for thread ${event.threadId}:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n}\r\n"], "names": ["RedisEventController", "ROLE_TAGS", "SUPERVISOR_TAG", "WORKER_TAG", "TOOL_CALL_TAGS", "SUPERVISOR_TOOL_CALL_TAG", "WORKER_TOOL_CALL_TAG", "handleRunTrigger", "data", "logger", "log", "threadId", "runId", "validateRunTriggerEvent", "error", "activeThreads", "has", "warn", "abortController", "AbortController", "set", "threadToRun", "runData", "userAgentRunsQueries", "getRunById", "cleanupThread", "agentId", "processAgentThread", "jwt", "message", "stack", "name", "handleRunCancel", "reason", "validateRunCancelEvent", "get", "abort", "producer", "redisService", "getRawClient", "emitStream", "event", "timestamp", "Date", "now", "userId", "eventType", "REDIS_EVENTS", "RUN_TRIGGER", "RUN_CANCEL", "debug", "id", "status", "payloadSize", "JSON", "stringify", "payload", "length", "aborted", "signal", "hasJwt", "jwtLength", "customConfig", "buildCustomConfigurable", "created_by", "decryptedPayload", "decryptApiKeysInPayload", "alwaysApproveToolCall", "thread_id", "supervisorAgentId", "agentConfigCount", "Object", "keys", "agentConfigMap", "partialTokens", "messageContent", "content", "trim", "hasPayload", "hasMessage", "payloadStructure", "messageKeys", "messageType", "contentType", "rawPayloadSample", "substring", "Error", "humanMessage", "HumanMessage", "input", "messages", "activeAgent", "messageLength", "messagePreview", "humanMessageType", "constructor", "hasValidMessage", "streaming", "workflow", "streamEvents", "configurable", "subgraphs", "recursionLimit", "version", "streamingIterator", "Symbol", "asyncIterator", "abortListener", "return", "addEventListener", "tags", "some", "t", "includes", "role", "getRoleFromTags", "chunk", "text", "push", "displayText", "safeText", "replace", "textLength", "fullText", "undefined", "toolInfo", "toolName", "value", "interruptValue", "parse", "displayValue", "interruptType", "has<PERSON>rompt", "prompt", "completeResponse", "join", "responsePreview", "safePreview", "totalTokens", "totalChars", "saveCompleteResponse", "isAbortError", "TypeError", "errorName", "emitError", "partialText", "tokenCount", "preview", "savePartialResponse", "delete", "getActiveThreadsCount", "size", "getActiveThreadIds", "Array", "from", "isThreadActive", "getThreadToRunMapping", "fromEntries", "entries", "roleTags", "tag", "completeText", "userMessagesQueries", "createMessage", "contentBlocks", "type", "complete", "partialLength", "partial", "cancelled", "agentConfig", "model", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isArray", "map", "encrypted<PERSON>ey", "apiKeyEncryptionHelper", "decryptAdminApiKey", "decryptUserApiKey", "processing", "primaryAgentId", "config", "multiMcpClients", "hasMultiMcp", "apiKeysDecrypted", "streamKey", "streamData", "toString", "xadd", "flat", "publish", "<PERSON><PERSON>", "Map"], "mappings": ";;;;+BAoCaA;;;eAAAA;;;wBApCsB;+BACG;0BACoB;2BACI;uBACjC;0BAEa;4BAEjB;4BAMlB;wCACgC;;;;;;;;;;;;;;;AAEvC,qCAAqC;AACrC,MAAMC,YAAY;IAACC,0BAAc;IAAEC,sBAAU;CAAC;AAC9C,MAAMC,iBAAiB;IAACC,oCAAwB;IAAEC,gCAAoB;CAAC;AAiBhE,IAAA,AAAMN,uBAAN,MAAMA;IAcX;;;GAGC,GACD,MACMO,iBAAiB,AAAWC,IAAqB,EAAiB;QACtE,IAAI;YACF,IAAI,CAACC,MAAM,CAACC,GAAG,CAAC,CAAC,sCAAsC,EAAEF,KAAKG,QAAQ,CAAC,MAAM,EAAEH,KAAKI,KAAK,EAAE;YAE3F,yBAAyB;YACzB,IAAI,CAAC,IAAI,CAACC,uBAAuB,CAACL,OAAO;gBACvC,IAAI,CAACC,MAAM,CAACK,KAAK,CAAC,CAAC,6CAA6C,EAAEN,MAAM,CAAC,WAAW,EAAE;gBACtF;YACF;YAEA,6CAA6C;YAC7C,IAAI,IAAI,CAACO,aAAa,CAACC,GAAG,CAACR,KAAKG,QAAQ,GAAG;gBACzC,IAAI,CAACF,MAAM,CAACQ,IAAI,CAAC,CAAC,OAAO,EAAET,KAAKG,QAAQ,CAAC,uDAAuD,CAAC;gBACjG;YACF;YAEA,6DAA6D;YAC7D,MAAMO,kBAAkB,IAAIC;YAC5B,IAAI,CAACJ,aAAa,CAACK,GAAG,CAACZ,KAAKG,QAAQ,EAAEO;YACtC,IAAI,CAACG,WAAW,CAACD,GAAG,CAACZ,KAAKG,QAAQ,EAAEH,KAAKI,KAAK;YAE9C,+BAA+B;YAC/B,MAAMU,UAAU,MAAM,IAAI,CAACC,oBAAoB,CAACC,UAAU,CAAChB,KAAKI,KAAK;YAErE,IAAI,CAACU,SAAS;gBACZ,IAAI,CAACb,MAAM,CAACK,KAAK,CAAC,CAAC,IAAI,EAAEN,KAAKI,KAAK,CAAC,kCAAkC,EAAEJ,KAAKG,QAAQ,EAAE;gBACvF,IAAI,CAACc,aAAa,CAACjB,KAAKG,QAAQ;gBAChC;YACF;YAEA,IAAI,CAACF,MAAM,CAACC,GAAG,CAAC,CAAC,kBAAkB,EAAEF,KAAKG,QAAQ,CAAC,WAAW,EAAEH,KAAKkB,OAAO,CAAC,MAAM,EAAElB,KAAKI,KAAK,CAAC,UAAU,CAAC;YAE3G,mEAAmE;YACnE,sCAAsC;YACtC,MAAM,IAAI,CAACe,kBAAkB,CAACnB,KAAKG,QAAQ,EAAEW,SAASJ,iBAAiBV,KAAKoB,GAAG;QAEjF,EAAE,OAAOd,OAAO;YACd,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,CAAC,sCAAsC,EAAEN,KAAKG,QAAQ,CAAC,CAAC,CAAC,EAAE;gBAC3EkB,SAASf,MAAMe,OAAO;gBACtBC,OAAOhB,MAAMgB,KAAK;gBAClBC,MAAMjB,MAAMiB,IAAI;gBAChBpB,UAAUH,KAAKG,QAAQ;gBACvBC,OAAOJ,KAAKI,KAAK;gBACjBE,OAAOA;YACT;YACA,IAAI,CAACW,aAAa,CAACjB,KAAKG,QAAQ;QAClC;IACF;IAEA;;;GAGC,GACD,MACMqB,gBAAgB,AAAWxB,IAAoB,EAAiB;QACpE,IAAI;YACF,IAAI,CAACC,MAAM,CAACC,GAAG,CAAC,CAAC,qCAAqC,EAAEF,KAAKG,QAAQ,CAAC,EAAE,EAAEH,KAAKyB,MAAM,EAAE;YAEvF,yBAAyB;YACzB,IAAI,CAAC,IAAI,CAACC,sBAAsB,CAAC1B,OAAO;gBACtC,IAAI,CAACC,MAAM,CAACK,KAAK,CAAC,CAAC,4CAA4C,EAAEN,MAAM,CAAC,WAAW,EAAE;gBACrF;YACF;YAEA,8DAA8D;YAC9D,MAAMU,kBAAkB,IAAI,CAACH,aAAa,CAACoB,GAAG,CAAC3B,KAAKG,QAAQ;YAC5D,IAAI,CAACO,iBAAiB;gBACpB,IAAI,CAACT,MAAM,CAACC,GAAG,CAAC,CAAC,OAAO,EAAEF,KAAKG,QAAQ,CAAC,wDAAwD,CAAC;gBACjG;YACF;YAEA,2DAA2D;YAC3DO,gBAAgBkB,KAAK;YACrB,IAAI,CAAC3B,MAAM,CAACC,GAAG,CAAC,CAAC,iBAAiB,EAAEF,KAAKG,QAAQ,CAAC,EAAE,EAAEH,KAAKyB,MAAM,EAAE;YAEnE,2CAA2C;YAC3C,IAAI;gBACF,MAAMI,WAAW,IAAI,CAACC,YAAY,CAACC,YAAY;gBAC/C,MAAM,IAAI,CAACC,UAAU,CAACH,UAAU;oBAC9B1B,UAAUH,KAAKG,QAAQ;oBACvB8B,OAAO;oBACPjC,MAAM;wBAAEyB,QAAQzB,KAAKyB,MAAM;wBAAES,WAAWC,KAAKC,GAAG;oBAAG;gBACrD;YACF,EAAE,OAAO9B,OAAO;gBACd,IAAI,CAACL,MAAM,CAACQ,IAAI,CAAC,CAAC,6CAA6C,EAAET,KAAKG,QAAQ,CAAC,CAAC,CAAC,EAAEG;YACrF;YAEA,2BAA2B;YAC3B,IAAI,CAACW,aAAa,CAACjB,KAAKG,QAAQ;QAElC,EAAE,OAAOG,OAAO;YACd,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,CAAC,qCAAqC,EAAEN,KAAKG,QAAQ,CAAC,CAAC,CAAC,EAAEG;QAC9E;IACF;IAEA;;;;GAIC,GACD,AAAQD,wBAAwBL,IAAS,EAA2B;QAClE,OACEA,QACA,OAAOA,KAAKI,KAAK,KAAK,YACtB,OAAOJ,KAAKG,QAAQ,KAAK,YACzB,OAAOH,KAAKkB,OAAO,KAAK,YACxB,OAAOlB,KAAKqC,MAAM,KAAK,YACvB,OAAOrC,KAAKoB,GAAG,KAAK,YACpB,OAAOpB,KAAKkC,SAAS,KAAK,YAC1BlC,KAAKsC,SAAS,KAAKC,uBAAY,CAACC,WAAW;IAE/C;IAEA;;;;GAIC,GACD,AAAQd,uBAAuB1B,IAAS,EAA0B;QAChE,OACEA,QACA,OAAOA,KAAKG,QAAQ,KAAK,YACzB,OAAOH,KAAKyB,MAAM,KAAK,YACvB,OAAOzB,KAAKkC,SAAS,KAAK,YAC1BlC,KAAKsC,SAAS,KAAKC,uBAAY,CAACE,UAAU;IAE9C;IAEA;;;;;;GAMC,GACD,MAActB,mBACZhB,QAAgB,EAChBW,OAAY,EACZJ,eAAgC,EAChCU,GAAW,EACI;QACf,IAAI,CAACnB,MAAM,CAACyC,KAAK,CAAC,CAAC,4BAA4B,EAAEvC,SAAS,UAAU,EAAEW,QAAQ6B,EAAE,CAAC,CAAC,CAAC,EAAE;YACnFxC;YACAC,OAAOU,QAAQ6B,EAAE;YACjBC,QAAQ9B,QAAQ8B,MAAM;YACtBC,aAAaC,KAAKC,SAAS,CAACjC,QAAQkC,OAAO,EAAEC,MAAM;YACnDC,SAASxC,gBAAgByC,MAAM,CAACD,OAAO;YACvCE,QAAQ,CAAC,CAAChC;YACViC,WAAWjC,IAAI6B,MAAM;QACvB;QAEA,wCAAwC;QACxC,MAAMpB,WAAW,IAAI,CAACC,YAAY,CAACC,YAAY;QAE/C,+EAA+E;QAC/E,MAAMuB,eAAe,IAAI,CAACC,uBAAuB,CAACzC,SAASX;QAE3D,+CAA+C;QAC/C,MAAMkC,SAASvB,QAAQ0C,UAAU;QACjC,MAAMC,mBAAmB,IAAI,CAACC,uBAAuB,CAAC5C,QAAQkC,OAAO,EAAEX;QAEvE,IAAI,CAACpC,MAAM,CAACC,GAAG,CAAC,CAAC,wCAAwC,EAAEC,SAAS,MAAM,EAAEW,QAAQ6B,EAAE,CAAC,CAAC,CAAC;QACzF,IAAI,CAAC1C,MAAM,CAACyC,KAAK,CAAC,CAAC,6BAA6B,CAAC,EAAE;YACjDiB,uBAAuBL,aAAaK,qBAAqB;YACzDC,WAAWN,aAAaM,SAAS;YACjCC,mBAAmBP,aAAaO,iBAAiB;YACjDC,kBAAkBC,OAAOC,IAAI,CAACV,aAAaW,cAAc,IAAI,CAAC,GAAGhB,MAAM;QACzE;QAEA,uCAAuC;QACvC,MAAMiB,gBAA0B,EAAE;QAElC,IAAI;YACF,2CAA2C;YAC3C,IAAIxD,gBAAgByC,MAAM,CAACD,OAAO,EAAE;gBAClC,IAAI,CAACjD,MAAM,CAACC,GAAG,CAAC,CAAC,OAAO,EAAEC,SAAS,wCAAwC,CAAC;gBAC5E;YACF;YAEA,8CAA8C;YAC9C,MAAMgE,iBAAiBV,kBAAkBpC,SAAS+C,WAAW;YAE7D,2BAA2B;YAC3B,IAAI,CAACD,kBAAkBA,eAAeE,IAAI,OAAO,IAAI;gBACnD,IAAI,CAACpE,MAAM,CAACK,KAAK,CAAC,CAAC,iCAAiC,EAAEH,UAAU,EAAE;oBAChEmE,YAAY,CAAC,CAACb;oBACdc,YAAY,CAAC,CAACd,kBAAkBpC;oBAChC8C,gBAAgBA;oBAChBK,kBAAkB;wBAChBR,MAAMP,mBAAmBM,OAAOC,IAAI,CAACP,oBAAoB,EAAE;wBAC3DgB,aAAahB,kBAAkBpC,UAAU0C,OAAOC,IAAI,CAACP,iBAAiBpC,OAAO,IAAI,EAAE;wBACnFqD,aAAa,OAAOjB,kBAAkBpC;wBACtCsD,aAAa,OAAOlB,kBAAkBpC,SAAS+C;oBACjD;oBACAQ,kBAAkB9B,KAAKC,SAAS,CAACU,kBAAkBoB,SAAS,CAAC,GAAG;gBAClE;gBACA,MAAM,IAAIC,MAAM;YAClB;YAEA,iDAAiD;YACjD,MAAMC,eAAe,IAAIC,sBAAY,CAACb;YACtC,MAAMc,QAAQ;gBACZC,UAAU;oBAACH;iBAAa;gBACxBI,aAAa7B,aAAaO,iBAAiB,IAAI;YACjD;YAEA,IAAI,CAAC5D,MAAM,CAACyC,KAAK,CAAC,CAAC,4CAA4C,CAAC,EAAE;gBAChEvC;gBACAgF,aAAaF,MAAME,WAAW;gBAC9BC,eAAejB,eAAelB,MAAM;gBACpCoC,gBAAgBlB,eAAeU,SAAS,CAAC,GAAG;gBAC5ClB,uBAAuBL,aAAaK,qBAAqB;gBACzD2B,kBAAkBP,aAAaQ,WAAW,CAAChE,IAAI;gBAC/CiE,iBAAiB,CAAC,CAACT,aAAaX,OAAO;YACzC;YAEA,wCAAwC;YACxC,MAAMqB,YAAYC,oBAAQ,CAACC,YAAY,CAACV,OAAO;gBAC7CW,cAActC;gBACduC,WAAW;gBACXC,gBAAgB;gBAChBC,SAAS;gBACT5C,QAAQzC,gBAAgByC,MAAM;YAChC;YACA,MAAM6C,oBAAoBP,SAAS,CAACQ,OAAOC,aAAa,CAAC;YAEzD,yDAAyD;YACzD,MAAMC,gBAAgB;gBACpB,IAAI,CAAClG,MAAM,CAACC,GAAG,CAAC,CAAC,OAAO,EAAEC,SAAS,yBAAyB,CAAC;gBAC7D,uCAAuC;gBACvC,MAAM6F,mBAAmBI;YAC3B;YACA1F,gBAAgByC,MAAM,CAACkD,gBAAgB,CAAC,SAASF;YAEjD,4DAA4D;YAC5D,WAAW,MAAM,EAAElE,KAAK,EAAEjC,IAAI,EAAEsG,OAAO,EAAE,EAAE,IAAIN,kBAAmB;gBAChE,IAAItF,gBAAgByC,MAAM,CAACD,OAAO,EAAE;oBAClC;gBACF;gBAEA,8BAA8B;gBAC9B,IACEjB,UAAU,0BACVqE,KAAKC,IAAI,CAAC,CAACC,IAAc/G,UAAUgH,QAAQ,CAACD,KAC5C;oBACA,MAAME,OAAO,IAAI,CAACC,eAAe,CAACL,MAAM7G;oBACxC,IAAIO,KAAK4G,KAAK,EAAExC,SAAS;wBACvB,MAAMyC,OAAO7G,KAAK4G,KAAK,CAACxC,OAAO;wBAC/BF,cAAc4C,IAAI,CAACD,OAAO,8CAA8C;wBAExE,gDAAgD;wBAChD,MAAME,cAAcF,KAAK5D,MAAM,GAAG,KAAK,GAAG4D,KAAKhC,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAGgC;wBACvE,MAAMG,WAAWD,YAAYE,OAAO,CAAC,OAAO,OAAOA,OAAO,CAAC,OAAO;wBAElE,IAAI,CAAChH,MAAM,CAACyC,KAAK,CAAC,CAAC,yBAAyB,EAAEgE,KAAK,IAAI,EAAEM,SAAS,GAAG,EAAEH,KAAK5D,MAAM,CAAC,OAAO,CAAC,EAAE;4BAC3F9C;4BACAuG;4BACAQ,YAAYL,KAAK5D,MAAM;4BACvBkE,UAAUN,KAAK5D,MAAM,IAAI,KAAK4D,OAAOO;wBACvC;wBAEA,MAAM,IAAI,CAACpF,UAAU,CAACH,UAAU;4BAC9B1B;4BACA8B,OAAO;4BACPjC,MAAM;gCAAE0G;gCAAMG;4BAAK;wBACrB;oBACF,OAAO;wBACL,IAAI,CAAC5G,MAAM,CAACyC,KAAK,CAAC,CAAC,yBAAyB,EAAEgE,KAAK,CAAC,CAAC,EAAE;4BAAEvG;4BAAUuG;wBAAK;wBACxE,MAAM,IAAI,CAAC1E,UAAU,CAACH,UAAU;4BAC9B1B;4BACA8B,OAAO;4BACPjC,MAAM;gCAAE0G;4BAAK;wBACf;oBACF;gBAEA,wBAAwB;gBAC1B,OAAO,IAAI;oBAAC;oBAAiB;iBAAc,CAACD,QAAQ,CAACxE,QAAQ;oBAC3D,MAAMyE,OAAO,IAAI,CAACC,eAAe,CAACL,MAAM1G;oBACxC,MAAM0C,YAAYL,UAAU,kBAAkB,oBAAoB;oBAElE,oDAAoD;oBACpD,IAAIoF,WAAW;oBACf,IAAIC;oBAEJ,IAAItH,QAAQ,OAAOA,SAAS,YAAY,UAAUA,MAAM;wBACtDsH,WAAW,AAACtH,KAAauB,IAAI;wBAC7B8F,WAAW,CAAC,EAAE,EAAEC,SAAS,CAAC,CAAC;oBAC7B;oBAEA,IAAI,CAACrH,MAAM,CAACyC,KAAK,CAAC,CAAC,QAAQ,EAAEJ,UAAU,EAAE,EAAEoE,KAAK,CAAC,EAAEW,UAAU,EAAE;wBAC7DlH;wBACAuG;wBACAY;wBACAhF;oBACF;oBAEA,MAAM,IAAI,CAACN,UAAU,CAACH,UAAU;wBAC9B1B;wBACA8B,OAAOK;wBACPtC,MAAM;4BAAE0G;4BAAMY;wBAAS;oBACzB;gBAEA,oCAAoC;gBACtC,OAAO,IACLrF,UAAU,qBACVjC,KAAK4G,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,gBAAgB,EAClC;oBACA,MAAM,CAAC,EAAEW,KAAK,EAAE,CAAC,GAAGvH,KAAK4G,KAAK,CAAC,EAAE,CAAC,gBAAgB;oBAClD,MAAMY,iBAAiB1E,KAAK2E,KAAK,CAACF;oBAElC,6BAA6B;oBAC7B,MAAMG,eAAe5E,KAAKC,SAAS,CAACyE,gBAAgBvE,MAAM,GAAG,MACzD,GAAGH,KAAKC,SAAS,CAACyE,gBAAgB3C,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GACxD/B,KAAKC,SAAS,CAACyE;oBAEnB,IAAI,CAACvH,MAAM,CAACyC,KAAK,CAAC,CAAC,wBAAwB,EAAEgF,cAAc,EAAE;wBAC3DvH;wBACAwH,eAAeH,eAAed,IAAI,IAAI;wBACtCkB,WAAW,CAAC,CAACJ,eAAeK,MAAM;oBACpC;oBAEA,MAAM,IAAI,CAAC7F,UAAU,CAACH,UAAU;wBAC9B1B;wBACA8B,OAAO;wBACPjC,MAAMwH;oBACR;gBACF;YACF;YAEA,oDAAoD;YACpD,IAAI,CAAC9G,gBAAgByC,MAAM,CAACD,OAAO,EAAE;gBACnC,gCAAgC;gBAChC,MAAM4E,mBAAmB5D,cAAc6D,IAAI,CAAC;gBAC5C,MAAMC,kBAAkBF,iBAAiB7E,MAAM,GAAG,MAC9C,GAAG6E,iBAAiBjD,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAC1CiD;gBACJ,MAAMG,cAAcD,gBAAgBf,OAAO,CAAC,OAAO,OAAOA,OAAO,CAAC,OAAO;gBAEzE,IAAI,CAAChH,MAAM,CAACC,GAAG,CAAC,CAAC,mBAAmB,EAAEC,SAAS,aAAa,EAAE8H,YAAY,CAAC,CAAC,EAAE;oBAC5E9H;oBACAC,OAAOU,QAAQ6B,EAAE;oBACjBuF,aAAahE,cAAcjB,MAAM;oBACjCkF,YAAYL,iBAAiB7E,MAAM;oBACnC+E,iBAAiBC;gBACnB;gBAEA,MAAM,IAAI,CAACjG,UAAU,CAACH,UAAU;oBAC9B1B;oBACA8B,OAAO;oBACPjC,MAAM;wBAAEG;wBAAUC,OAAOU,QAAQ6B,EAAE;oBAAC;gBACtC;gBAEA,+CAA+C;gBAC/C,IAAIuB,cAAcjB,MAAM,GAAG,GAAG;oBAC5B,MAAM,IAAI,CAACmF,oBAAoB,CAACjI,UAAU+D,cAAc6D,IAAI,CAAC,KAAK1F;gBACpE;gBAEA,IAAI,CAACpB,aAAa,CAACd;YACrB;QAEF,EAAE,OAAOG,OAAO;YACd,MAAM+H,eAAe3H,gBAAgByC,MAAM,CAACD,OAAO,IAChD5C,iBAAiBgI,aAAahI,MAAMe,OAAO,CAACoF,QAAQ,CAAC;YAExD,IAAI4B,cAAc;gBAChB,yCAAyC;gBACzC,IAAI,CAACpI,MAAM,CAACC,GAAG,CAAC;YAClB,OAAO;gBACL,6EAA6E;gBAC7E,IAAI,CAACD,MAAM,CAACK,KAAK,CAAC,CAAC,iCAAiC,EAAEH,SAAS,CAAC,CAAC,EAAE;oBACjEkB,SAASf,MAAMe,OAAO;oBACtBC,OAAOhB,MAAMgB,KAAK;oBAClBC,MAAMjB,MAAMiB,IAAI;oBAChBpB;oBACAC,OAAOU,QAAQ6B,EAAE;oBACjBrC,OAAOA;gBACT;gBAEA,qEAAqE;gBACrE,IAAI;oBACF,MAAM,IAAI,CAAC0B,UAAU,CAACH,UAAU;wBAC9B1B;wBACA8B,OAAO;wBACPjC,MAAM;4BACJM,OAAOA,MAAMe,OAAO,IAAI;4BACxBkH,WAAWjI,MAAMiB,IAAI;4BACrBD,OAAOhB,MAAMgB,KAAK;4BAClBnB;4BACAC,OAAOU,QAAQ6B,EAAE;4BACjBT,WAAWC,KAAKC,GAAG;wBACrB;oBACF;oBAEA,4DAA4D;oBAC5D,MAAM,IAAI,CAACJ,UAAU,CAACH,UAAU;wBAC9B1B;wBACA8B,OAAO;wBACPjC,MAAM;4BAAEG;4BAAUC,OAAOU,QAAQ6B,EAAE;4BAAErC,OAAO;wBAAK;oBACnD;gBACF,EAAE,OAAOkI,WAAW;oBAClB,IAAI,CAACvI,MAAM,CAACK,KAAK,CAAC,CAAC,sCAAsC,EAAEH,SAAS,CAAC,CAAC,EAAEqI;gBAC1E;gBAEA,IAAI,CAACvH,aAAa,CAACd;YACnB,+EAA+E;YACjF;QACF,SAAU;YACR,8CAA8C;YAC9C,IAAIO,gBAAgByC,MAAM,CAACD,OAAO,IAAIgB,cAAcjB,MAAM,GAAG,GAAG;gBAC9D,MAAMwF,cAAcvE,cAAc6D,IAAI,CAAC;gBACvC,MAAMhB,cAAc0B,YAAYxF,MAAM,GAAG,MAAM,GAAGwF,YAAY5D,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG4D;gBACvF,MAAMzB,WAAWD,YAAYE,OAAO,CAAC,OAAO,OAAOA,OAAO,CAAC,OAAO;gBAElE,IAAI,CAAChH,MAAM,CAACC,GAAG,CAAC,CAAC,+BAA+B,EAAEC,SAAS,GAAG,EAAE6G,SAAS,CAAC,CAAC,EAAE;oBAC3E0B,YAAYxE,cAAcjB,MAAM;oBAChCiE,YAAYuB,YAAYxF,MAAM;oBAC9B0F,SAAS3B;gBACX;gBACA,MAAM,IAAI,CAAC4B,mBAAmB,CAACzI,UAAUsI;YAC3C;QACF;IACF;IAEA;;;GAGC,GACD,AAAQxH,cAAcd,QAAgB,EAAQ;QAC5C,MAAMC,QAAQ,IAAI,CAACS,WAAW,CAACc,GAAG,CAACxB;QACnC,IAAI,CAACI,aAAa,CAACsI,MAAM,CAAC1I;QAC1B,IAAI,CAACU,WAAW,CAACgI,MAAM,CAAC1I;QAExB,IAAI,CAACF,MAAM,CAACyC,KAAK,CAAC,CAAC,kBAAkB,EAAEvC,WAAWC,QAAQ,CAAC,MAAM,EAAEA,MAAM,CAAC,CAAC,GAAG,IAAI;IACpF;IAEA;;;GAGC,GACD0I,wBAAgC;QAC9B,OAAO,IAAI,CAACvI,aAAa,CAACwI,IAAI;IAChC;IAEA;;;GAGC,GACDC,qBAA+B;QAC7B,OAAOC,MAAMC,IAAI,CAAC,IAAI,CAAC3I,aAAa,CAACyD,IAAI;IAC3C;IAEA;;;;GAIC,GACDmF,eAAehJ,QAAgB,EAAW;QACxC,OAAO,IAAI,CAACI,aAAa,CAACC,GAAG,CAACL;IAChC;IAEA;;;GAGC,GACDiJ,wBAAgD;QAC9C,OAAOrF,OAAOsF,WAAW,CAAC,IAAI,CAACxI,WAAW,CAACyI,OAAO;IACpD;IAEA;;;;;GAKC,GACD,AAAQ3C,gBAAgBL,IAAc,EAAEiD,QAAkB,EAAiB;QACzE,KAAK,MAAMC,OAAOlD,KAAM;YACtB,IAAIiD,SAAS9C,QAAQ,CAAC+C,MAAM;gBAC1B,OAAOA,QAAQ9J,0BAAc,IAAI8J,QAAQ3J,oCAAwB,GAAG,eAAe;YACrF;QACF;QACA,OAAO;IACT;IAEA;;;;;GAKC,GACD,MAAcuI,qBAAqBjI,QAAgB,EAAEsJ,YAAoB,EAAEpH,MAAc,EAAiB;QACxG,IAAI;YACF,IAAI,CAACpC,MAAM,CAACC,GAAG,CAAC,CAAC,iDAAiD,EAAEC,UAAU,EAAE;gBAC9EA;gBACA+G,YAAYuC,aAAaxG,MAAM;gBAC/BZ;gBACAsG,SAASc,aAAa5E,SAAS,CAAC,GAAG,OAAQ4E,CAAAA,aAAaxG,MAAM,GAAG,MAAM,QAAQ,EAAC;YAClF;YAEA,MAAM,IAAI,CAACyG,mBAAmB,CAACC,aAAa,CAAC;gBAC3C/F,WAAWzD;gBACXuG,MAAM;gBACNtC,SAAS;oBACPwF,eAAe;wBACb;4BACEC,MAAM;4BACNzF,SAASqF;wBACX;qBACD;oBACDK,UAAU;oBACVpB,YAAYe,aAAaxG,MAAM;oBAC/Bf,WAAWC,KAAKC,GAAG;gBACrB;gBACAoB,YAAYnB;YACd;YAEA,IAAI,CAACpC,MAAM,CAACC,GAAG,CAAC,CAAC,4DAA4D,EAAEC,UAAU;QAE3F,EAAE,OAAOG,OAAO;YACd,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,CAAC,4CAA4C,EAAEH,SAAS,CAAC,CAAC,EAAEG;QAC9E,wEAAwE;QAC1E;IACF;IAEA;;;;GAIC,GACD,MAAcsI,oBAAoBzI,QAAgB,EAAEsI,WAAmB,EAAiB;QACtF,IAAI;YACF,+BAA+B;YAC/B,MAAMrI,QAAQ,IAAI,CAACS,WAAW,CAACc,GAAG,CAACxB;YACnC,IAAI,CAACC,OAAO;gBACV,IAAI,CAACH,MAAM,CAACQ,IAAI,CAAC,CAAC,0BAA0B,EAAEN,SAAS,8BAA8B,CAAC;gBACtF;YACF;YAEA,MAAMW,UAAU,MAAM,IAAI,CAACC,oBAAoB,CAACC,UAAU,CAACZ;YAC3D,IAAI,CAACU,SAAS;gBACZ,IAAI,CAACb,MAAM,CAACQ,IAAI,CAAC,CAAC,4BAA4B,EAAEL,MAAM,8BAA8B,CAAC;gBACrF;YACF;YAEA,MAAMiC,SAASvB,QAAQ0C,UAAU;YAEjC,IAAI,CAACvD,MAAM,CAACC,GAAG,CAAC,CAAC,gDAAgD,EAAEC,UAAU,EAAE;gBAC7EA;gBACA4J,eAAetB,YAAYxF,MAAM;gBACjCZ;gBACAsG,SAASF,YAAY5D,SAAS,CAAC,GAAG,OAAQ4D,CAAAA,YAAYxF,MAAM,GAAG,MAAM,QAAQ,EAAC;YAChF;YAEA,MAAM,IAAI,CAACyG,mBAAmB,CAACC,aAAa,CAAC;gBAC3C/F,WAAWzD;gBACXuG,MAAM;gBACNtC,SAAS;oBACPwF,eAAe;wBACb;4BACEC,MAAM;4BACNzF,SAASqE;wBACX;qBACD;oBACDuB,SAAS;oBACTC,WAAW;oBACXvB,YAAYD,YAAYxF,MAAM;oBAC9Bf,WAAWC,KAAKC,GAAG;gBACrB;gBACAoB,YAAYnB;YACd;YAEA,IAAI,CAACpC,MAAM,CAACC,GAAG,CAAC,CAAC,2DAA2D,EAAEC,UAAU;QAE1F,EAAE,OAAOG,OAAO;YACd,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,CAAC,2CAA2C,EAAEH,SAAS,CAAC,CAAC,EAAEG;QAC7E,yEAAyE;QAC3E;IACF;IAEA;;;;;GAKC,GACD,AAAQoD,wBAAwBV,OAAY,EAAEX,MAAc,EAAO;QACjE,IAAI;YACF,IAAI,CAACW,SAAS;gBACZ,OAAOA;YACT;YAEA,iDAAiD;YACjD,MAAMS,mBAAmBX,KAAK2E,KAAK,CAAC3E,KAAKC,SAAS,CAACC;YAEnD,qCAAqC;YACrC,IAAIS,iBAAiBQ,cAAc,EAAE;gBACnC,IAAK,MAAM/C,WAAWuC,iBAAiBQ,cAAc,CAAE;oBACrD,MAAMiG,cAAczG,iBAAiBQ,cAAc,CAAC/C,QAAQ;oBAE5D,IAAIgJ,aAAaC,OAAOC,WAAWnB,MAAMoB,OAAO,CAACH,YAAYC,KAAK,CAACC,OAAO,GAAG;wBAC3E,IAAI,CAACnK,MAAM,CAACyC,KAAK,CAAC,CAAC,WAAW,EAAEwH,YAAYC,KAAK,CAACC,OAAO,CAACnH,MAAM,CAAC,oBAAoB,EAAE/B,SAAS;wBAEhGgJ,YAAYC,KAAK,CAACC,OAAO,GAAGF,YAAYC,KAAK,CAACC,OAAO,CAACE,GAAG,CAAC,CAACC;4BACzD,IAAI;gCACF,6DAA6D;gCAC7D,IAAIL,YAAYC,KAAK,CAACN,IAAI,KAAK,UAAU;oCACvC,+BAA+B;oCAC/B,OAAO,IAAI,CAACW,sBAAsB,CAACC,kBAAkB,CAACF;gCACxD,OAAO;oCACL,qCAAqC;oCACrC,OAAO,IAAI,CAACC,sBAAsB,CAACE,iBAAiB,CAACH,cAAclI;gCACrE;4BACF,EAAE,OAAO/B,OAAO;gCACd,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,CAAC,oCAAoC,EAAEY,QAAQ,CAAC,CAAC,EAAEZ;gCACrE,uEAAuE;gCACvE,OAAOiK;4BACT;wBACF;oBACF;gBACF;YACF;YAEA,IAAI,CAACtK,MAAM,CAACyC,KAAK,CAAC,CAAC,oDAAoD,EAAEL,QAAQ;YACjF,OAAOoB;QAET,EAAE,OAAOnD,OAAO;YACd,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,CAAC,+CAA+C,EAAE+B,OAAO,CAAC,CAAC,EAAE/B;YAC/E,8CAA8C;YAC9C,OAAO0C;QACT;IACF;IAEA;;;;;GAKC,GACD,AAAQO,wBAAwBzC,OAAY,EAAEX,QAAgB,EAA0B;QACtF,IAAI;YACF,gDAAgD;YAChD,MAAMkC,SAASvB,QAAQ0C,UAAU,EAAE,mCAAmC;YACtE,MAAMC,mBAAmB,IAAI,CAACC,uBAAuB,CAAC5C,QAAQkC,OAAO,EAAEX;YAEvE,qEAAqE;YACrE,MAAMsB,wBAAwBF,kBAAkBkH,YAAYhH,yBAAyB;YACrF,MAAMM,iBAAiBR,kBAAkBQ,kBAAkB,CAAC;YAC5D,MAAMJ,oBAAoBJ,kBAAkBmH,kBAAkB;YAE9D,MAAMC,SAAiC;gBACrClH;gBACAC,WAAWzD;gBACX8D;gBACAJ;gBACAiH,iBAAiB1D;YACnB;YAEA,IAAI,CAACnH,MAAM,CAACyC,KAAK,CAAC,CAAC,wCAAwC,EAAEvC,SAAS,CAAC,CAAC,EAAE;gBACxEwD;gBACAC,WAAWzD;gBACX0D;gBACAC,kBAAkBC,OAAOC,IAAI,CAACC,gBAAgBhB,MAAM;gBACpD8H,aAAa;gBACb1I,QAAQA;gBACR2I,kBAAkB;YACpB;YAEA,OAAOH;QAET,EAAE,OAAOvK,OAAO;YACd,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,CAAC,kDAAkD,EAAEH,SAAS,CAAC,CAAC,EAAEG;YAEpF,wCAAwC;YACxC,OAAO;gBACLqD,uBAAuB;gBACvBC,WAAWzD;gBACX8D,gBAAgB,CAAC;gBACjBJ,mBAAmB;gBACnBiH,iBAAiB1D;YACnB;QACF;IACF;IAEA;;;;GAIC,GACD,MAAMpF,WAAWH,QAAa,EAAEI,KAI/B,EAAiB;QAChB,IAAI;YACF,MAAMgJ,YAAY,CAAC,aAAa,EAAEhJ,MAAM9B,QAAQ,EAAE;YAClD,MAAM+K,aAAa;gBACjBjJ,OAAOA,MAAMA,KAAK;gBAClBjC,MAAM8C,KAAKC,SAAS,CAACd,MAAMjC,IAAI;gBAC/BkC,WAAWC,KAAKC,GAAG,GAAG+I,QAAQ;YAChC;YAEA,sCAAsC;YACtC,MAAMtJ,SAASuJ,IAAI,CAACH,WAAW,QAAQlH,OAAOuF,OAAO,CAAC4B,YAAYG,IAAI;YAEtE,8EAA8E;YAC9E,MAAMxJ,SAASyJ,OAAO,CAACL,WAAWnI,KAAKC,SAAS,CAAC;gBAC/Cd,OAAOA,MAAMA,KAAK;gBAClBC,WAAWC,KAAKC,GAAG;YACrB;YAEA,IAAI,CAACnC,MAAM,CAACyC,KAAK,CAAC,CAAC,qBAAqB,EAAET,MAAMA,KAAK,CAAC,YAAY,EAAEA,MAAM9B,QAAQ,EAAE;QAEtF,EAAE,OAAOG,OAAO;YACd,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,CAAC,4BAA4B,EAAE2B,MAAMA,KAAK,CAAC,YAAY,EAAEA,MAAM9B,QAAQ,CAAC,CAAC,CAAC,EAAEG;YAC9F,MAAMA;QACR;IACF;IAvtBAiF,YACE,AAAiBxE,oBAA0C,EAC3D,AAAiB2I,mBAAwC,EACzD,AAAiB5H,YAA0B,EAC3C,AAAiB0I,sBAA8C,CAC/D;aAJiBzJ,uBAAAA;aACA2I,sBAAAA;aACA5H,eAAAA;aACA0I,yBAAAA;aAVFvK,SAAS,IAAIsL,cAAM,CAAC/L,qBAAqB+B,IAAI;aAG7ChB,gBAAgB,IAAIiL;aACpB3K,cAAc,IAAI2K;IAOhC;AAotBL;;6DA9sB6BhJ;;;;;;;;;6DAsDAC"}