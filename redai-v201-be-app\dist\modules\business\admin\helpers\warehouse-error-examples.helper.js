"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WAREHOUSE_ERROR_EXAMPLES = void 0;
const warehouse_exception_1 = require("../exceptions/warehouse.exception");
exports.WAREHOUSE_ERROR_EXAMPLES = {
    WAREHOUSE_NOT_FOUND: {
        code: warehouse_exception_1.ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_NOT_FOUND.code,
        message: warehouse_exception_1.ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_NOT_FOUND.message,
        detail: {
            warehouseId: 999
        }
    },
    WAREHOUSE_FIND_FAILED: {
        code: warehouse_exception_1.ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_FIND_FAILED.code,
        message: warehouse_exception_1.ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_FIND_FAILED.message,
        detail: {
            error: 'Lỗi kết nối cơ sở dữ liệu'
        }
    },
    PHYSICAL_WAREHOUSE_NOT_FOUND: {
        code: warehouse_exception_1.ADMIN_WAREHOUSE_ERROR_CODES.PHYSICAL_WAREHOUSE_NOT_FOUND.code,
        message: warehouse_exception_1.ADMIN_WAREHOUSE_ERROR_CODES.PHYSICAL_WAREHOUSE_NOT_FOUND.message,
        detail: {
            warehouseId: 999
        }
    },
    PHYSICAL_WAREHOUSE_FIND_FAILED: {
        code: warehouse_exception_1.ADMIN_WAREHOUSE_ERROR_CODES.PHYSICAL_WAREHOUSE_FIND_FAILED.code,
        message: warehouse_exception_1.ADMIN_WAREHOUSE_ERROR_CODES.PHYSICAL_WAREHOUSE_FIND_FAILED.message,
        detail: {
            error: 'Lỗi kết nối cơ sở dữ liệu'
        }
    },
    VIRTUAL_WAREHOUSE_NOT_FOUND: {
        code: warehouse_exception_1.ADMIN_WAREHOUSE_ERROR_CODES.VIRTUAL_WAREHOUSE_NOT_FOUND.code,
        message: warehouse_exception_1.ADMIN_WAREHOUSE_ERROR_CODES.VIRTUAL_WAREHOUSE_NOT_FOUND.message,
        detail: {
            warehouseId: 999
        }
    },
    VIRTUAL_WAREHOUSE_FIND_FAILED: {
        code: warehouse_exception_1.ADMIN_WAREHOUSE_ERROR_CODES.VIRTUAL_WAREHOUSE_FIND_FAILED.code,
        message: warehouse_exception_1.ADMIN_WAREHOUSE_ERROR_CODES.VIRTUAL_WAREHOUSE_FIND_FAILED.message,
        detail: {
            error: 'Lỗi kết nối cơ sở dữ liệu'
        }
    },
    WAREHOUSE_CUSTOM_FIELD_NOT_FOUND: {
        code: warehouse_exception_1.ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_NOT_FOUND.code,
        message: warehouse_exception_1.ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_NOT_FOUND.message,
        detail: {
            warehouseId: 1,
            fieldId: 999
        }
    },
    WAREHOUSE_CUSTOM_FIELD_FIND_FAILED: {
        code: warehouse_exception_1.ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_FIND_FAILED.code,
        message: warehouse_exception_1.ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_FIND_FAILED.message,
        detail: {
            error: 'Lỗi kết nối cơ sở dữ liệu'
        }
    },
    INVALID_WAREHOUSE_TYPE: {
        code: warehouse_exception_1.ADMIN_WAREHOUSE_ERROR_CODES.INVALID_WAREHOUSE_TYPE.code,
        message: warehouse_exception_1.ADMIN_WAREHOUSE_ERROR_CODES.INVALID_WAREHOUSE_TYPE.message,
        detail: {
            type: 'UNKNOWN',
            validTypes: ['PHYSICAL', 'VIRTUAL']
        }
    }
};
//# sourceMappingURL=warehouse-error-examples.helper.js.map