"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryClassificationDto = exports.ClassificationSortField = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const query_dto_1 = require("../../../../common/dto/query.dto");
var ClassificationSortField;
(function (ClassificationSortField) {
    ClassificationSortField["ID"] = "id";
    ClassificationSortField["TYPE"] = "type";
})(ClassificationSortField || (exports.ClassificationSortField = ClassificationSortField = {}));
class QueryClassificationDto extends query_dto_1.QueryDto {
    productId;
    type;
    sortBy = ClassificationSortField.ID;
}
exports.QueryClassificationDto = QueryClassificationDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID sản phẩm để lọc',
        example: 123,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], QueryClassificationDto.prototype, "productId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Loại phân loại để lọc',
        example: 'Màu sắc',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryClassificationDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Trường sắp xếp',
        enum: ClassificationSortField,
        default: ClassificationSortField.ID,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(ClassificationSortField),
    __metadata("design:type", String)
], QueryClassificationDto.prototype, "sortBy", void 0);
//# sourceMappingURL=query-classification.dto.js.map