"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddUpdatedAtToRuleContract1720000000003 = void 0;
class AddUpdatedAtToRuleContract1720000000003 {
    async up(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE "rule_contract" 
      ADD COLUMN IF NOT EXISTS "updated_at" bigint
    `);
        await queryRunner.query(`
      COMMENT ON COLUMN "rule_contract"."updated_at" 
      IS 'Thời gian cập nhật hợp đồng (Unix timestamp)'
    `);
        await queryRunner.query(`
      UPDATE "rule_contract" 
      SET "updated_at" = COALESCE("created_at", EXTRACT(EPOCH FROM NOW()) * 1000)
      WHERE "updated_at" IS NULL
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE "rule_contract" 
      DROP COLUMN IF EXISTS "updated_at"
    `);
    }
}
exports.AddUpdatedAtToRuleContract1720000000003 = AddUpdatedAtToRuleContract1720000000003;
//# sourceMappingURL=1720000000003-AddUpdatedAtToRuleContract.js.map