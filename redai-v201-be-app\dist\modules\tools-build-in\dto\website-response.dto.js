"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebsiteSeoResponseDto = exports.WebsiteAnalysisResponseDto = exports.WebsiteInfoResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class WebsiteInfoResponseDto {
    url;
    title;
    description;
    favicon;
}
exports.WebsiteInfoResponseDto = WebsiteInfoResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'URL của website',
        example: 'https://example.com'
    }),
    __metadata("design:type", String)
], WebsiteInfoResponseDto.prototype, "url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tiêu đề của website',
        example: 'Example Website',
        nullable: true
    }),
    __metadata("design:type", String)
], WebsiteInfoResponseDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả của website',
        example: 'This is an example website',
        nullable: true
    }),
    __metadata("design:type", String)
], WebsiteInfoResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'URL favicon của website',
        example: 'https://example.com/favicon.ico',
        nullable: true
    }),
    __metadata("design:type", String)
], WebsiteInfoResponseDto.prototype, "favicon", void 0);
class WebsiteAnalysisResponseDto {
    info;
    metaTags;
    scripts;
    technologies;
}
exports.WebsiteAnalysisResponseDto = WebsiteAnalysisResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin cơ bản của website',
        type: WebsiteInfoResponseDto
    }),
    __metadata("design:type", WebsiteInfoResponseDto)
], WebsiteAnalysisResponseDto.prototype, "info", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Các meta tag của website',
        example: {
            'og:title': 'Example Website',
            'og:description': 'This is an example website'
        },
        nullable: true
    }),
    __metadata("design:type", Object)
], WebsiteAnalysisResponseDto.prototype, "metaTags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Các script được sử dụng trên website',
        example: ['jQuery', 'Bootstrap', 'Google Analytics'],
        nullable: true
    }),
    __metadata("design:type", Array)
], WebsiteAnalysisResponseDto.prototype, "scripts", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Các công nghệ được phát hiện trên website',
        example: ['React', 'Node.js', 'MongoDB'],
        nullable: true
    }),
    __metadata("design:type", Array)
], WebsiteAnalysisResponseDto.prototype, "technologies", void 0);
class WebsiteSeoResponseDto {
    title;
    description;
    keywords;
    score;
    issues;
}
exports.WebsiteSeoResponseDto = WebsiteSeoResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tiêu đề SEO',
        example: 'Example Website - Best Example on the Web'
    }),
    __metadata("design:type", String)
], WebsiteSeoResponseDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả SEO',
        example: 'This is the best example website on the web with amazing features'
    }),
    __metadata("design:type", String)
], WebsiteSeoResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Các từ khóa SEO',
        example: ['example', 'website', 'best']
    }),
    __metadata("design:type", Array)
], WebsiteSeoResponseDto.prototype, "keywords", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Điểm SEO tổng thể (0-100)',
        example: 85
    }),
    __metadata("design:type", Number)
], WebsiteSeoResponseDto.prototype, "score", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Các vấn đề SEO được phát hiện',
        example: ['Missing alt tags on images', 'Slow page load time'],
        nullable: true
    }),
    __metadata("design:type", Array)
], WebsiteSeoResponseDto.prototype, "issues", void 0);
//# sourceMappingURL=website-response.dto.js.map