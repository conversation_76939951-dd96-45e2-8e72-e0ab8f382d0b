{"version": 3, "sources": ["../../../src/agent/database/database.service.ts"], "sourcesContent": ["import { Injectable, OnModuleInit, OnModuleD<PERSON>roy } from '@nestjs/common';\nimport { DataSource } from 'typeorm';\nimport { InjectDataSource } from '@nestjs/typeorm';\n\n/**\n * Database service for raw SQL operations (Agent Module)\n * \n * This service provides access to TypeORM DataSource for executing raw SQL queries\n * without using entities or repositories. The agent module primarily needs to query\n * user_agent_runs table by ID and update run status.\n */\n@Injectable()\nexport class AgentDatabaseService implements OnModuleInit, OnModuleDestroy {\n  constructor(\n    @InjectDataSource()\n    private readonly dataSource: DataSource,\n  ) {}\n\n  async onModuleInit() {\n    // Ensure database connection is established\n    if (!this.dataSource.isInitialized) {\n      await this.dataSource.initialize();\n    }\n  }\n\n  async onModuleDestroy() {\n    // Clean up database connection\n    if (this.dataSource.isInitialized) {\n      await this.dataSource.destroy();\n    }\n  }\n\n  /**\n   * Get the TypeORM DataSource instance for raw SQL operations\n   * @returns DataSource instance\n   */\n  getDataSource(): DataSource {\n    return this.dataSource;\n  }\n\n  /**\n   * Execute a raw SQL query\n   * @param query SQL query string\n   * @param parameters Query parameters\n   * @returns Promise<any[]> Query results\n   */\n  async query(query: string, parameters?: any[]): Promise<any[]> {\n    try {\n      return await this.dataSource.query(query, parameters);\n    } catch (error) {\n      throw new Error(`Database query failed: ${error.message}`);\n    }\n  }\n\n  /**\n   * Execute a raw SQL query and return a single result\n   * @param query SQL query string\n   * @param parameters Query parameters\n   * @returns Promise<any | null> Single result or null\n   */\n  async queryOne(query: string, parameters?: any[]): Promise<any | null> {\n    const results = await this.query(query, parameters);\n    return results.length > 0 ? results[0] : null;\n  }\n}\n"], "names": ["AgentDatabaseService", "onModuleInit", "dataSource", "isInitialized", "initialize", "onModuleDestroy", "destroy", "getDataSource", "query", "parameters", "error", "Error", "message", "queryOne", "results", "length", "constructor"], "mappings": ";;;;+BAYaA;;;eAAAA;;;wBAZ6C;yBAC/B;0BACM;;;;;;;;;;;;;;;AAU1B,IAAA,AAAMA,uBAAN,MAAMA;IAMX,MAAMC,eAAe;QACnB,4CAA4C;QAC5C,IAAI,CAAC,IAAI,CAACC,UAAU,CAACC,aAAa,EAAE;YAClC,MAAM,IAAI,CAACD,UAAU,CAACE,UAAU;QAClC;IACF;IAEA,MAAMC,kBAAkB;QACtB,+BAA+B;QAC/B,IAAI,IAAI,CAACH,UAAU,CAACC,aAAa,EAAE;YACjC,MAAM,IAAI,CAACD,UAAU,CAACI,OAAO;QAC/B;IACF;IAEA;;;GAGC,GACDC,gBAA4B;QAC1B,OAAO,IAAI,CAACL,UAAU;IACxB;IAEA;;;;;GAKC,GACD,MAAMM,MAAMA,KAAa,EAAEC,UAAkB,EAAkB;QAC7D,IAAI;YACF,OAAO,MAAM,IAAI,CAACP,UAAU,CAACM,KAAK,CAACA,OAAOC;QAC5C,EAAE,OAAOC,OAAO;YACd,MAAM,IAAIC,MAAM,CAAC,uBAAuB,EAAED,MAAME,OAAO,EAAE;QAC3D;IACF;IAEA;;;;;GAKC,GACD,MAAMC,SAASL,KAAa,EAAEC,UAAkB,EAAuB;QACrE,MAAMK,UAAU,MAAM,IAAI,CAACN,KAAK,CAACA,OAAOC;QACxC,OAAOK,QAAQC,MAAM,GAAG,IAAID,OAAO,CAAC,EAAE,GAAG;IAC3C;IAlDAE,YACE,AACiBd,UAAsB,CACvC;aADiBA,aAAAA;IAChB;AAgDL"}