{"version": 3, "sources": ["../../../src/agent/interfaces/system-agent-config.interface.ts"], "sourcesContent": ["export enum ModelProviderEnum {\r\n  OPENAI = 'OPENAI',\r\n  XAI = 'XAI',\r\n  ANTHROPIC = 'ANTHROPIC',\r\n  GOOGLE = 'GOOGLE',\r\n  DEEPSEEK = 'DEEPSEEK',\r\n}\r\n\r\nexport enum InputModality {\r\n  TEXT = 'text',\r\n  IMAGE = 'image',\r\n  AUDIO = 'audio',\r\n  VIDEO = 'video',\r\n}\r\n\r\nexport enum OutputModality {\r\n  TEXT = 'text',\r\n  IMAGE = 'image',\r\n  AUDIO = 'audio',\r\n  VIDEO = 'video',\r\n}\r\n\r\nexport enum SamplingParameter {\r\n  TEMPERATURE = 'temperature',\r\n  TOP_P = 'top_p',\r\n  TOP_K = 'top_k',\r\n  MAX_TOKENS = 'max_tokens',\r\n  MAX_OUTPUT_TOKENS = 'max_output_tokens',\r\n}\r\n\r\nexport enum ModelFeature {\r\n  TOOL_CALL = 'tool_call',\r\n  PARALLEL_TOOL_CALL = 'parallel_tool_call',\r\n}\r\n\r\n\r\nexport type TrimmingType = 'top_k' | 'ai' | 'token';\r\n\r\nexport interface McpSseConfig {\r\n  serverName: string;\r\n  config: Record<string, any>;\r\n}\r\n\r\nexport interface SystemAgentConfig {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  instruction: string;\r\n  mcpConfig: McpSseConfig[];\r\n  vectorStoreId: string;\r\n  trimmingConfig: {\r\n    type: TrimmingType; // default to `top_k` always\r\n    threshold: number;\r\n  }\r\n  model: {\r\n    name: string;\r\n    provider: ModelProviderEnum;\r\n    inputModalities: InputModality[];\r\n    outputModalities: OutputModality[];\r\n    samplingParameters: SamplingParameter[];\r\n    features: ModelFeature[];\r\n    parameters?: {\r\n      temperature?: number;\r\n      topP?: number;\r\n      topK?: number;\r\n      maxTokens?: number;\r\n      maxOutputTokens?: number;\r\n    };\r\n    type: 'SYSTEM';\r\n    apiKeys: string[];\r\n  };\r\n}\r\n\r\n\r\nexport interface SystemAgentConfigMap {\r\n  [agentId: string]: SystemAgentConfig;\r\n}\r\n"], "names": ["InputModality", "ModelFeature", "ModelProviderEnum", "OutputModality", "SamplingParameter"], "mappings": ";;;;;;;;;;;QAQYA;eAAAA;;QAsBAC;eAAAA;;QA9BAC;eAAAA;;QAeAC;eAAAA;;QAOAC;eAAAA;;;AAtBL,IAAA,AAAKF,2CAAAA;;;;;;WAAAA;;AAQL,IAAA,AAAKF,uCAAAA;;;;;WAAAA;;AAOL,IAAA,AAAKG,wCAAAA;;;;;WAAAA;;AAOL,IAAA,AAAKC,2CAAAA;;;;;;WAAAA;;AAQL,IAAA,AAAKH,sCAAAA;;;WAAAA"}