"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AgentToolsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentToolsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const exceptions_1 = require("../../../common/exceptions");
const exceptions_2 = require("../exceptions");
const dto_1 = require("../dto");
let AgentToolsService = AgentToolsService_1 = class AgentToolsService {
    dataSource;
    logger = new common_1.Logger(AgentToolsService_1.name);
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async getAgentTools(agentId) {
        try {
            const agent = await this.dataSource
                .createQueryBuilder()
                .select(['id', 'name'])
                .from('agents', 'agent')
                .where('agent.id = :agentId', { agentId })
                .getRawOne();
            if (!agent) {
                throw new exceptions_1.AppException(exceptions_2.TOOLS_BUILD_IN_ERROR_CODES.AGENT_NOT_FOUND, `Không tìm thấy agent với ID ${agentId}`);
            }
            const agentUser = await this.dataSource
                .createQueryBuilder()
                .select(['type_id'])
                .from('agents_user', 'agent_user')
                .where('agent_user.id = :agentId', { agentId })
                .getRawOne();
            if (!agentUser || !agentUser.type_id) {
                throw new exceptions_1.AppException(exceptions_2.TOOLS_BUILD_IN_ERROR_CODES.AGENT_TYPE_NOT_FOUND, `Agent không có loại agent`);
            }
            const tools = [];
            const newFormatTools = [];
            const useNewFormat = true;
            if (useNewFormat) {
                return {
                    tools: newFormatTools
                };
            }
            return {
                agentId: agent.id,
                agentName: agent.name,
                tools: tools,
                totalTools: 0,
            };
        }
        catch (error) {
            if (error instanceof exceptions_1.AppException) {
                throw error;
            }
            this.logger.error(`Lỗi khi lấy danh sách tool của agent: ${error.message}`, error.stack);
            throw new exceptions_1.AppException(exceptions_2.TOOLS_BUILD_IN_ERROR_CODES.AGENT_TOOLS_FETCH_FAILED, `Lỗi khi lấy danh sách tool của agent: ${error.message}`);
        }
    }
    async getToolsByTypeAgentId(typeAgentId) {
        try {
            return [];
        }
        catch (error) {
            this.logger.error(`Error getting tools: ${error.message}`, error.stack);
            return [];
        }
    }
    async getToolsByGroupId(groupId) {
        try {
            return [];
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy danh sách tool trong nhóm: ${error.message}`, error.stack);
            return [];
        }
    }
    async getLatestToolVersion(toolId) {
        try {
            return await this.dataSource
                .createQueryBuilder()
                .select([
                'id',
                'tool_id',
                'version_number',
                'tool_name',
                'tool_description',
                'parameters',
                'status'
            ])
                .from('admin_tool_versions', 'atv')
                .where('atv.tool_id = :toolId', { toolId })
                .andWhere('atv.status != :status', { status: 'DEPRECATED' })
                .orderBy('atv.version_number', 'DESC')
                .limit(1)
                .getRawOne();
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy phiên bản mới nhất của tool: ${error.message}`, error.stack);
            return null;
        }
    }
    async convertToNewFormat(tools) {
        const newFormatTools = [];
        for (const tool of tools) {
            try {
                const parameters = tool.parameters || {
                    type: 'object',
                    properties: {},
                    required: [],
                };
                let newTool;
                if (tool.type === 'USER') {
                    newTool = {
                        parameter: {
                            name: tool.name,
                            description: tool.description,
                            inputSchema: this.convertParametersToInputSchema(parameters, tool.type),
                        },
                        extra: {
                            url: tool.endpoint || '',
                            method: tool.method || 'GET',
                        },
                        typeTool: dto_1.ToolTypeEnum.IN_SYSTEM,
                    };
                }
                else if (tool.type === 'CUSTOM') {
                    const extra = {
                        url: this.getToolUrl(tool),
                        method: this.getToolMethod(tool),
                    };
                    if (tool.apiKeyInfo && tool.apiKeyInfo.paramName && tool.apiKeyInfo.apiKey) {
                        const { paramName, apiKey, schemeName } = tool.apiKeyInfo;
                        if (schemeName && schemeName !== 'default') {
                            extra.headers = {
                                ...(extra.headers || {}),
                                [paramName]: `${apiKey}`
                            };
                            this.logger.debug(`Sử dụng API key với scheme: ${schemeName} ${apiKey}`);
                        }
                        else {
                            extra.headers = {
                                ...(extra.headers || {}),
                                [paramName]: apiKey
                            };
                            this.logger.debug(`Sử dụng API key không có scheme: ${apiKey}`);
                        }
                    }
                    newTool = {
                        parameter: {
                            name: tool.name,
                            description: tool.description,
                            inputSchema: this.convertParametersToInputSchema(parameters, tool.type),
                        },
                        extra: extra,
                        typeTool: dto_1.ToolTypeEnum.OUT_SYSTEM,
                    };
                }
                else {
                    newTool = {
                        parameter: {
                            name: tool.name,
                            description: tool.description,
                            inputSchema: this.convertParametersToInputSchema(parameters, tool.type),
                        },
                        extra: {
                            url: this.getToolUrl(tool),
                            method: this.getToolMethod(tool),
                        },
                        typeTool: dto_1.ToolTypeEnum.IN_SYSTEM,
                    };
                }
                newFormatTools.push(newTool);
            }
            catch (error) {
                this.logger.error(`Lỗi khi chuyển đổi tool ${tool.id} sang định dạng mới: ${error.message}`);
                continue;
            }
        }
        return newFormatTools;
    }
    convertParametersToInputSchema(parameters, _toolType) {
        if (!parameters) {
            return {
                type: 'object',
                properties: {},
                required: [],
            };
        }
        const inputSchema = {
            type: 'object',
            required: parameters.required || [],
            properties: {
                query_param: {
                    type: 'object',
                    description: 'Tham số truy vấn',
                    properties: {},
                },
                path_param: {
                    type: 'object',
                    description: 'Tham số đường dẫn',
                    properties: {},
                },
                body: {
                    type: 'object',
                    description: 'Tham số body',
                    properties: {},
                },
            },
            description: parameters.description || 'Tham số đầu vào cho công cụ',
        };
        if (parameters.properties) {
            if (parameters.properties.query_param) {
                inputSchema.properties.query_param.properties = this.processParameterProperties(parameters.properties.query_param);
            }
            if (parameters.properties.path_param) {
                inputSchema.properties.path_param.properties = this.processParameterProperties(parameters.properties.path_param);
            }
            if (parameters.properties.body) {
                inputSchema.properties.body.properties = this.processParameterProperties(parameters.properties.body);
            }
        }
        return inputSchema;
    }
    processParameterProperties(paramProperties) {
        const result = {};
        if (!paramProperties)
            return result;
        if (paramProperties.properties) {
            return paramProperties.properties;
        }
        Object.keys(paramProperties).forEach(key => {
            const property = paramProperties[key];
            result[key] = {
                type: property.type || 'string',
                description: property.description || '',
            };
            if (property.type === 'array' && property.items) {
                result[key].items = property.items;
            }
            if (property.example) {
                result[key].example = property.example;
            }
        });
        return result;
    }
    getToolUrl(tool) {
        if (tool.integration) {
            return `${tool.integration.baseUrl}${tool.integration.endpoint}`;
        }
        if (tool.type === 'USER' && tool.endpoint) {
            return tool.endpoint;
        }
        return `http://localhost:3003/api/v1/agent`;
    }
    getToolMethod(tool) {
        if (tool.integration) {
            return tool.integration.method;
        }
        if (tool.type === 'USER' && tool.method) {
            return tool.method;
        }
        return 'GET';
    }
    extractPathParamsFromUrl(url) {
        const pathParams = [];
        const regex = /{([^}]+)}/g;
        let match;
        while ((match = regex.exec(url)) !== null) {
            pathParams.push(match[1]);
        }
        return pathParams;
    }
};
exports.AgentToolsService = AgentToolsService;
exports.AgentToolsService = AgentToolsService = AgentToolsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], AgentToolsService);
//# sourceMappingURL=agent-tools.service.js.map