"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const microservices_1 = require("@nestjs/microservices");
const chat_service_1 = require("./services/chat.service");
const agent_config_service_1 = require("./services/agent-config.service");
const conversation_thread_service_1 = require("./services/conversation-thread.service");
const chat_controller_1 = require("./controllers/chat.controller");
const stream_controller_1 = require("./controllers/stream.controller");
const conversation_thread_controller_1 = require("./controllers/conversation-thread.controller");
const database_1 = require("./database");
const config_1 = require("../../config");
const constants_1 = require("../../config/constants");
const entities_1 = require("../agent/entities");
const user_conversation_thread_entity_1 = require("./entities/user-conversation-thread.entity");
let ChatModule = class ChatModule {
};
exports.ChatModule = ChatModule;
exports.ChatModule = ChatModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                entities_1.Agent,
                entities_1.AgentSystem,
                entities_1.AgentUser,
                entities_1.UserMultiAgent,
                user_conversation_thread_entity_1.UserConversationThread,
            ]),
            microservices_1.ClientsModule.registerAsync([
                {
                    name: 'REDIS_CLIENT',
                    useFactory: (configService) => {
                        const redisConfig = configService.getConfig(constants_1.ConfigType.Redis);
                        const url = new URL(redisConfig.url);
                        return {
                            transport: microservices_1.Transport.REDIS,
                            options: {
                                host: url.hostname,
                                port: parseInt(url.port) || 6379,
                                password: redisConfig.password || url.password,
                                db: parseInt(url.pathname.slice(1)) || 0,
                                retryDelayOnFailover: 100,
                                maxRetriesPerRequest: 3,
                            },
                        };
                    },
                    inject: [config_1.ConfigService],
                },
            ]),
        ],
        controllers: [
            chat_controller_1.ChatController,
            stream_controller_1.StreamController,
            conversation_thread_controller_1.ConversationThreadController,
        ],
        providers: [
            chat_service_1.ChatService,
            agent_config_service_1.AgentConfigService,
            conversation_thread_service_1.ConversationThreadService,
            database_1.ChatDatabaseService,
            database_1.UserAgentRunsQueries,
            database_1.AgentConfigQueries,
            database_1.UserMessagesQueries,
        ],
        exports: [
            chat_service_1.ChatService,
            agent_config_service_1.AgentConfigService,
            conversation_thread_service_1.ConversationThreadService,
            database_1.ChatDatabaseService,
            database_1.UserAgentRunsQueries,
            database_1.AgentConfigQueries,
            database_1.UserMessagesQueries,
        ],
    })
], ChatModule);
//# sourceMappingURL=chat.module.js.map