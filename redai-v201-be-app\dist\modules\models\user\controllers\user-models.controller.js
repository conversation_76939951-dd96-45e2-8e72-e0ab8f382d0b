"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserModelsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const swagger_2 = require("../../../../common/swagger");
const decorators_1 = require("../../../auth/decorators");
const guards_1 = require("../../../auth/guards");
const response_1 = require("../../../../common/response");
const user_models_1 = require("../dto/user-models");
const user_models_service_1 = require("../services/user-models.service");
const user_model_fine_tune_1 = require("../dto/user-model-fine-tune");
const system_models_response_dto_1 = require("../dto/user-models/system-models-response.dto");
const dto_1 = require("../../../../common/dto");
let UserModelsController = class UserModelsController {
    userModelsService;
    constructor(userModelsService) {
        this.userModelsService = userModelsService;
    }
    async getUserModelsByKeys(userId, keyllmId, queryDto) {
        return this.userModelsService.getUserModelsByKeys(userId, keyllmId, queryDto);
    }
    async getSystemModels(queryDto) {
        return this.userModelsService.getSystemModels(queryDto);
    }
    async getUserFineTuneDatasets(userId, queryDto) {
        return this.userModelsService.getUserFineTuneDatasets(userId, queryDto);
    }
};
exports.UserModelsController = UserModelsController;
__decorate([
    (0, common_1.Get)('user-models-by-keys/:keyllmId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Lấy danh sách models theo user keys',
        description: 'Lấy danh sách models mà user có thể sử dụng thông qua LLM key cụ thể'
    }),
    (0, swagger_1.ApiParam)({
        name: 'keyllmId',
        description: 'ID của user LLM key',
        example: '123e4567-e89b-12d3-a456-426614174000'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Danh sách models theo user keys',
        schema: response_1.ApiResponseDto.getPaginatedSchema(user_models_1.UserModelsResponseDto)
    }),
    __param(0, (0, decorators_1.CurrentUser)('id')),
    __param(1, (0, common_1.Param)('keyllmId')),
    __param(2, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String, dto_1.QueryDto]),
    __metadata("design:returntype", Promise)
], UserModelsController.prototype, "getUserModelsByKeys", null);
__decorate([
    (0, common_1.Get)('system-models'),
    (0, swagger_1.ApiOperation)({
        summary: 'Lấy danh sách active system models',
        description: 'Lấy danh sách models hệ thống đang hoạt động mà user có thể sử dụng'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Danh sách active system models',
        schema: response_1.ApiResponseDto.getPaginatedSchema(system_models_response_dto_1.SystemModelsResponseDto)
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_models_1.SystemModelsQueryDto]),
    __metadata("design:returntype", Promise)
], UserModelsController.prototype, "getSystemModels", null);
__decorate([
    (0, common_1.Get)('fine-tune-datasets'),
    (0, swagger_1.ApiOperation)({
        summary: 'Lấy danh sách user model fine-tune của user',
        description: 'Lấy danh sách các model đã fine-tune mà user đã tạo'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Danh sách user model fine-tune của user',
        schema: response_1.ApiResponseDto.getPaginatedSchema(user_model_fine_tune_1.UserModelFineTuneResponseDto)
    }),
    __param(0, (0, decorators_1.CurrentUser)('id')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.QueryDto]),
    __metadata("design:returntype", Promise)
], UserModelsController.prototype, "getUserFineTuneDatasets", null);
exports.UserModelsController = UserModelsController = __decorate([
    (0, swagger_1.ApiTags)(swagger_2.SWAGGER_API_TAGS.USER_API_KEY_MODEL),
    (0, common_1.Controller)('models'),
    (0, common_1.UseGuards)(guards_1.JwtUserGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiExtraModels)(response_1.ApiResponseDto, response_1.PaginatedResult, user_models_1.UserModelsResponseDto, system_models_response_dto_1.SystemModelsResponseDto, user_model_fine_tune_1.UserModelFineTuneResponseDto),
    __metadata("design:paramtypes", [user_models_service_1.UserModelsService])
], UserModelsController);
//# sourceMappingURL=user-models.controller.js.map