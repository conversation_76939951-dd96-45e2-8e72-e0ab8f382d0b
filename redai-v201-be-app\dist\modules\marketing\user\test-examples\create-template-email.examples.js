"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.invalidExamples = exports.createTemplateEmailExamples = void 0;
exports.createTemplateEmailExamples = {
    basicNewsletter: {
        name: "Newsletter tháng 1",
        subject: "🎉 Khuyến mãi đặc biệt dành cho bạn!",
        htmlContent: `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Newsletter</title>
</head>
<body>
    <h1>Xin chào {customer_name}!</h1>
    <p>Chúng tôi có ưu đãi đặc biệt cho {product_name} với giảm giá {discount_amount}%!</p>
    <p>Hạn chót: {expiry_date}</p>
    <a href="{shop_url}">Mua ngay</a>
</body>
</html>`,
        textContent: "Xin chào {customer_name}! Chúng tôi có ưu đãi đặc biệt cho {product_name} với giảm giá {discount_amount}%! Hạn chót: {expiry_date}. Truy cập: {shop_url}",
        type: "NEWSLETTER",
        previewText: "Đừng bỏ lỡ ưu đãi lên đến 50% cho tất cả sản phẩm...",
        tags: ["newsletter", "promotion", "monthly"],
        variables: [
            {
                name: "customer_name",
                type: "TEXT",
                defaultValue: "Khách hàng",
                required: true,
                description: "Tên khách hàng sẽ được hiển thị trong email"
            },
            {
                name: "product_name",
                type: "TEXT",
                defaultValue: "sản phẩm",
                required: false,
                description: "Tên sản phẩm khuyến mãi"
            },
            {
                name: "discount_amount",
                type: "NUMBER",
                defaultValue: "10",
                required: false,
                description: "Phần trăm giảm giá"
            },
            {
                name: "expiry_date",
                type: "DATE",
                defaultValue: "31/12/2024",
                required: false,
                description: "Ngày hết hạn khuyến mãi"
            },
            {
                name: "shop_url",
                type: "URL",
                defaultValue: "https://shop.example.com",
                required: false,
                description: "Link đến cửa hàng"
            }
        ]
    },
    welcomeEmail: {
        name: "Email chào mừng",
        subject: "Chào mừng bạn đến với {company_name}!",
        htmlContent: `<div style="font-family: Arial, sans-serif;">
    <h2>Chào mừng {user_name}!</h2>
    <p>Cảm ơn bạn đã đăng ký tài khoản tại {company_name}.</p>
    <p>Mã xác thực của bạn là: <strong>{verification_code}</strong></p>
</div>`,
        type: "WELCOME",
        previewText: "Chào mừng bạn đến với hệ thống của chúng tôi",
        tags: ["welcome", "verification"],
        variables: [
            {
                name: "user_name",
                type: "TEXT",
                defaultValue: "Người dùng",
                required: true,
                description: "Tên người dùng"
            },
            {
                name: "company_name",
                type: "TEXT",
                defaultValue: "RedAI",
                required: true,
                description: "Tên công ty"
            },
            {
                name: "verification_code",
                type: "TEXT",
                required: true,
                description: "Mã xác thực"
            }
        ]
    },
    promotionalEmail: {
        name: "Flash Sale 24h",
        subject: "⚡ Flash Sale 24h - Giảm đến 70%",
        htmlContent: `<!DOCTYPE html>
<html>
<body>
    <div style="text-align: center;">
        <img src="{banner_image}" alt="Flash Sale" style="max-width: 100%;">
        <h1>Flash Sale 24h</h1>
        <p>Chỉ còn {hours_left} giờ để nhận ưu đãi!</p>
        <p>Giảm giá lên đến {max_discount}% cho tất cả sản phẩm</p>
        <a href="{sale_url}" style="background: #ff6b6b; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px;">
            Mua ngay
        </a>
    </div>
</body>
</html>`,
        textContent: "Flash Sale 24h! Chỉ còn {hours_left} giờ để nhận ưu đãi giảm giá lên đến {max_discount}%. Truy cập: {sale_url}",
        type: "PROMOTIONAL",
        previewText: "Flash Sale 24h - Giảm đến 70% tất cả sản phẩm",
        tags: ["flash-sale", "promotion", "urgent"],
        variables: [
            {
                name: "banner_image",
                type: "IMAGE",
                defaultValue: "https://example.com/banner.jpg",
                required: false,
                description: "Hình ảnh banner cho flash sale"
            },
            {
                name: "hours_left",
                type: "NUMBER",
                defaultValue: "24",
                required: true,
                description: "Số giờ còn lại của flash sale"
            },
            {
                name: "max_discount",
                type: "NUMBER",
                defaultValue: "70",
                required: true,
                description: "Phần trăm giảm giá tối đa"
            },
            {
                name: "sale_url",
                type: "URL",
                defaultValue: "https://shop.example.com/flash-sale",
                required: true,
                description: "Link đến trang flash sale"
            }
        ]
    },
    minimalTemplate: {
        name: "Template đơn giản",
        subject: "Thông báo từ hệ thống",
        htmlContent: "<p>Đây là một email thông báo đơn giản.</p>"
    },
    complexTemplate: {
        name: "Báo cáo đơn hàng chi tiết",
        subject: "Đơn hàng #{order_id} - Trạng thái: {order_status}",
        htmlContent: `<div>
    <h2>Thông tin đơn hàng #{order_id}</h2>
    <p>Khách hàng: {customer_name}</p>
    <p>Email: {customer_email}</p>
    <p>Ngày đặt: {order_date}</p>
    <p>Tổng tiền: {total_amount} VND</p>
    <p>Trạng thái: {order_status}</p>
    <p>Link theo dõi: <a href="{tracking_url}">Xem chi tiết</a></p>
</div>`,
        type: "TRANSACTIONAL",
        tags: ["order", "transaction", "notification"],
        variables: [
            {
                name: "order_id",
                type: "TEXT",
                required: true,
                description: "Mã đơn hàng"
            },
            {
                name: "customer_name",
                type: "TEXT",
                required: true,
                description: "Tên khách hàng"
            },
            {
                name: "customer_email",
                type: "TEXT",
                required: true,
                description: "Email khách hàng"
            },
            {
                name: "order_date",
                type: "DATE",
                required: true,
                description: "Ngày đặt hàng"
            },
            {
                name: "total_amount",
                type: "NUMBER",
                required: true,
                description: "Tổng số tiền đơn hàng"
            },
            {
                name: "order_status",
                type: "TEXT",
                required: true,
                description: "Trạng thái đơn hàng"
            },
            {
                name: "tracking_url",
                type: "URL",
                required: false,
                description: "Link theo dõi đơn hàng"
            }
        ]
    }
};
exports.invalidExamples = {
    emptyName: {
        name: "",
        subject: "Test subject",
        htmlContent: "<p>Test content</p>"
    },
    emptySubject: {
        name: "Test template",
        subject: "",
        htmlContent: "<p>Test content</p>"
    },
    emptyContent: {
        name: "Test template",
        subject: "Test subject",
        htmlContent: ""
    },
    invalidType: {
        name: "Test template",
        subject: "Test subject",
        htmlContent: "<p>Test content</p>",
        type: "INVALID_TYPE"
    },
    invalidVariableName: {
        name: "Test template",
        subject: "Test subject",
        htmlContent: "<p>Hello {123invalid}!</p>",
        variables: [
            {
                name: "123invalid",
                type: "TEXT"
            }
        ]
    },
    invalidVariableType: {
        name: "Test template",
        subject: "Test subject",
        htmlContent: "<p>Hello {user_name}!</p>",
        variables: [
            {
                name: "user_name",
                type: "INVALID_TYPE"
            }
        ]
    },
    duplicateVariableNames: {
        name: "Test template",
        subject: "Test subject",
        htmlContent: "<p>Hello {user_name}!</p>",
        variables: [
            {
                name: "user_name",
                type: "TEXT"
            },
            {
                name: "user_name",
                type: "TEXT"
            }
        ]
    }
};
//# sourceMappingURL=create-template-email.examples.js.map