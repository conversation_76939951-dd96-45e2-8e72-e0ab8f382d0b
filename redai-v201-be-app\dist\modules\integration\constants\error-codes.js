"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.INTEGRATION_ERROR_HTTP_STATUS = exports.INTEGRATION_ERROR_MESSAGES = exports.INTEGRATION_ERROR_CODES = void 0;
exports.INTEGRATION_ERROR_CODES = {
    INVALID_DATA: 'INVALID_DATA',
    ENCRYPTION_FAILED: 'ENCRYPTION_FAILED',
    DECRYPTION_FAILED: 'DECRYPTION_FAILED',
    PROVIDER_NOT_FOUND: 'PROVIDER_NOT_FOUND',
    UNSUPPORTED_PROVIDER: 'UNSUPPORTED_PROVIDER',
    PROVIDER_CONFIG_INVALID: 'PROVIDER_CONFIG_INVALID',
    CONNECTION_TEST_FAILED: 'CONNECTION_TEST_FAILED',
    INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
    PROVIDER_UNAVAILABLE: 'PROVIDER_UNAVAILABLE',
    TIMEOUT_ERROR: 'TIMEOUT_ERROR',
    NETWORK_ERROR: 'NETWORK_ERROR',
    GHN_INVALID_TOKEN: 'GHN_INVALID_TOKEN',
    GHN_SHOP_NOT_FOUND: 'GHN_SHOP_NOT_FOUND',
    GHN_API_ERROR: 'GHN_API_ERROR',
    GHTK_INVALID_TOKEN: 'GHTK_INVALID_TOKEN',
    GHTK_INVALID_PARTNER_CODE: 'GHTK_INVALID_PARTNER_CODE',
    GHTK_API_ERROR: 'GHTK_API_ERROR',
    AHAMOVE_INVALID_API_KEY: 'AHAMOVE_INVALID_API_KEY',
    AHAMOVE_INVALID_MOBILE: 'AHAMOVE_INVALID_MOBILE',
    AHAMOVE_AUTH_FAILED: 'AHAMOVE_AUTH_FAILED',
    AHAMOVE_API_ERROR: 'AHAMOVE_API_ERROR',
    JT_INVALID_USERNAME: 'JT_INVALID_USERNAME',
    JT_INVALID_API_KEY: 'JT_INVALID_API_KEY',
    JT_SIGNATURE_ERROR: 'JT_SIGNATURE_ERROR',
    JT_API_ERROR: 'JT_API_ERROR',
    USER_PROVIDER_NOT_FOUND: 'USER_PROVIDER_NOT_FOUND',
    USER_PROVIDER_CREATE_FAILED: 'USER_PROVIDER_CREATE_FAILED',
    USER_PROVIDER_UPDATE_FAILED: 'USER_PROVIDER_UPDATE_FAILED',
    USER_PROVIDER_DELETE_FAILED: 'USER_PROVIDER_DELETE_FAILED',
};
exports.INTEGRATION_ERROR_MESSAGES = {
    [exports.INTEGRATION_ERROR_CODES.INVALID_DATA]: 'Dữ liệu không hợp lệ',
    [exports.INTEGRATION_ERROR_CODES.ENCRYPTION_FAILED]: 'Lỗi mã hóa dữ liệu',
    [exports.INTEGRATION_ERROR_CODES.DECRYPTION_FAILED]: 'Lỗi giải mã dữ liệu',
    [exports.INTEGRATION_ERROR_CODES.PROVIDER_NOT_FOUND]: 'Không tìm thấy nhà cung cấp',
    [exports.INTEGRATION_ERROR_CODES.UNSUPPORTED_PROVIDER]: 'Nhà cung cấp không được hỗ trợ',
    [exports.INTEGRATION_ERROR_CODES.PROVIDER_CONFIG_INVALID]: 'Cấu hình nhà cung cấp không hợp lệ',
    [exports.INTEGRATION_ERROR_CODES.CONNECTION_TEST_FAILED]: 'Kiểm tra kết nối thất bại',
    [exports.INTEGRATION_ERROR_CODES.INVALID_CREDENTIALS]: 'Thông tin xác thực không hợp lệ',
    [exports.INTEGRATION_ERROR_CODES.PROVIDER_UNAVAILABLE]: 'Nhà cung cấp không khả dụng',
    [exports.INTEGRATION_ERROR_CODES.TIMEOUT_ERROR]: 'Hết thời gian chờ kết nối',
    [exports.INTEGRATION_ERROR_CODES.NETWORK_ERROR]: 'Lỗi kết nối mạng',
    [exports.INTEGRATION_ERROR_CODES.GHN_INVALID_TOKEN]: 'Token GHN không hợp lệ',
    [exports.INTEGRATION_ERROR_CODES.GHN_SHOP_NOT_FOUND]: 'Không tìm thấy Shop ID trong tài khoản GHN',
    [exports.INTEGRATION_ERROR_CODES.GHN_API_ERROR]: 'Lỗi API GHN',
    [exports.INTEGRATION_ERROR_CODES.GHTK_INVALID_TOKEN]: 'Token GHTK không hợp lệ',
    [exports.INTEGRATION_ERROR_CODES.GHTK_INVALID_PARTNER_CODE]: 'Mã đối tác GHTK không hợp lệ',
    [exports.INTEGRATION_ERROR_CODES.GHTK_API_ERROR]: 'Lỗi API GHTK',
    [exports.INTEGRATION_ERROR_CODES.AHAMOVE_INVALID_API_KEY]: 'API Key Ahamove không hợp lệ',
    [exports.INTEGRATION_ERROR_CODES.AHAMOVE_INVALID_MOBILE]: 'Số điện thoại Ahamove không hợp lệ',
    [exports.INTEGRATION_ERROR_CODES.AHAMOVE_AUTH_FAILED]: 'Xác thực Ahamove thất bại',
    [exports.INTEGRATION_ERROR_CODES.AHAMOVE_API_ERROR]: 'Lỗi API Ahamove',
    [exports.INTEGRATION_ERROR_CODES.JT_INVALID_USERNAME]: 'Username J&T không hợp lệ',
    [exports.INTEGRATION_ERROR_CODES.JT_INVALID_API_KEY]: 'API Key J&T không hợp lệ',
    [exports.INTEGRATION_ERROR_CODES.JT_SIGNATURE_ERROR]: 'Lỗi chữ ký J&T',
    [exports.INTEGRATION_ERROR_CODES.JT_API_ERROR]: 'Lỗi API J&T',
    [exports.INTEGRATION_ERROR_CODES.USER_PROVIDER_NOT_FOUND]: 'Không tìm thấy cấu hình nhà cung cấp',
    [exports.INTEGRATION_ERROR_CODES.USER_PROVIDER_CREATE_FAILED]: 'Tạo cấu hình nhà cung cấp thất bại',
    [exports.INTEGRATION_ERROR_CODES.USER_PROVIDER_UPDATE_FAILED]: 'Cập nhật cấu hình nhà cung cấp thất bại',
    [exports.INTEGRATION_ERROR_CODES.USER_PROVIDER_DELETE_FAILED]: 'Xóa cấu hình nhà cung cấp thất bại',
};
exports.INTEGRATION_ERROR_HTTP_STATUS = {
    [exports.INTEGRATION_ERROR_CODES.INVALID_DATA]: 400,
    [exports.INTEGRATION_ERROR_CODES.ENCRYPTION_FAILED]: 500,
    [exports.INTEGRATION_ERROR_CODES.DECRYPTION_FAILED]: 500,
    [exports.INTEGRATION_ERROR_CODES.PROVIDER_NOT_FOUND]: 404,
    [exports.INTEGRATION_ERROR_CODES.UNSUPPORTED_PROVIDER]: 400,
    [exports.INTEGRATION_ERROR_CODES.PROVIDER_CONFIG_INVALID]: 400,
    [exports.INTEGRATION_ERROR_CODES.CONNECTION_TEST_FAILED]: 400,
    [exports.INTEGRATION_ERROR_CODES.INVALID_CREDENTIALS]: 401,
    [exports.INTEGRATION_ERROR_CODES.PROVIDER_UNAVAILABLE]: 503,
    [exports.INTEGRATION_ERROR_CODES.TIMEOUT_ERROR]: 408,
    [exports.INTEGRATION_ERROR_CODES.NETWORK_ERROR]: 503,
    [exports.INTEGRATION_ERROR_CODES.GHN_INVALID_TOKEN]: 401,
    [exports.INTEGRATION_ERROR_CODES.GHN_SHOP_NOT_FOUND]: 404,
    [exports.INTEGRATION_ERROR_CODES.GHN_API_ERROR]: 502,
    [exports.INTEGRATION_ERROR_CODES.GHTK_INVALID_TOKEN]: 401,
    [exports.INTEGRATION_ERROR_CODES.GHTK_INVALID_PARTNER_CODE]: 401,
    [exports.INTEGRATION_ERROR_CODES.GHTK_API_ERROR]: 502,
    [exports.INTEGRATION_ERROR_CODES.AHAMOVE_INVALID_API_KEY]: 401,
    [exports.INTEGRATION_ERROR_CODES.AHAMOVE_INVALID_MOBILE]: 400,
    [exports.INTEGRATION_ERROR_CODES.AHAMOVE_AUTH_FAILED]: 401,
    [exports.INTEGRATION_ERROR_CODES.AHAMOVE_API_ERROR]: 502,
    [exports.INTEGRATION_ERROR_CODES.JT_INVALID_USERNAME]: 401,
    [exports.INTEGRATION_ERROR_CODES.JT_INVALID_API_KEY]: 401,
    [exports.INTEGRATION_ERROR_CODES.JT_SIGNATURE_ERROR]: 400,
    [exports.INTEGRATION_ERROR_CODES.JT_API_ERROR]: 502,
    [exports.INTEGRATION_ERROR_CODES.USER_PROVIDER_NOT_FOUND]: 404,
    [exports.INTEGRATION_ERROR_CODES.USER_PROVIDER_CREATE_FAILED]: 500,
    [exports.INTEGRATION_ERROR_CODES.USER_PROVIDER_UPDATE_FAILED]: 500,
    [exports.INTEGRATION_ERROR_CODES.USER_PROVIDER_DELETE_FAILED]: 500,
};
//# sourceMappingURL=error-codes.js.map