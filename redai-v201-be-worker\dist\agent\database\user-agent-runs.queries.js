"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "UserAgentRunsQueries", {
    enumerable: true,
    get: function() {
        return UserAgentRunsQueries;
    }
});
const _common = require("@nestjs/common");
const _databaseservice = require("./database.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let UserAgentRunsQueries = class UserAgentRunsQueries {
    /**
   * Get a user agent run by ID (main agent operation)
   * @param id Run ID
   * @returns Promise<UserAgentRun | null>
   */ async getRunById(id) {
        const query = `
      SELECT id, payload, status, created_at, created_by 
      FROM user_agent_runs 
      WHERE id = $1
    `;
        const result = await this.databaseService.query(query, [
            id
        ]);
        if (result.length === 0) {
            return null;
        }
        return {
            id: result[0].id,
            payload: result[0].payload,
            status: result[0].status,
            created_at: result[0].created_at,
            created_by: result[0].created_by
        };
    }
    /**
   * Update run status (agent updates status during processing)
   * @param id Run ID
   * @param status New status
   * @returns Promise<boolean> True if update was successful
   */ async updateRunStatus(id, status) {
        const query = `
      UPDATE user_agent_runs 
      SET status = $1 
      WHERE id = $2
    `;
        const result = await this.databaseService.query(query, [
            status,
            id
        ]);
        return Array.isArray(result) && result.length >= 0;
    }
    constructor(databaseService){
        this.databaseService = databaseService;
    }
};
UserAgentRunsQueries = _ts_decorate([
    (0, _common.Injectable)(),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _databaseservice.AgentDatabaseService === "undefined" ? Object : _databaseservice.AgentDatabaseService
    ])
], UserAgentRunsQueries);

//# sourceMappingURL=user-agent-runs.queries.js.map