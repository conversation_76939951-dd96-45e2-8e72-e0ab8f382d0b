"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RuleContract = exports.ContractTypeEnum = exports.ContractStatusEnum = void 0;
const typeorm_1 = require("typeorm");
var ContractStatusEnum;
(function (ContractStatusEnum) {
    ContractStatusEnum["DRAFT"] = "DRAFT";
    ContractStatusEnum["PENDING_APPROVAL"] = "PENDING_APPROVAL";
    ContractStatusEnum["APPROVED"] = "APPROVED";
    ContractStatusEnum["REJECTED"] = "REJECTED";
})(ContractStatusEnum || (exports.ContractStatusEnum = ContractStatusEnum = {}));
var ContractTypeEnum;
(function (ContractTypeEnum) {
    ContractTypeEnum["INDIVIDUAL"] = "INDIVIDUAL";
    ContractTypeEnum["BUSINESS"] = "BUSINESS";
})(ContractTypeEnum || (exports.ContractTypeEnum = ContractTypeEnum = {}));
let RuleContract = class RuleContract {
    id;
    userId;
    status;
    type;
    contractUrlPdf;
    createdAt;
    userSignatureAt;
    adminSignatureAt;
    rejectReason;
    updatedAt;
};
exports.RuleContract = RuleContract;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'integer' }),
    __metadata("design:type", Number)
], RuleContract.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id', type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], RuleContract.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'status',
        type: 'enum',
        enum: ContractStatusEnum,
        nullable: false,
    }),
    __metadata("design:type", String)
], RuleContract.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'type',
        type: 'enum',
        enum: ContractTypeEnum,
        nullable: true,
    }),
    __metadata("design:type", String)
], RuleContract.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'contract_url_pdf', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], RuleContract.prototype, "contractUrlPdf", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_at', type: 'bigint', nullable: true }),
    __metadata("design:type", Number)
], RuleContract.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_signature_at', type: 'bigint', nullable: true }),
    __metadata("design:type", Number)
], RuleContract.prototype, "userSignatureAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'admin_signature_at', type: 'bigint', nullable: true }),
    __metadata("design:type", Number)
], RuleContract.prototype, "adminSignatureAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'reject_reason', type: 'text', nullable: true }),
    __metadata("design:type", String)
], RuleContract.prototype, "rejectReason", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updated_at',
        type: 'bigint',
        default: () => '((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT)',
    }),
    __metadata("design:type", Number)
], RuleContract.prototype, "updatedAt", void 0);
exports.RuleContract = RuleContract = __decorate([
    (0, typeorm_1.Entity)({ name: 'rule_contract' })
], RuleContract);
//# sourceMappingURL=rule-contract.entity.js.map