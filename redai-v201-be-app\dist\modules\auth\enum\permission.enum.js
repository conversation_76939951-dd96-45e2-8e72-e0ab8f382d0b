"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Permission = void 0;
var Permission;
(function (Permission) {
    Permission["POINT_VIEW_LIST"] = "point:view_list";
    Permission["POINT_CREATE"] = "point:create";
    Permission["POINT_UPDATE_POINT"] = "point:update_point";
    Permission["AGENT_CREATE"] = "agent:create";
    Permission["USER_DASHBOARD_VIEW_INFO"] = "user_dashboard:view_info";
    Permission["AFFILIATE_CREATE"] = "affiliate:create";
    Permission["AFFILIATE_VIEW"] = "affiliate:view";
    Permission["AFFILIATE_UPDATE"] = "affiliate:update";
    Permission["AFFILIATE_DELETE"] = "affiliate:delete";
    Permission["AUTH_LOGIN"] = "auth:login";
    Permission["AUTH_LOGOUT"] = "auth:logout";
    Permission["AUTH_RESET_PASSWORD"] = "auth:reset-password";
    Permission["BLOG_CREATE"] = "blog:create";
    Permission["BLOG_VIEW"] = "blog:view";
    Permission["BLOG_UPDATE"] = "blog:update";
    Permission["BLOG_DELETE"] = "blog:delete";
    Permission["BLOG_PUBLISH"] = "blog:publish";
    Permission["BUSINESS_CREATE"] = "business:create";
    Permission["BUSINESS_VIEW"] = "business:view";
    Permission["BUSINESS_UPDATE"] = "business:update";
    Permission["BUSINESS_DELETE"] = "business:delete";
    Permission["DATA_IMPORT"] = "data:import";
    Permission["DATA_EXPORT"] = "data:export";
    Permission["DATA_VIEW"] = "data:view";
    Permission["DATA_DELETE"] = "data:delete";
    Permission["DATABASE_BACKUP"] = "database:backup";
    Permission["DATABASE_RESTORE"] = "database:restore";
    Permission["DATABASE_VIEW"] = "database:view";
    Permission["EMAIL_SEND"] = "email:send";
    Permission["EMAIL_CREATE_TEMPLATE"] = "email:create-template";
    Permission["EMAIL_UPDATE_TEMPLATE"] = "email:update-template";
    Permission["EMAIL_DELETE_TEMPLATE"] = "email:delete-template";
    Permission["EMAIL_VIEW_TEMPLATE"] = "email:view-template";
    Permission["EMPLOYEE_CREATE"] = "employee:create";
    Permission["EMPLOYEE_VIEW"] = "employee:view";
    Permission["EMPLOYEE_UPDATE"] = "employee:update";
    Permission["EMPLOYEE_DELETE"] = "employee:delete";
    Permission["EMPLOYEE_ASSIGN_ROLE"] = "employee:assign-role";
    Permission["GENERIC_MANAGE_SETTINGS"] = "generic:manage-settings";
    Permission["GENERIC_VIEW_LOGS"] = "generic:view-logs";
    Permission["GOOGLE_CONNECT"] = "google:connect";
    Permission["GOOGLE_DISCONNECT"] = "google:disconnect";
    Permission["INTEGRATION_CREATE"] = "integration:create";
    Permission["INTEGRATION_VIEW"] = "integration:view";
    Permission["INTEGRATION_UPDATE"] = "integration:update";
    Permission["INTEGRATION_DELETE"] = "integration:delete";
    Permission["INVOICE_CREATE"] = "invoice:create";
    Permission["INVOICE_VIEW"] = "invoice:view";
    Permission["INVOICE_UPDATE"] = "invoice:update";
    Permission["INVOICE_DELETE"] = "invoice:delete";
    Permission["INVOICE_EXPORT"] = "invoice:export";
    Permission["MARKETPLACE_CREATE_PRODUCT"] = "marketplace:create-product";
    Permission["MARKETPLACE_VIEW_PRODUCT"] = "marketplace:view-product";
    Permission["MARKETPLACE_UPDATE_PRODUCT"] = "marketplace:update-product";
    Permission["MARKETPLACE_DELETE_PRODUCT"] = "marketplace:delete-product";
    Permission["MARKETPLACE_VIEW_ORDER"] = "marketplace:view-order";
    Permission["MARKETPLACE_UPDATE_ORDER"] = "marketplace:update-order";
    Permission["MARKETING_CREATE_CAMPAIGN"] = "marketing:create-campaign";
    Permission["MARKETING_VIEW_CAMPAIGN"] = "marketing:view-campaign";
    Permission["MARKETING_UPDATE_CAMPAIGN"] = "marketing:update-campaign";
    Permission["MARKETING_DELETE_CAMPAIGN"] = "marketing:delete-campaign";
    Permission["MODEL_TRAINING_CREATE"] = "model-training:create";
    Permission["MODEL_TRAINING_VIEW"] = "model-training:view";
    Permission["MODEL_TRAINING_UPDATE"] = "model-training:update";
    Permission["MODEL_TRAINING_DELETE"] = "model-training:delete";
    Permission["PDF_CREATE"] = "pdf:create";
    Permission["PDF_VIEW"] = "pdf:view";
    Permission["PDF_DELETE"] = "pdf:delete";
    Permission["R_POINT_CREATE"] = "r-point:create";
    Permission["R_POINT_VIEW"] = "r-point:view";
    Permission["R_POINT_UPDATE"] = "r-point:update";
    Permission["R_POINT_DELETE"] = "r-point:delete";
    Permission["RECAPTCHA_CONFIGURE"] = "recaptcha:configure";
    Permission["RECAPTCHA_VIEW"] = "recaptcha:view";
    Permission["SMS_SEND"] = "sms:send";
    Permission["SMS_CREATE_TEMPLATE"] = "sms:create-template";
    Permission["SMS_UPDATE_TEMPLATE"] = "sms:update-template";
    Permission["SMS_DELETE_TEMPLATE"] = "sms:delete-template";
    Permission["SMS_VIEW_TEMPLATE"] = "sms:view-template";
    Permission["STRATEGY_CREATE"] = "strategy:create";
    Permission["STRATEGY_VIEW"] = "strategy:view";
    Permission["STRATEGY_UPDATE"] = "strategy:update";
    Permission["STRATEGY_DELETE"] = "strategy:delete";
    Permission["SUBSCRIPTION_CREATE"] = "subscription:create";
    Permission["SUBSCRIPTION_VIEW"] = "subscription:view";
    Permission["SUBSCRIPTION_UPDATE"] = "subscription:update";
    Permission["SUBSCRIPTION_DELETE"] = "subscription:delete";
    Permission["SUBSCRIPTION_ASSIGN_USER"] = "subscription:assign-user";
    Permission["SYSTEM_CONFIGURATION_VIEW"] = "system-configuration:view";
    Permission["SYSTEM_CONFIGURATION_UPDATE"] = "system-configuration:update";
    Permission["TASK_CREATE"] = "task:create";
    Permission["TASK_VIEW"] = "task:view";
    Permission["TASK_UPDATE"] = "task:update";
    Permission["TASK_DELETE"] = "task:delete";
    Permission["TASK_ASSIGN"] = "task:assign";
    Permission["USER_CREATE"] = "user:create";
    Permission["USER_VIEW"] = "user:view";
    Permission["USER_UPDATE"] = "user:update";
    Permission["USER_DELETE"] = "user:delete";
    Permission["USER_BLOCK"] = "user:block";
    Permission["USER_UNBLOCK"] = "user:unblock";
})(Permission || (exports.Permission = Permission = {}));
//# sourceMappingURL=permission.enum.js.map