"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EXECUTION_ERROR_CODES = void 0;
const common_1 = require("../../../common");
const common_2 = require("@nestjs/common");
exports.EXECUTION_ERROR_CODES = {
    EXECUTION_NOT_FOUND: new common_1.ErrorCode(10300, 'Không tìm thấy phiên thực thi', common_2.HttpStatus.NOT_FOUND),
    EXECUTION_CREATION_FAILED: new common_1.ErrorCode(10301, 'Tạo phiên thực thi thất bại', common_2.HttpStatus.INTERNAL_SERVER_ERROR),
    EXECUTION_UPDATE_FAILED: new common_1.ErrorCode(10302, 'Cập nhật phiên thực thi thất bại', common_2.HttpStatus.INTERNAL_SERVER_ERROR),
    EXECUTION_DELETE_FAILED: new common_1.ErrorCode(10303, '<PERSON><PERSON>a phiên thực thi thất bại', common_2.HttpStatus.INTERNAL_SERVER_ERROR),
    EXECUTION_FETCH_FAILED: new common_1.ErrorCode(10304, 'Lấy thông tin phiên thực thi thất bại', common_2.HttpStatus.INTERNAL_SERVER_ERROR),
    EXECUTION_UNAUTHORIZED: new common_1.ErrorCode(10310, 'Không có quyền truy cập phiên thực thi này', common_2.HttpStatus.FORBIDDEN),
    EXECUTION_ALREADY_COMPLETED: new common_1.ErrorCode(10320, 'Phiên thực thi đã hoàn thành', common_2.HttpStatus.BAD_REQUEST),
    EXECUTION_ALREADY_FAILED: new common_1.ErrorCode(10321, 'Phiên thực thi đã thất bại', common_2.HttpStatus.BAD_REQUEST),
    EXECUTION_ALREADY_CANCELLED: new common_1.ErrorCode(10322, 'Phiên thực thi đã bị hủy', common_2.HttpStatus.BAD_REQUEST),
    EXECUTION_INVALID_STATUS: new common_1.ErrorCode(10323, 'Trạng thái phiên thực thi không hợp lệ', common_2.HttpStatus.BAD_REQUEST),
    EXECUTION_CANNOT_RESUME: new common_1.ErrorCode(10324, 'Không thể tiếp tục phiên thực thi này', common_2.HttpStatus.BAD_REQUEST),
    EXECUTION_STEP_FAILED: new common_1.ErrorCode(10330, 'Thực thi bước thất bại', common_2.HttpStatus.INTERNAL_SERVER_ERROR),
    EXECUTION_MISSING_INPUT: new common_1.ErrorCode(10331, 'Thiếu dữ liệu đầu vào cho bước thực thi', common_2.HttpStatus.BAD_REQUEST),
    EXECUTION_INVALID_INPUT: new common_1.ErrorCode(10332, 'Dữ liệu đầu vào không hợp lệ cho bước thực thi', common_2.HttpStatus.BAD_REQUEST),
    EXECUTION_TIMEOUT: new common_1.ErrorCode(10333, 'Thực thi bước bị vượt quá thời gian', common_2.HttpStatus.REQUEST_TIMEOUT),
    EXECUTION_EXTERNAL_SERVICE_ERROR: new common_1.ErrorCode(10334, 'Lỗi dịch vụ bên ngoài khi thực thi bước', common_2.HttpStatus.BAD_GATEWAY),
    EXECUTION_LIMIT_EXCEEDED: new common_1.ErrorCode(10340, 'Đã vượt quá giới hạn số lượng phiên thực thi', common_2.HttpStatus.BAD_REQUEST),
    EXECUTION_RATE_LIMIT: new common_1.ErrorCode(10341, 'Đã vượt quá tần suất thực thi nhiệm vụ', common_2.HttpStatus.TOO_MANY_REQUESTS),
};
//# sourceMappingURL=execution.exceptions.js.map