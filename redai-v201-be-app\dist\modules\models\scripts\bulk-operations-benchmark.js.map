{"version": 3, "file": "bulk-operations-benchmark.js", "sourceRoot": "", "sources": ["../../../../src/modules/models/scripts/bulk-operations-benchmark.ts"], "names": [], "mappings": ";;AAiWS,gEAA0B;AAjWnC,uCAA2C;AAC3C,oDAAgD;AAChD,6FAAsG;AACtG,uFAAkF;AAClF,mFAA8E;AAC9E,qGAA8F;AAC9F,iGAA0F;AAC1F,yFAAoF;AAkBpF,KAAK,UAAU,0BAA0B;IACvC,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;IAEtE,IAAI,CAAC;QAEH,MAAM,GAAG,GAAG,MAAM,kBAAW,CAAC,wBAAwB,CAAC,sBAAS,CAAC,CAAC;QAGlE,MAAM,cAAc,GAAG,GAAG,CAAC,GAAG,CAAC,0DAA0B,CAAC,CAAC;QAC3D,MAAM,gBAAgB,GAAG,GAAG,CAAC,GAAG,CAAC,iDAAsB,CAAC,CAAC;QACzD,MAAM,cAAc,GAAG,GAAG,CAAC,GAAG,CAAC,6CAAoB,CAAC,CAAC;QACrD,MAAM,iBAAiB,GAAG,GAAG,CAAC,GAAG,CAAC,6DAA2B,CAAC,CAAC;QAC/D,MAAM,eAAe,GAAG,GAAG,CAAC,GAAG,CAAC,yDAAyB,CAAC,CAAC;QAC3D,MAAM,kBAAkB,GAAG,GAAG,CAAC,GAAG,CAAC,uDAAyB,CAAC,CAAC;QAE9D,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QAGnD,MAAM,aAAa,GAAG;YACpB,EAAE,WAAW,EAAE,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE;YAC/C,EAAE,WAAW,EAAE,EAAE,EAAE,WAAW,EAAE,cAAc,EAAE;YAChD,EAAE,WAAW,EAAE,GAAG,EAAE,WAAW,EAAE,aAAa,EAAE;YAChD,EAAE,WAAW,EAAE,GAAG,EAAE,WAAW,EAAE,kBAAkB,EAAE;SACtD,CAAC;QAEF,MAAM,OAAO,GAAsB,EAAE,CAAC;QAEtC,KAAK,MAAM,QAAQ,IAAI,aAAa,EAAE,CAAC;YACrC,OAAO,CAAC,GAAG,CAAC,cAAc,QAAQ,CAAC,WAAW,KAAK,QAAQ,CAAC,WAAW,UAAU,CAAC,CAAC;YAGnF,MAAM,QAAQ,GAAG,qBAAqB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAC7D,MAAM,SAAS,GAAG,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAG9C,MAAM,gBAAgB,GAAG,MAAM,wBAAwB,CACrD,QAAQ,EACR,SAAS,EACT,gBAAgB,EAChB,iBAAiB,EACjB,kBAAkB,EAClB,QAAQ,CAAC,WAAW,CACrB,CAAC;YACF,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAG/B,MAAM,eAAe,CAAC,QAAQ,EAAE,SAAS,EAAE,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;YAGhF,MAAM,UAAU,GAAG,MAAM,kBAAkB,CACzC,QAAQ,EACR,SAAS,EACT,cAAc,EACd,kBAAkB,EAClB,QAAQ,CAAC,WAAW,CACrB,CAAC;YACF,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAGzB,MAAM,eAAe,CAAC,QAAQ,EAAE,SAAS,EAAE,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;YAGhF,cAAc,CAAC,gBAAgB,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;YAEvD,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;QAGD,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAE/B,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;IAErE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QACpD,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;AACH,CAAC;AAKD,KAAK,UAAU,wBAAwB,CACrC,QAAyB,EACzB,KAAa,EACb,gBAAwC,EACxC,iBAA8C,EAC9C,OAAkC,EAClC,QAAgB;IAEhB,MAAM,WAAW,GAAG,yBAAyB,QAAQ,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;IAEtE,OAAO,CAAC,eAAe,CAAC,WAAW,EAAE;QACnC,QAAQ,EAAE,uBAAuB;QACjC,QAAQ;QACR,WAAW,EAAE,QAAQ,CAAC,MAAM;KAC7B,CAAC,CAAC;IAEH,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;IACnD,IAAI,UAAU,GAAG,CAAC,CAAC;IAGnB,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;QAC5B,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzE,UAAU,EAAE,CAAC;YAEb,IAAI,eAAuB,CAAC;YAE5B,IAAI,CAAC,aAAa,EAAE,CAAC;gBAEnB,MAAM,QAAQ,GAAG,gBAAgB,CAAC,MAAM,CAAC;oBACvC,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,eAAe,EAAE,IAAI,CAAC,eAAe;oBACrC,MAAM,EAAE,IAAI;iBACb,CAAC,CAAC;gBACH,MAAM,UAAU,GAAG,MAAM,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACzD,eAAe,GAAG,UAAU,CAAC,EAAE,CAAC;gBAChC,UAAU,EAAE,CAAC;YACf,CAAC;iBAAM,CAAC;gBACN,eAAe,GAAG,aAAa,CAAC,EAAE,CAAC;YACrC,CAAC;YAGD,MAAM,eAAe,GAAG,MAAM,iBAAiB,CAAC,iBAAiB,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YAC1F,UAAU,EAAE,CAAC;YAEb,IAAI,CAAC,eAAe,EAAE,CAAC;gBAErB,MAAM,OAAO,GAAG,iBAAiB,CAAC,MAAM,CAAC;oBACvC,OAAO,EAAE,eAAe;oBACxB,QAAQ,EAAE,KAAK;iBAChB,CAAC,CAAC;gBACH,MAAM,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACtC,UAAU,EAAE,CAAC;YACf,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,IAAI,CAAC,OAAO,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QACnF,CAAC;IACH,CAAC;IAED,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;IACpD,MAAM,SAAS,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;IAEjD,OAAO;QACL,SAAS,EAAE,wBAAwB;QACnC,QAAQ,EAAE,YAAY;QACtB,WAAW,EAAE,QAAQ,CAAC,MAAM;QAC5B,QAAQ,EAAE,OAAO,EAAE,QAAQ,IAAI,CAAC;QAChC,UAAU,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;QAC/D,WAAW,EAAE,SAAS,GAAG,WAAW;QACpC,eAAe,EAAE,UAAU;KAC5B,CAAC;AACJ,CAAC;AAKD,KAAK,UAAU,kBAAkB,CAC/B,QAAyB,EACzB,KAAa,EACb,cAA0C,EAC1C,OAAkC,EAClC,QAAgB;IAEhB,MAAM,WAAW,GAAG,mBAAmB,QAAQ,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;IAEhE,OAAO,CAAC,eAAe,CAAC,WAAW,EAAE;QACnC,QAAQ,EAAE,iBAAiB;QAC3B,QAAQ;QACR,WAAW,EAAE,QAAQ,CAAC,MAAM;KAC7B,CAAC,CAAC;IAEH,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;IAGnD,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAE5E,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;IACpD,MAAM,SAAS,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;IAIjD,MAAM,gBAAgB,GAAG,CAAC,CAAC;IAE3B,OAAO;QACL,SAAS,EAAE,wBAAwB;QACnC,QAAQ,EAAE,MAAM;QAChB,WAAW,EAAE,QAAQ,CAAC,MAAM;QAC5B,QAAQ,EAAE,OAAO,EAAE,QAAQ,IAAI,CAAC;QAChC,UAAU,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;QAC/D,WAAW,EAAE,SAAS,GAAG,WAAW;QACpC,eAAe,EAAE,gBAAgB;KAClC,CAAC;AACJ,CAAC;AAKD,SAAS,cAAc,CACrB,gBAAiC,EACjC,UAA2B,EAC3B,QAAa;IAEb,MAAM,OAAO,GAAG,gBAAgB,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;IAChE,MAAM,cAAc,GAAG,CAAC,gBAAgB,CAAC,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC,GAAG,gBAAgB,CAAC,eAAe,GAAG,GAAG,CAAC;IAChI,MAAM,qBAAqB,GAAG,CAAC,UAAU,CAAC,UAAU,GAAG,gBAAgB,CAAC,UAAU,CAAC,GAAG,gBAAgB,CAAC,UAAU,GAAG,GAAG,CAAC;IAExH,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,CAAC,WAAW,KAAK,QAAQ,CAAC,WAAW,YAAY,CAAC,CAAC;IACzF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAChD,OAAO,CAAC,GAAG,CAAC,kBAAkB,gBAAgB,CAAC,QAAQ,IAAI,CAAC,CAAC;IAC7D,OAAO,CAAC,GAAG,CAAC,oBAAoB,gBAAgB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;IACtF,OAAO,CAAC,GAAG,CAAC,0BAA0B,gBAAgB,CAAC,eAAe,EAAE,CAAC,CAAC;IAC1E,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAEhG,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC1C,OAAO,CAAC,GAAG,CAAC,kBAAkB,UAAU,CAAC,QAAQ,IAAI,CAAC,CAAC;IACvD,OAAO,CAAC,GAAG,CAAC,oBAAoB,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;IAChF,OAAO,CAAC,GAAG,CAAC,0BAA0B,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC;IACpE,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAE1F,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAC9C,OAAO,CAAC,GAAG,CAAC,iBAAiB,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;IAC3D,OAAO,CAAC,GAAG,CAAC,yBAAyB,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACnE,OAAO,CAAC,GAAG,CAAC,gCAAgC,qBAAqB,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACjF,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACzF,CAAC;AAKD,SAAS,qBAAqB,CAAC,KAAa;IAC1C,MAAM,IAAI,GAAoB,EAAE,CAAC;IACjC,MAAM,SAAS,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;IACvE,MAAM,UAAU,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;IAEnE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;QAC/B,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;QACjD,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;QAEpD,IAAI,CAAC,IAAI,CAAC;YACR,OAAO,EAAE,GAAG,SAAS,IAAI,CAAC,OAAO;YACjC,eAAe,EAAE,YAAY,QAAQ,IAAI,CAAC,EAAE;YAC5C,QAAQ,EAAE;gBACR,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,CAAC;gBACR,QAAQ;aACT;SACF,CAAC,CAAC;IACL,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAKD,KAAK,UAAU,eAAe,CAC5B,QAAyB,EACzB,KAAa,EACb,gBAAwC,EACxC,iBAA8C;IAE9C,IAAI,CAAC;QAEH,MAAM,iBAAiB;aACpB,kBAAkB,EAAE;aACpB,MAAM,EAAE;aACR,KAAK,CAAC,mBAAmB,EAAE,EAAE,KAAK,EAAE,CAAC;aACrC,OAAO,EAAE,CAAC;QAGb,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAC9C,MAAM,gBAAgB;aACnB,kBAAkB,EAAE;aACpB,MAAM,EAAE;aACR,KAAK,CAAC,2BAA2B,EAAE,EAAE,QAAQ,EAAE,CAAC;aAChD,OAAO,EAAE,CAAC;IAEf,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IACjD,CAAC;AACH,CAAC;AAKD,SAAS,qBAAqB,CAAC,OAA0B;IACvD,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IACrC,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IACvD,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;IAExD,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC;IAC3E,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC;IAE/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAClD,MAAM,UAAU,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACxC,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;QAE5B,MAAM,OAAO,GAAG,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QACpD,MAAM,cAAc,GAAG,CAAC,UAAU,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,UAAU,CAAC,eAAe,GAAG,GAAG,CAAC;QAE9G,OAAO,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,aAAa,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC;IAChI,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IAClD,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;IAChE,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IAC/C,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;IAC5C,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;AACrD,CAAC;AAKD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,0BAA0B,EAAE;SACzB,IAAI,CAAC,GAAG,EAAE;QACT,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACvD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QAC7C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC"}