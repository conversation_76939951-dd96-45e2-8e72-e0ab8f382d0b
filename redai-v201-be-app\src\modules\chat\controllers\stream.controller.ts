import {
  BadRequestEx<PERSON>,
  Controller,
  Get,
  Logger,
  Param,
  Query,
  Req,
  Res,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  Api<PERSON>earerAuth,
} from '@nestjs/swagger';
import { RedisService } from '@shared/services/redis.service';
import { nanoid } from 'nanoid';

/**
 * Chat Stream Controller
 *
 * Provides Server-Sent Events (SSE) endpoint for streaming real-time chat events
 * from Redis Streams to frontend clients. Each connection receives messages
 * from the current run only, ensuring consistent experience across multiple devices.
 *
 * Key Features:
 * - Current run messages only (filtered by run ID)
 * - Multi-device synchronization with unique consumer groups
 * - Complete stream replay for current run across all devices
 * - Proper error handling and connection cleanup
 * - Real-time streaming with Redis Streams
 */
@ApiTags('Chat')
@ApiBearerAuth('JWT-auth')
@Controller('chat/stream')
export class StreamController {
  private readonly logger = new Logger(StreamController.name);

  constructor(private readonly redisService: RedisService) {}

  /**
   * SSE endpoint for streaming chat events to frontend clients
   * @param req Express request object
   * @param res Express response object
   * @param threadId Thread ID for the chat stream
   */
  @Get('events/:threadId')
  @ApiOperation({
    summary: 'Stream chat events via Server-Sent Events',
    description:
      'Establishes SSE connection to stream real-time chat events for a specific thread. Resumes from last unread position.',
  })
  @ApiParam({
    name: 'threadId',
    description: 'Chat thread ID to stream events for',
    example: 'thread_123456',
  })
  @ApiQuery({
    name: 'from',
    description:
      'Optional message ID to resume from. If not provided, starts from latest.',
    example: '1749266123456-0',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'SSE stream established successfully',
    headers: {
      'Content-Type': { description: 'text/event-stream' },
      'Cache-Control': { description: 'no-cache' },
      Connection: { description: 'keep-alive' },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid threadId parameter',
  })
  async streamEvents(
    @Req() req: Request,
    @Res() res: Response,
    @Param('threadId') threadId: string,
    @Query('from') fromMessageId?: string,
  ): Promise<void> {
    // Validate threadId parameter
    if (!threadId || threadId.trim() === '') {
      throw new BadRequestException('threadId is required and cannot be empty');
    }

    this.logger.log(`🔥 Starting chat SSE stream for thread ${threadId}`);

    try {
      // Set SSE headers for streaming
      res.set({
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-transform',
        Connection: 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
        'X-Accel-Buffering': 'no', // Disable nginx buffering
      });

      // Send initial connection confirmation
      const resumeMode = fromMessageId ? 'resume' : 'unconsumed';
      res.write(`event: connected\n`);
      res.write(
        `data: {"threadId":"${threadId}","from":"${fromMessageId || 'unconsumed'}","timestamp":${Date.now()},"status":"connected","mode":"${resumeMode}"}\n\n`,
      );

      this.logger.log(
        `✅ Chat SSE headers set for thread ${threadId} (${resumeMode}: ${fromMessageId || 'unconsumed'})`,
      );

      // Generate unique consumer group for this connection (multi-device support)
      const streamKey = `agent_stream:${threadId}`;
      const groupName = `sse-group:${threadId}:${nanoid()}`;
      const consumerId = `consumer-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      this.logger.log(
        `🔥 Creating consumer group ${groupName} for stream ${streamKey}`,
      );

      try {
        // Create consumer group starting from specified position or beginning
        const startPosition = fromMessageId || '$'; // '0' means from beginning to catch unconsumed messages
        await this.redisService.createConsumerGroup(
          streamKey,
          groupName,
          startPosition,
        );
        this.logger.log(
          `✅ Consumer group ${groupName} created successfully (starting from: ${startPosition})`,
        );

        // Start consuming messages (no filtering needed - position-based)
        await this.consumeStreamMessages(
          res,
          streamKey,
          groupName,
          consumerId,
          threadId,
          fromMessageId,
        );
      } catch (error) {
        this.logger.error(
          `💥 Failed to create consumer group ${groupName}:`,
          error,
        );
        if (!res.destroyed) {
          res.write(`event: error\n`);
          res.write(
            `data: {"error":"Failed to initialize stream consumer","threadId":"${threadId}"}\n\n`,
          );
          res.end();
        }
        return;
      }

      // Handle client disconnect
      req.on('close', () => {
        this.logger.log(`🔌 Client disconnected from chat thread ${threadId}`);
        this.cleanupConsumerGroup(streamKey, groupName);
      });

      req.on('error', (error) => {
        this.logger.error(`❌ Chat SSE error for thread ${threadId}:`, error);
        this.cleanupConsumerGroup(streamKey, groupName);
        if (!res.destroyed) {
          res.end();
        }
      });
    } catch (error) {
      this.logger.error(
        `💥 Failed to establish chat SSE for thread ${threadId}:`,
        error,
      );
      if (!res.headersSent) {
        throw error;
      }
    }
  }

  /**
   * Consume messages from Redis Stream and send via SSE (stream resume approach)
   * @param res Express response object
   * @param streamKey Redis stream key
   * @param groupName Consumer group name
   * @param consumerId Consumer ID
   * @param threadId Thread ID for logging
   * @param fromMessageId Optional message ID to resume from
   */
  private async consumeStreamMessages(
    res: Response,
    streamKey: string,
    groupName: string,
    consumerId: string,
    threadId: string,
    _fromMessageId?: string,
  ): Promise<void> {
    this.logger.log(
      `🔄 Starting stream consumption for thread ${threadId} (pub/sub + initial read)`,
    );

    const client = this.redisService.getDuplicateClient();
    const subscriber = client.duplicate();

    // Helper to parse field arrays
    const parseFields = (fields: string[]): Record<string, any> => {
      const obj: Record<string, any> = {};
      for (let i = 0; i < fields.length; i += 2) {
        const key = fields[i];
        try {
          obj[key] = JSON.parse(fields[i + 1]);
        } catch {
          obj[key] = fields[i + 1];
        }
      }
      return obj;
    };

    // Helper to read new messages
    const readNew = async () => {
      try {
        const chunks = await client.xreadgroup(
          'GROUP',
          groupName,
          consumerId,
          'COUNT',
          20,
          'STREAMS',
          streamKey,
          '>',
        );

        if (!chunks) return;

        // @ts-ignore
        const [[, messages]] = chunks;

        for (const [id, fields] of messages) {
          const payload = parseFields(fields);

          // Include SSE id header so client could reconnect with Last-Event-ID
          res.write(`id: ${id}\n`);
          res.write(`data: ${JSON.stringify(payload)}\n\n`);

          this.logger.debug(
            `� Sent message ${id} for thread ${threadId}: ${payload.event}`,
          );

          if (payload.event === 'llm_stream_end') {
            this.logger.log(`🏁 Stream ended for thread ${threadId}`);
            res.write(`event: end\n`);
            res.write(`data: ${JSON.stringify(payload.data || {})}\n\n`);
            await subscriber.unsubscribe(`stream_notify:${threadId}`);
            res.end();
            return;
          }

          if (payload.event === 'stream_error') {
            this.logger.error(
              `💥 Stream error for thread ${threadId}:`,
              payload.data,
            );
            res.write(`event: error\n`);
            res.write(`data: ${JSON.stringify(payload.data)}\n\n`);
            // Continue - wait for stream_end
          }
        }
      } catch (error) {
        this.logger.error(
          `💥 Error reading new messages for thread ${threadId}:`,
          error,
        );
        if (!res.destroyed) {
          res.write(`event: error\n`);
          res.write(
            `data: {"error":"Stream reading error","threadId":"${threadId}"}\n\n`,
          );
          res.end();
        }
      }
    };

    try {
      await readNew();
      // Subscribe to pub/sub notifications on this thread's channel
      await subscriber.subscribe(`stream_notify:${threadId}`);
      subscriber.on('message', async () => {
        await readNew();
      });

      // Initial catch-up: in case events arrived before we subscribed

      // Handle connection cleanup
      res.on('close', async () => {
        this.logger.log(`🛑 SSE connection closed for thread ${threadId}`);
        await subscriber.unsubscribe(`stream_notify:${threadId}`);
        subscriber.disconnect();
      });
    } catch (error) {
      this.logger.error(
        `💥 Error setting up stream consumption for thread ${threadId}:`,
        error,
      );
      if (!res.destroyed) {
        res.write(`event: error\n`);
        res.write(`data: {"error":"Setup error","threadId":"${threadId}"}\n\n`);
        res.end();
      }
    }
  }

  /**
   * Cleanup consumer group on disconnect
   * @param streamKey Redis stream key
   * @param groupName Consumer group name to cleanup
   */
  private async cleanupConsumerGroup(
    streamKey: string,
    groupName: string,
  ): Promise<void> {
    try {
      await this.redisService.deleteConsumerGroup(streamKey, groupName);
      this.logger.log(
        `🧹 Cleaned up consumer group ${groupName} for stream ${streamKey}`,
      );
    } catch (error) {
      this.logger.error(`💥 Error cleaning up consumer group ${groupName}:`, {
        message: error.message,
        stack: error.stack,
        name: error.name,
        groupName,
        streamKey,
        error: error,
      });
      // Don't throw - cleanup errors shouldn't affect the application
    }
  }

  /**
   * Health check endpoint for chat streaming service
   */
  @Get('health')
  @ApiOperation({
    summary: 'Health check for chat streaming service',
    description: 'Returns the health status of the chat streaming service',
  })
  @ApiResponse({
    status: 200,
    description: 'Service is healthy',
  })
  getHealth(): { status: string; timestamp: number; service: string } {
    return {
      status: 'healthy',
      timestamp: Date.now(),
      service: 'chat-streaming',
    };
  }
}
