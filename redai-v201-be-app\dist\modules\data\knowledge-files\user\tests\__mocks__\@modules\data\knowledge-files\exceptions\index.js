"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.KNOWLEDGE_FILE_ERROR_CODES = void 0;
const common_1 = require("@nestjs/common");
const app_exception_1 = require("../../../../../../../../../../common/exceptions/app.exception");
exports.KNOWLEDGE_FILE_ERROR_CODES = {
    KNOWLEDGE_FILE_CREATE_ERROR: new app_exception_1.ErrorCode(20101, 'Lỗi khi tạo file tri thức', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    KNOWLEDGE_FILE_LIST_ERROR: new app_exception_1.ErrorCode(20102, 'Lỗi khi lấy danh sách file tri thức', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    KNOWLEDGE_FILE_DELETE_ERROR: new app_exception_1.ErrorCode(20103, 'Lỗi khi xóa file tri thức', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    KNOWLEDGE_FILE_NOT_FOUND: new app_exception_1.ErrorCode(20104, 'Không tìm thấy file tri thức', common_1.HttpStatus.NOT_FOUND),
    VECTOR_STORE_CREATE_ERROR: new app_exception_1.ErrorCode(20201, 'Lỗi khi tạo vector store', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    VECTOR_STORE_LIST_ERROR: new app_exception_1.ErrorCode(20202, 'Lỗi khi lấy danh sách vector store', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    VECTOR_STORE_DETAIL_ERROR: new app_exception_1.ErrorCode(20203, 'Lỗi khi lấy chi tiết vector store', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    VECTOR_STORE_DELETE_ERROR: new app_exception_1.ErrorCode(20204, 'Lỗi khi xóa vector store', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    VECTOR_STORE_NOT_FOUND: new app_exception_1.ErrorCode(20205, 'Không tìm thấy vector store', common_1.HttpStatus.NOT_FOUND),
    VECTOR_STORE_ASSIGN_FILES_ERROR: new app_exception_1.ErrorCode(20206, 'Lỗi khi gán file vào vector store', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    VECTOR_STORE_REMOVE_FILES_ERROR: new app_exception_1.ErrorCode(20207, 'Lỗi khi xóa file khỏi vector store', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
};
//# sourceMappingURL=index.js.map