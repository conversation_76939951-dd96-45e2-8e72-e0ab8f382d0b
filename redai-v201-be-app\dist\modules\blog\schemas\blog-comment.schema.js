"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlogCommentListResponseSchema = exports.BlogCommentSchema = void 0;
const swagger_1 = require("@nestjs/swagger");
const enums_1 = require("../enums");
class BlogCommentSchema {
    id;
    blogId;
    userId;
    createdAt;
    content;
    authorType;
    employeeId;
    parentCommentId;
    replies;
    constructor(partial) {
        Object.assign(this, partial);
    }
}
exports.BlogCommentSchema = BlogCommentSchema;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của bình luận',
        example: 1,
    }),
    __metadata("design:type", Number)
], BlogCommentSchema.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của bài viết',
        example: 1,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogCommentSchema.prototype, "blogId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của người dùng bình luận',
        example: 1,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogCommentSchema.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo bình luận (Unix timestamp)',
        example: 1625097600000,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogCommentSchema.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nội dung bình luận',
        example: 'Bài viết rất hay và bổ ích!',
        nullable: true,
    }),
    __metadata("design:type", String)
], BlogCommentSchema.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại tài khoản bình luận',
        example: enums_1.AuthorTypeEnum.USER,
        enum: enums_1.AuthorTypeEnum,
        nullable: true,
    }),
    __metadata("design:type", String)
], BlogCommentSchema.prototype, "authorType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của nhân viên nếu là bình luận của hệ thống',
        example: 1,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogCommentSchema.prototype, "employeeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của bình luận cha nếu đây là bình luận phản hồi',
        example: 1,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogCommentSchema.prototype, "parentCommentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách các bình luận phản hồi',
        type: [BlogCommentSchema],
        nullable: true,
    }),
    __metadata("design:type", Array)
], BlogCommentSchema.prototype, "replies", void 0);
class BlogCommentListResponseSchema {
    items;
    meta;
}
exports.BlogCommentListResponseSchema = BlogCommentListResponseSchema;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách bình luận',
        type: [BlogCommentSchema],
    }),
    __metadata("design:type", Array)
], BlogCommentListResponseSchema.prototype, "items", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin phân trang',
        type: 'object',
        properties: {
            totalItems: {
                type: 'number',
                example: 100,
                description: 'Tổng số bình luận',
            },
            itemCount: {
                type: 'number',
                example: 10,
                description: 'Số bình luận trên trang hiện tại',
            },
            itemsPerPage: {
                type: 'number',
                example: 10,
                description: 'Số bình luận trên mỗi trang',
            },
            totalPages: {
                type: 'number',
                example: 10,
                description: 'Tổng số trang',
            },
            currentPage: {
                type: 'number',
                example: 1,
                description: 'Trang hiện tại',
            },
        },
    }),
    __metadata("design:type", Object)
], BlogCommentListResponseSchema.prototype, "meta", void 0);
//# sourceMappingURL=blog-comment.schema.js.map