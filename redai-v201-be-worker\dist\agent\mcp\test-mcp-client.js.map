{"version": 3, "sources": ["../../../src/agent/mcp/test-mcp-client.ts"], "sourcesContent": ["import { Client } from '@modelcontextprotocol/sdk/client/index.js';\r\nimport * as crypto from 'crypto';\r\nimport { Logger } from '@nestjs/common';\r\nimport { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';\r\nimport { env } from '../../config';\r\n\r\nconst apiSecretKey = env.agent.API_SECRET_KEY;\r\nconst apiPrefixKey = env.agent.API_PREFIX_KEY;\r\n\r\nconst logger = new Logger('TestMCPClient');\r\nconst mcpUserClient = new Client(\r\n  {\r\n    name: 'swagger',\r\n    version: '1.0.0',\r\n  },\r\n  {\r\n    capabilities: {},\r\n  },\r\n);\r\n\r\n/**\r\n * Tạo key từ secret key\r\n * @returns Buffer chứa key\r\n */\r\nfunction generateKey() {\r\n  return crypto.createHash('sha256').update(apiSecretKey).digest();\r\n}\r\n\r\n/**\r\n * Tạo iv (initialization vector) ngẫu nhiên\r\n * @returns Buffer chứa iv\r\n */\r\nfunction generateIv() {\r\n  return crypto.randomBytes(16);\r\n}\r\n\r\n/**\r\n * Tạo API Key từ Agent ID và User ID\r\n * @param agentId ID của worker\r\n * @param userId ID của user\r\n * @returns API Key\r\n */\r\nfunction generateApiKey(\r\n  agentId = 'fb838815-f1dd-4e57-83e6-bb635b39e236',\r\n  userId = 1,\r\n) {\r\n  try {\r\n    const data = JSON.stringify({ agentId, userId });\r\n    const key = generateKey();\r\n    const iv = generateIv();\r\n    const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);\r\n    let encrypted = cipher.update(data, 'utf8', 'base64');\r\n    encrypted += cipher.final('base64');\r\n    const result = iv.toString('base64') + ':' + encrypted;\r\n    return `${apiPrefixKey}_${result}`;\r\n  } catch (error) {\r\n    throw new Error('Không thể tạo API Key');\r\n  }\r\n}\r\n\r\n// Các hàm tạo API key giữ nguyên...\r\n\r\nasync function createMcpUserClient() {\r\n  try {\r\n    const apiKey = generateApiKey('fb838815-f1dd-4e57-83e6-bb635b39e236', 1);\r\n    logger.log('API Key:', apiKey);\r\n\r\n    // Tạo URL cho kết nối SSE và messages\r\n    const sseUrl = new URL(\r\n      `http://${env.agent.MCP_HOST}:${env.agent.MCP_PORT}/sse`,\r\n    );\r\n\r\n    // Tùy chọn cho SSEClientTransport\r\n    const transportOptions = {\r\n      // Tùy chỉnh request SSE ban đầu\r\n      eventSourceInit: {\r\n        withCredentials: false, // Không gửi credentials\r\n        headers: {\r\n          Accept: 'text/event-stream',\r\n          'Cache-Control': 'no-cache',\r\n          Connection: 'keep-alive',\r\n        },\r\n      },\r\n\r\n      // Tùy chỉnh các POST requests\r\n      requestInit: {\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n      },\r\n    };\r\n\r\n    // Tạo transport với cả hai URL\r\n    const transport = new StdioClientTransport({\r\n      command: 'npx',\r\n      args: ['-y', 'tavily-mcp@0.1.2'],\r\n      env: {\r\n        TAVILY_API_KEY: process.env.TAVILY_API_KEY as string,\r\n      },\r\n    });\r\n\r\n    // Xử lý sự kiện đóng kết nối\r\n    transport.onclose = () => {\r\n      logger.log('Kết nối SSE đã đóng');\r\n    };\r\n\r\n    // Xử lý lỗi\r\n    transport.onerror = (error) => {\r\n      console.error('Lỗi kết nối SSE:', error);\r\n    };\r\n\r\n    // Xử lý tin nhắn nhận được\r\n    transport.onmessage = (message) => {\r\n      logger.log('Nhận tin nhắn từ server:', message);\r\n    };\r\n\r\n    // KẾT NỐI CLIENT VỚI TRANSPORT\r\n    logger.log('Đang kết nối đến server...');\r\n    await mcpUserClient.connect(transport);\r\n    logger.log('Đã kết nối thành công!');\r\n  } catch (error) {\r\n    console.error('Lỗi:', error);\r\n  }\r\n}\r\n\r\nexport { createMcpUserClient, mcpUserClient };\r\n"], "names": ["createMcpUserClient", "mcpUserClient", "apiSecret<PERSON>ey", "env", "agent", "API_SECRET_KEY", "apiPrefixKey", "API_PREFIX_KEY", "logger", "<PERSON><PERSON>", "Client", "name", "version", "capabilities", "<PERSON><PERSON>ey", "crypto", "createHash", "update", "digest", "generateIv", "randomBytes", "generateApiKey", "agentId", "userId", "data", "JSON", "stringify", "key", "iv", "cipher", "createCipheriv", "encrypted", "final", "result", "toString", "error", "Error", "<PERSON><PERSON><PERSON><PERSON>", "log", "sseUrl", "URL", "MCP_HOST", "MCP_PORT", "transportOptions", "eventSourceInit", "withCredentials", "headers", "Accept", "Connection", "requestInit", "transport", "StdioClientTransport", "command", "args", "TAVILY_API_KEY", "process", "onclose", "onerror", "console", "onmessage", "message", "connect"], "mappings": ";;;;;;;;;;;QA6HSA;eAAAA;;QAAqBC;eAAAA;;;uBA7HP;gEACC;wBACD;uBACc;wBACjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpB,MAAMC,eAAeC,WAAG,CAACC,KAAK,CAACC,cAAc;AAC7C,MAAMC,eAAeH,WAAG,CAACC,KAAK,CAACG,cAAc;AAE7C,MAAMC,SAAS,IAAIC,cAAM,CAAC;AAC1B,MAAMR,gBAAgB,IAAIS,aAAM,CAC9B;IACEC,MAAM;IACNC,SAAS;AACX,GACA;IACEC,cAAc,CAAC;AACjB;AAGF;;;CAGC,GACD,SAASC;IACP,OAAOC,QAAOC,UAAU,CAAC,UAAUC,MAAM,CAACf,cAAcgB,MAAM;AAChE;AAEA;;;CAGC,GACD,SAASC;IACP,OAAOJ,QAAOK,WAAW,CAAC;AAC5B;AAEA;;;;;CAKC,GACD,SAASC,eACPC,UAAU,sCAAsC,EAChDC,SAAS,CAAC;IAEV,IAAI;QACF,MAAMC,OAAOC,KAAKC,SAAS,CAAC;YAAEJ;YAASC;QAAO;QAC9C,MAAMI,MAAMb;QACZ,MAAMc,KAAKT;QACX,MAAMU,SAASd,QAAOe,cAAc,CAAC,eAAeH,KAAKC;QACzD,IAAIG,YAAYF,OAAOZ,MAAM,CAACO,MAAM,QAAQ;QAC5CO,aAAaF,OAAOG,KAAK,CAAC;QAC1B,MAAMC,SAASL,GAAGM,QAAQ,CAAC,YAAY,MAAMH;QAC7C,OAAO,GAAGzB,aAAa,CAAC,EAAE2B,QAAQ;IACpC,EAAE,OAAOE,OAAO;QACd,MAAM,IAAIC,MAAM;IAClB;AACF;AAEA,oCAAoC;AAEpC,eAAepC;IACb,IAAI;QACF,MAAMqC,SAAShB,eAAe,wCAAwC;QACtEb,OAAO8B,GAAG,CAAC,YAAYD;QAEvB,sCAAsC;QACtC,MAAME,SAAS,IAAIC,IACjB,CAAC,OAAO,EAAErC,WAAG,CAACC,KAAK,CAACqC,QAAQ,CAAC,CAAC,EAAEtC,WAAG,CAACC,KAAK,CAACsC,QAAQ,CAAC,IAAI,CAAC;QAG1D,kCAAkC;QAClC,MAAMC,mBAAmB;YACvB,gCAAgC;YAChCC,iBAAiB;gBACfC,iBAAiB;gBACjBC,SAAS;oBACPC,QAAQ;oBACR,iBAAiB;oBACjBC,YAAY;gBACd;YACF;YAEA,8BAA8B;YAC9BC,aAAa;gBACXH,SAAS;oBACP,gBAAgB;gBAClB;YACF;QACF;QAEA,+BAA+B;QAC/B,MAAMI,YAAY,IAAIC,2BAAoB,CAAC;YACzCC,SAAS;YACTC,MAAM;gBAAC;gBAAM;aAAmB;YAChClD,KAAK;gBACHmD,gBAAgBC,QAAQpD,GAAG,CAACmD,cAAc;YAC5C;QACF;QAEA,6BAA6B;QAC7BJ,UAAUM,OAAO,GAAG;YAClBhD,OAAO8B,GAAG,CAAC;QACb;QAEA,YAAY;QACZY,UAAUO,OAAO,GAAG,CAACtB;YACnBuB,QAAQvB,KAAK,CAAC,oBAAoBA;QACpC;QAEA,2BAA2B;QAC3Be,UAAUS,SAAS,GAAG,CAACC;YACrBpD,OAAO8B,GAAG,CAAC,4BAA4BsB;QACzC;QAEA,+BAA+B;QAC/BpD,OAAO8B,GAAG,CAAC;QACX,MAAMrC,cAAc4D,OAAO,CAACX;QAC5B1C,OAAO8B,GAAG,CAAC;IACb,EAAE,OAAOH,OAAO;QACduB,QAAQvB,KAAK,CAAC,QAAQA;IACxB;AACF"}