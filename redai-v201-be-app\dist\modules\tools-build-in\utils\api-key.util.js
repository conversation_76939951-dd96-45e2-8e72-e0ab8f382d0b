"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ApiKeyUtil_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiKeyUtil = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const crypto = require("crypto");
let ApiKeyUtil = ApiKeyUtil_1 = class ApiKeyUtil {
    configService;
    logger = new common_1.Logger(ApiKeyUtil_1.name);
    apiPrefixKey;
    apiSecretKey;
    algorithm = 'aes-256-cbc';
    constructor(configService) {
        this.configService = configService;
        this.apiPrefixKey = this.configService.get('API_PREFIX_KEY') || 'redai';
        this.apiSecretKey = this.configService.get('API_SERECT_KEY') || '';
        if (!this.apiSecretKey) {
            this.logger.warn('API_SERECT_KEY không được cấu hình. Xác thực API Key sẽ không hoạt động đúng.');
        }
    }
    generateApiKey(agentId, userId) {
        try {
            const data = JSON.stringify({ agentId, userId });
            const key = this.generateKey();
            const iv = this.generateIv();
            const cipher = crypto.createCipheriv(this.algorithm, key, iv);
            let encrypted = cipher.update(data, 'utf8', 'base64');
            encrypted += cipher.final('base64');
            const result = iv.toString('base64') + ':' + encrypted;
            return `${this.apiPrefixKey}_${result}`;
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo API Key: ${error.message}`, error.stack);
            throw new Error('Không thể tạo API Key');
        }
    }
    decodeApiKey(apiKey) {
        try {
            if (!apiKey || !apiKey.startsWith(`${this.apiPrefixKey}_`)) {
                return null;
            }
            const encryptedPart = apiKey.substring(this.apiPrefixKey.length + 1);
            const parts = encryptedPart.split(':');
            if (parts.length !== 2) {
                return null;
            }
            const iv = Buffer.from(parts[0], 'base64');
            const encrypted = parts[1];
            const key = this.generateKey();
            const decipher = crypto.createDecipheriv(this.algorithm, key, iv);
            let decrypted = decipher.update(encrypted, 'base64', 'utf8');
            decrypted += decipher.final('utf8');
            const data = JSON.parse(decrypted);
            return {
                agentId: data.agentId,
                userId: data.userId,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi giải mã API Key: ${error.message}`, error.stack);
            return null;
        }
    }
    generateKey() {
        return crypto.createHash('sha256').update(this.apiSecretKey).digest();
    }
    generateIv() {
        return crypto.randomBytes(16);
    }
};
exports.ApiKeyUtil = ApiKeyUtil;
exports.ApiKeyUtil = ApiKeyUtil = ApiKeyUtil_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], ApiKeyUtil);
//# sourceMappingURL=api-key.util.js.map