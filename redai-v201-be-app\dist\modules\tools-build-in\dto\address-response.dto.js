"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddressSearchResponseDto = exports.ShippingResponseDto = exports.AddressResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class AddressResponseDto {
    id;
    contactName;
    phone;
    addressLine;
    ward;
    district;
    province;
    postalCode;
    addressType;
    notes;
    isDefault;
}
exports.AddressResponseDto = AddressResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của địa chỉ',
        example: 1,
        nullable: true
    }),
    __metadata("design:type", Number)
], AddressResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên người nhận/liên hệ',
        example: 'Nguyễn Văn A'
    }),
    __metadata("design:type", String)
], AddressResponseDto.prototype, "contactName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số điện thoại liên hệ',
        example: '0987654321'
    }),
    __metadata("design:type", String)
], AddressResponseDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Địa chỉ chi tiết',
        example: 'Số 123, Đường ABC'
    }),
    __metadata("design:type", String)
], AddressResponseDto.prototype, "addressLine", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Phường/Xã',
        example: 'Phường XYZ',
        nullable: true
    }),
    __metadata("design:type", String)
], AddressResponseDto.prototype, "ward", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Quận/Huyện',
        example: 'Quận 1'
    }),
    __metadata("design:type", String)
], AddressResponseDto.prototype, "district", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tỉnh/Thành phố',
        example: 'TP. Hồ Chí Minh'
    }),
    __metadata("design:type", String)
], AddressResponseDto.prototype, "province", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mã bưu chính',
        example: '700000',
        nullable: true
    }),
    __metadata("design:type", String)
], AddressResponseDto.prototype, "postalCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại địa chỉ',
        example: 'home',
        nullable: true
    }),
    __metadata("design:type", String)
], AddressResponseDto.prototype, "addressType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Ghi chú thêm',
        example: 'Giao hàng giờ hành chính',
        nullable: true
    }),
    __metadata("design:type", String)
], AddressResponseDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Có phải địa chỉ mặc định không',
        example: true,
        nullable: true
    }),
    __metadata("design:type", Boolean)
], AddressResponseDto.prototype, "isDefault", void 0);
class ShippingResponseDto {
    pickupAddress;
    deliveryAddress;
    shippingMethod;
    shippingFee;
    estimatedDeliveryTime;
    trackingCode;
}
exports.ShippingResponseDto = ShippingResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Địa chỉ lấy hàng',
        type: AddressResponseDto
    }),
    __metadata("design:type", AddressResponseDto)
], ShippingResponseDto.prototype, "pickupAddress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Địa chỉ giao hàng',
        type: AddressResponseDto
    }),
    __metadata("design:type", AddressResponseDto)
], ShippingResponseDto.prototype, "deliveryAddress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Phương thức vận chuyển',
        example: 'GHTK'
    }),
    __metadata("design:type", String)
], ShippingResponseDto.prototype, "shippingMethod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Phí vận chuyển',
        example: 25000
    }),
    __metadata("design:type", Number)
], ShippingResponseDto.prototype, "shippingFee", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian dự kiến giao hàng',
        example: '2023-12-31T12:00:00Z',
        nullable: true
    }),
    __metadata("design:type", String)
], ShippingResponseDto.prototype, "estimatedDeliveryTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mã đơn vận chuyển',
        example: 'GHTK123456789',
        nullable: true
    }),
    __metadata("design:type", String)
], ShippingResponseDto.prototype, "trackingCode", void 0);
class AddressSearchResponseDto {
    addresses;
    total;
    keyword;
}
exports.AddressSearchResponseDto = AddressSearchResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách các địa chỉ tìm thấy',
        type: [AddressResponseDto]
    }),
    __metadata("design:type", Array)
], AddressSearchResponseDto.prototype, "addresses", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tổng số kết quả',
        example: 10
    }),
    __metadata("design:type", Number)
], AddressSearchResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Từ khóa tìm kiếm',
        example: 'Hồ Chí Minh'
    }),
    __metadata("design:type", String)
], AddressSearchResponseDto.prototype, "keyword", void 0);
//# sourceMappingURL=address-response.dto.js.map