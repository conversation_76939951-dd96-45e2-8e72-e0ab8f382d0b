"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RuleContractRepository_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RuleContractRepository = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const rule_contract_entity_1 = require("../entities/rule-contract.entity");
let RuleContractRepository = RuleContractRepository_1 = class RuleContractRepository extends typeorm_1.Repository {
    dataSource;
    logger = new common_1.Logger(RuleContractRepository_1.name);
    constructor(dataSource) {
        super(rule_contract_entity_1.RuleContract, dataSource.createEntityManager());
        this.dataSource = dataSource;
    }
    async findById(id) {
        return this.findOne({ where: { id } });
    }
    async findByUserId(userId) {
        return this.find({ where: { userId } });
    }
    async findLatestByUserId(userId) {
        return this.findOne({
            where: { userId },
            order: { createdAt: 'DESC' },
        });
    }
    async findWithPaginationForAdmin(queryDto) {
        const { page = 1, limit = 10, search, sortBy = 'createdAt', sortDirection = 'DESC', status, type, } = queryDto;
        const offset = (page - 1) * limit;
        const where = {};
        if (status) {
            where.status = status;
        }
        if (type) {
            where.type = type;
        }
        const [items, totalItems] = await this.findAndCount({
            where,
            order: { [sortBy]: sortDirection },
            skip: offset,
            take: limit,
        });
        return {
            items,
            meta: {
                totalItems,
                itemCount: items.length,
                itemsPerPage: limit,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
            },
        };
    }
    async findWithPaginationForUser(userId, queryDto) {
        const { page = 1, limit = 10, search, sortBy = 'createdAt', sortDirection = 'DESC', status, type, } = queryDto;
        const offset = (page - 1) * limit;
        const where = { userId };
        if (status) {
            where.status = status;
        }
        if (type) {
            where.type = type;
        }
        const [items, totalItems] = await this.findAndCount({
            where,
            order: { [sortBy]: sortDirection },
            skip: offset,
            take: limit,
        });
        return {
            items,
            meta: {
                totalItems,
                itemCount: items.length,
                itemsPerPage: limit,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
            },
        };
    }
};
exports.RuleContractRepository = RuleContractRepository;
exports.RuleContractRepository = RuleContractRepository = RuleContractRepository_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], RuleContractRepository);
//# sourceMappingURL=rule-contract.repository.js.map