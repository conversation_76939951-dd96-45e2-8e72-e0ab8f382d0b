{"version": 3, "sources": ["../src/main.ts"], "sourcesContent": ["import { Logger } from '@nestjs/common';\r\nimport { NestFactory } from '@nestjs/core';\r\nimport { AppModule } from './app.module';\r\nimport { MicroserviceOptions, Transport } from '@nestjs/microservices';\r\nconst logger = new Logger('Bootstrap');\r\n\r\nasync function bootstrap() {\r\n  const app = await NestFactory.create(AppModule);\r\n  if (!process.env.REDIS_URL) {\r\n    logger.error('REDIS_URL environment variable is not set.');\r\n    process.exit(1);\r\n  }\r\n  const redisUrl = new URL(process.env.REDIS_URL);\r\n\r\n  // Configure the microservice options for Redis\r\n  app.connectMicroservice<MicroserviceOptions>({\r\n    transport: Transport.REDIS,\r\n    options: {\r\n      host: redisUrl.hostname,\r\n      port: parseInt(redisUrl.port, 10),\r\n      password: redisUrl.password,\r\n      retryAttempts: 5,\r\n      retryDelay: 2000,\r\n      tls: redisUrl.protocol === 'rediss:' ? { rejectUnauthorized: false } : undefined,\r\n    },\r\n  });\r\n  // Start the microservice\r\n  await app.startAllMicroservices();\r\n  const port = process.env.PORT ?? 3000;\r\n  await app.listen(port);\r\n  logger.log(`Application is running on: http://localhost:${port}`);\r\n  logger.log(\r\n    `Swagger documentation is available at: http://localhost:${port}/api/docs`,\r\n  );\r\n}\r\n\r\nbootstrap().catch((err) => {\r\n  logger.error('Failed to start application', err);\r\n  process.exit(1);\r\n});\r\n"], "names": ["logger", "<PERSON><PERSON>", "bootstrap", "app", "NestFactory", "create", "AppModule", "process", "env", "REDIS_URL", "error", "exit", "redisUrl", "URL", "connectMicroservice", "transport", "Transport", "REDIS", "options", "host", "hostname", "port", "parseInt", "password", "retryAttempts", "retry<PERSON><PERSON><PERSON>", "tls", "protocol", "rejectUnauthorized", "undefined", "startAllMicroservices", "PORT", "listen", "log", "catch", "err"], "mappings": ";;;;wBAAuB;sBACK;2BACF;+BACqB;AAC/C,MAAMA,SAAS,IAAIC,cAAM,CAAC;AAE1B,eAAeC;IACb,MAAMC,MAAM,MAAMC,iBAAW,CAACC,MAAM,CAACC,oBAAS;IAC9C,IAAI,CAACC,QAAQC,GAAG,CAACC,SAAS,EAAE;QAC1BT,OAAOU,KAAK,CAAC;QACbH,QAAQI,IAAI,CAAC;IACf;IACA,MAAMC,WAAW,IAAIC,IAAIN,QAAQC,GAAG,CAACC,SAAS;IAE9C,+CAA+C;IAC/CN,IAAIW,mBAAmB,CAAsB;QAC3CC,WAAWC,wBAAS,CAACC,KAAK;QAC1BC,SAAS;YACPC,MAAMP,SAASQ,QAAQ;YACvBC,MAAMC,SAASV,SAASS,IAAI,EAAE;YAC9BE,UAAUX,SAASW,QAAQ;YAC3BC,eAAe;YACfC,YAAY;YACZC,KAAKd,SAASe,QAAQ,KAAK,YAAY;gBAAEC,oBAAoB;YAAM,IAAIC;QACzE;IACF;IACA,yBAAyB;IACzB,MAAM1B,IAAI2B,qBAAqB;IAC/B,MAAMT,OAAOd,QAAQC,GAAG,CAACuB,IAAI,IAAI;IACjC,MAAM5B,IAAI6B,MAAM,CAACX;IACjBrB,OAAOiC,GAAG,CAAC,CAAC,4CAA4C,EAAEZ,MAAM;IAChErB,OAAOiC,GAAG,CACR,CAAC,wDAAwD,EAAEZ,KAAK,SAAS,CAAC;AAE9E;AAEAnB,YAAYgC,KAAK,CAAC,CAACC;IACjBnC,OAAOU,KAAK,CAAC,+BAA+ByB;IAC5C5B,QAAQI,IAAI,CAAC;AACf"}