"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericPage = void 0;
const typeorm_1 = require("typeorm");
const generic_page_enum_1 = require("../constants/generic-page.enum");
let GenericPage = class GenericPage {
    id;
    name;
    description;
    path;
    config;
    status;
    createdAt;
    updatedAt;
    publishedAt;
    createdBy;
    updatedBy;
};
exports.GenericPage = GenericPage;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], GenericPage.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], GenericPage.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], GenericPage.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, unique: true }),
    __metadata("design:type", String)
], GenericPage.prototype, "path", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Object)
], GenericPage.prototype, "config", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: generic_page_enum_1.GenericPageStatusEnum,
        default: generic_page_enum_1.GenericPageStatusEnum.DRAFT,
    }),
    __metadata("design:type", String)
], GenericPage.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_at', type: 'bigint' }),
    __metadata("design:type", Number)
], GenericPage.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_at', type: 'bigint' }),
    __metadata("design:type", Number)
], GenericPage.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'published_at', type: 'bigint', nullable: true }),
    __metadata("design:type", Object)
], GenericPage.prototype, "publishedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', length: 36 }),
    __metadata("design:type", String)
], GenericPage.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', length: 36 }),
    __metadata("design:type", String)
], GenericPage.prototype, "updatedBy", void 0);
exports.GenericPage = GenericPage = __decorate([
    (0, typeorm_1.Entity)('generic_pages')
], GenericPage);
//# sourceMappingURL=generic-page.entity.js.map