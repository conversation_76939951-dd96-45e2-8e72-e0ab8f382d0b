{"version": 3, "file": "user-key-llm.service.js", "sourceRoot": "", "sources": ["../../../../../src/modules/models/user/services/user-key-llm.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,kGAAmF;AACnF,8DAAkD;AAClD,0DAAmE;AACnE,2CAAoD;AACpD,iEAAsD;AACtD,iDAAsD;AACtD,uFAAiF;AACjF,wFAAkF;AAClF,oFAA8E;AAE9E,sDAS6B;AAC7B,wEAAkE;AAM3D,IAAM,iBAAiB,yBAAvB,MAAM,iBAAiB;IAIT;IACA;IACA;IACA;IANF,MAAM,GAAG,IAAI,eAAM,CAAC,mBAAiB,CAAC,IAAI,CAAC,CAAC;IAE7D,YACmB,oBAA0C,EAC1C,sBAA8C,EAC9C,iBAAmC,EACnC,oBAA0C;QAH1C,yBAAoB,GAApB,oBAAoB,CAAsB;QAC1C,2BAAsB,GAAtB,sBAAsB,CAAwB;QAC9C,sBAAiB,GAAjB,iBAAiB,CAAkB;QACnC,yBAAoB,GAApB,oBAAoB,CAAsB;IACzD,CAAC;IAMC,AAAN,KAAK,CAAC,MAAM,CAAC,MAAc,EAAE,SAA8B;QACzD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kCAAkC,MAAM,EAAE,CAAC,CAAC;QAG5D,IAAI,CAAC,sCAAgB,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,yBAAY,CAAC,+BAAkB,CAAC,yBAAyB,CAAC,CAAC;QACvE,CAAC;QAGD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACnG,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,IAAI,yBAAY,CAAC,+BAAkB,CAAC,wBAAwB,CAAC,CAAC;QACtE,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAGhG,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAC5D,eAAe,EACf,SAAS,CAAC,QAAQ,EAClB,KAAK,EACL,MAAM,CACP,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YACxB,MAAM,IAAI,yBAAY,CAAC,+BAAkB,CAAC,8BAA8B,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;QAC9F,CAAC;QAGD,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;YAClD,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,MAAM,EAAE,eAAe;YACvB,MAAM;YACN,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC,CAAC;QAGH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAGzE,IAAI,cAA8C,CAAC;QACnD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yCAAyC,eAAe,CAAC,EAAE,EAAE,CAAC,CAAC;YAE/E,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC;gBAC5D,KAAK,EAAE,eAAe,CAAC,EAAE;gBACzB,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,eAAe,EAAE,eAAe;gBAChC,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;YAEH,cAAc,GAAG;gBACf,gBAAgB,EAAE,UAAU,CAAC,eAAe,CAAC,gBAAgB;gBAC7D,aAAa,EAAE,UAAU,CAAC,eAAe,CAAC,aAAa;gBACvD,gBAAgB,EAAE,UAAU,CAAC,eAAe,CAAC,gBAAgB;gBAC7D,mBAAmB,EAAE,UAAU,CAAC,eAAe,CAAC,mBAAmB;gBACnE,eAAe,EAAE,UAAU,CAAC,eAAe,CAAC,eAAe;gBAC3D,aAAa,EAAE,UAAU,CAAC,QAAQ;gBAClC,OAAO,EAAE,UAAU,CAAC,OAAO;gBAC3B,OAAO,EAAE,UAAU,CAAC,OAAO;gBAC3B,MAAM,EAAE,UAAU,CAAC,eAAe,CAAC,MAAM;aAC1C,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,eAAe,CAAC,EAAE,KAAK,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;QACzG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,eAAe,CAAC,EAAE,KAAK,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAE9G,cAAc,GAAG;gBACf,gBAAgB,EAAE,CAAC;gBACnB,aAAa,EAAE,CAAC;gBAChB,gBAAgB,EAAE,CAAC;gBACnB,mBAAmB,EAAE,CAAC;gBACtB,eAAe,EAAE,CAAC;gBAClB,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE;gBACzB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,6BAA6B,KAAK,CAAC,OAAO,EAAE;gBACrD,MAAM,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC;aACxB,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,UAAU,CAAC,EAAE,eAAe,CAAC,CAAC;QAEtE,MAAM,QAAQ,GAAgC;YAC5C,EAAE,EAAE,eAAe,CAAC,EAAE;YACtB,eAAe,EAAE,UAAU,CAAC,KAAK;YACjC,cAAc;SACf,CAAC;QAEF,OAAO,yBAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAKD,KAAK,CAAC,OAAO,CAAC,MAAc,EAAE,QAA4B;QACxD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sCAAsC,MAAM,EAAE,CAAC,CAAC;QAGhE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAGpF,MAAM,KAAK,GAAG,sCAAgB,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAEhE,OAAO,yBAAc,CAAC,SAAS,CAAC;YAC9B,KAAK;YACL,IAAI,EAAE,MAAM,CAAC,IAAI;SAClB,CAAC,CAAC;IACL,CAAC;IAMK,AAAN,KAAK,CAAC,MAAM,CAAC,MAAc,EAAE,EAAU,EAAE,SAA8B;QACrE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,EAAE,aAAa,MAAM,EAAE,CAAC,CAAC;QAGlE,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QACzF,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxB,MAAM,IAAI,yBAAY,CAAC,+BAAkB,CAAC,sBAAsB,CAAC,CAAC;QACpE,CAAC;QAGD,IAAI,SAAS,CAAC,IAAI,IAAI,CAAC,sCAAgB,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;YACxE,MAAM,IAAI,yBAAY,CAAC,+BAAkB,CAAC,yBAAyB,CAAC,CAAC;QACvE,CAAC;QAGD,IAAI,SAAS,CAAC,IAAI,IAAI,SAAS,CAAC,IAAI,KAAK,kBAAkB,CAAC,IAAI,EAAE,CAAC;YACjE,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;YACvG,IAAI,YAAY,EAAE,CAAC;gBACjB,MAAM,IAAI,yBAAY,CAAC,+BAAkB,CAAC,wBAAwB,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;QAED,IAAI,eAAmC,CAAC;QACxC,IAAI,eAAmC,CAAC;QACxC,IAAI,eAAe,GAAG,KAAK,CAAC;QAE5B,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;YACrB,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC1F,eAAe,GAAG,IAAI,CAAC;YAGvB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAC5D,eAAe,EACf,kBAAkB,CAAC,QAAQ,EAC3B,KAAK,EACL,MAAM,CACP,CAAC;YAEF,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBACxB,MAAM,IAAI,yBAAY,CAAC,+BAAkB,CAAC,8BAA8B,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;YAC9F,CAAC;YAED,eAAe,GAAG,UAAU,CAAC,KAAK,CAAC;QACrC,CAAC;QAGD,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACjC,kBAAkB,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC;QAC3C,CAAC;QAED,IAAI,SAAS,CAAC,MAAM,KAAK,SAAS,IAAI,eAAe,EAAE,CAAC;YACtD,kBAAkB,CAAC,MAAM,GAAG,eAAe,CAAC;QAC9C,CAAC;QAED,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAG1C,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAGzD,IAAI,cAA8C,CAAC;QACnD,IAAI,eAAe,IAAI,eAAe,EAAE,CAAC;YACvC,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iDAAiD,EAAE,EAAE,CAAC,CAAC;gBAEvE,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC;oBAC5D,KAAK,EAAE,EAAE;oBACT,QAAQ,EAAE,kBAAkB,CAAC,QAAQ;oBACrC,eAAe,EAAE,eAAe;oBAChC,MAAM,EAAE,MAAM;iBACf,CAAC,CAAC;gBAEH,cAAc,GAAG;oBACf,gBAAgB,EAAE,UAAU,CAAC,eAAe,CAAC,gBAAgB;oBAC7D,aAAa,EAAE,UAAU,CAAC,eAAe,CAAC,aAAa;oBACvD,gBAAgB,EAAE,UAAU,CAAC,eAAe,CAAC,gBAAgB;oBAC7D,mBAAmB,EAAE,UAAU,CAAC,eAAe,CAAC,mBAAmB;oBACnE,eAAe,EAAE,UAAU,CAAC,eAAe,CAAC,eAAe;oBAC3D,aAAa,EAAE,UAAU,CAAC,QAAQ;oBAClC,OAAO,EAAE,UAAU,CAAC,OAAO;oBAC3B,OAAO,EAAE,UAAU,CAAC,OAAO;oBAC3B,MAAM,EAAE,UAAU,CAAC,eAAe,CAAC,MAAM;iBAC1C,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kDAAkD,EAAE,KAAK,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;YACjG,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;gBAEtG,cAAc,GAAG;oBACf,gBAAgB,EAAE,CAAC;oBACnB,aAAa,EAAE,CAAC;oBAChB,gBAAgB,EAAE,CAAC;oBACnB,mBAAmB,EAAE,CAAC;oBACtB,eAAe,EAAE,CAAC;oBAClB,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE;oBACzB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,6BAA6B,KAAK,CAAC,OAAO,EAAE;oBACrD,MAAM,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC;iBACxB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,EAAE,eAAe,CAAC,CAAC;QAE3D,MAAM,QAAQ,GAAgC;YAC5C,EAAE,EAAE,kBAAkB,CAAC,EAAE;YACzB,eAAe;YACf,cAAc;SACf,CAAC;QAEF,OAAO,yBAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAMK,AAAN,KAAK,CAAC,MAAM,CAAC,MAAc,EAAE,EAAU;QACrC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,EAAE,aAAa,MAAM,EAAE,CAAC,CAAC;QAGlE,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QACzF,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxB,MAAM,IAAI,yBAAY,CAAC,+BAAkB,CAAC,sBAAsB,CAAC,CAAC;QACpE,CAAC;QAGD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wCAAwC,EAAE,EAAE,CAAC,CAAC;YAC9D,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YAC5E,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,WAAW,CAAC,eAAe,gCAAgC,EAAE,EAAE,CAAC,CAAC;QAC9F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QAExG,CAAC;QAGD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QACjF,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,yBAAY,CAAC,+BAAkB,CAAC,0BAA0B,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6BAA6B,EAAE,eAAe,CAAC,CAAC;QAChE,OAAO,yBAAc,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC,CAAC;IAC5E,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,MAAc,EAAE,KAAa;QAC9C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,KAAK,aAAa,MAAM,EAAE,CAAC,CAAC;QAE7E,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAE/E,MAAM,QAAQ,GAA4B;gBACxC,KAAK,EAAE,UAAU,CAAC,KAAK;gBACvB,QAAQ,EAAE,UAAU,CAAC,QAAQ;gBAC7B,MAAM,EAAE,UAAU,CAAC,MAAM;gBACzB,cAAc,EAAE;oBACd,gBAAgB,EAAE,UAAU,CAAC,eAAe,CAAC,gBAAgB;oBAC7D,aAAa,EAAE,UAAU,CAAC,eAAe,CAAC,aAAa;oBACvD,gBAAgB,EAAE,UAAU,CAAC,eAAe,CAAC,gBAAgB;oBAC7D,mBAAmB,EAAE,UAAU,CAAC,eAAe,CAAC,mBAAmB;oBACnE,eAAe,EAAE,UAAU,CAAC,eAAe,CAAC,eAAe;oBAC3D,aAAa,EAAE,UAAU,CAAC,QAAQ;oBAClC,OAAO,EAAE,UAAU,CAAC,OAAO;oBAC3B,OAAO,EAAE,UAAU,CAAC,OAAO;oBAC3B,MAAM,EAAE,UAAU,CAAC,eAAe,CAAC,MAAM;iBAC1C;aACF,CAAC;YAEF,OAAO,yBAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,KAAK,KAAK,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAClG,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AAnTY,8CAAiB;AActB;IADL,IAAA,qCAAa,GAAE;;6CACwB,kCAAmB;;+CA2F1D;AAwBK;IADL,IAAA,qCAAa,GAAE;;qDACoC,kCAAmB;;+CA+GtE;AAMK;IADL,IAAA,qCAAa,GAAE;;;;+CA4Bf;4BAjRU,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;qCAK8B,8CAAoB;QAClB,kDAAsB;QAC3B,qCAAgB;QACb,8CAAoB;GAPlD,iBAAiB,CAmT7B"}