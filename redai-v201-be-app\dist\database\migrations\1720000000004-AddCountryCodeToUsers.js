"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddCountryCodeToUsers1720000000004 = void 0;
class AddCountryCodeToUsers1720000000004 {
    async up(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE "users" 
      ADD COLUMN IF NOT EXISTS "country_code" VARCHAR(10) DEFAULT '+84'
    `);
        await queryRunner.query(`
      COMMENT ON COLUMN "users"."country_code" 
      IS 'Mã quốc gia của số điện thoại người dùng (ví dụ: +84, +1, +86)'
    `);
        await queryRunner.query(`
      UPDATE "users" 
      SET "country_code" = '+84' 
      WHERE "country_code" IS NULL
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE "users" 
      DROP COLUMN IF EXISTS "country_code"
    `);
    }
}
exports.AddCountryCodeToUsers1720000000004 = AddCountryCodeToUsers1720000000004;
//# sourceMappingURL=1720000000004-AddCountryCodeToUsers.js.map