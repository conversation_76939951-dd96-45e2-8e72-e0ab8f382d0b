"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get createConnectionSchema () {
        return createConnectionSchema;
    },
    get createStdioConnectionSchema () {
        return createStdioConnectionSchema;
    },
    get createStdioRestartSchema () {
        return createStdioRestartSchema;
    },
    get createStreamableHTTPConnectionSchema () {
        return createStreamableHTTPConnectionSchema;
    },
    get createStreamableReconnectSchema () {
        return createStreamableReconnectSchema;
    }
});
const _debug = /*#__PURE__*/ _interop_require_default(require("debug"));
const _zod = require("zod");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// Read package name from package.json
let debugLog;
function getDebugLog() {
    if (!debugLog) {
        debugLog = (0, _debug.default)('@langchain/mcp-adapters:client');
    }
    return debugLog;
}
function createStdioRestartSchema() {
    return _zod.z.object({
        /**
       * Whether to automatically restart the process if it exits
       */ enabled: _zod.z.boolean().describe('Whether to automatically restart the process if it exits').optional(),
        /**
       * Maximum number of restart attempts
       */ maxAttempts: _zod.z.number().describe('The maximum number of restart attempts').optional(),
        /**
       * Delay in milliseconds between restart attempts
       */ delayMs: _zod.z.number().describe('The delay in milliseconds between restart attempts').optional()
    }).describe('Configuration for stdio transport restart');
}
function createStdioConnectionSchema() {
    return _zod.z.object({
        /**
       * Optional transport type, inferred from the structure of the config if not provided. Included
       * for compatibility with common MCP client config file formats.
       */ transport: _zod.z.literal('stdio').optional(),
        /**
       * Optional transport type, inferred from the structure of the config if not provided. Included
       * for compatibility with common MCP client config file formats.
       */ type: _zod.z.literal('stdio').optional(),
        /**
       * The executable to run the server (e.g. `node`, `npx`, etc)
       */ command: _zod.z.string().describe('The executable to run the server'),
        /**
       * Array of command line arguments to pass to the executable
       */ args: _zod.z.array(_zod.z.string()).describe('Command line arguments to pass to the executable'),
        /**
       * Environment variables to set when spawning the process.
       */ env: _zod.z.record(_zod.z.string()).describe('The environment to use when spawning the process').optional(),
        /**
       * The encoding to use when reading from the process
       */ encoding: _zod.z.string().describe('The encoding to use when reading from the process').optional(),
        /**
       * How to handle stderr of the child process. This matches the semantics of Node's `child_process.spawn`
       *
       * The default is "inherit", meaning messages to stderr will be printed to the parent process's stderr.
       *
       * @default "inherit"
       */ stderr: _zod.z.union([
            _zod.z.literal('overlapped'),
            _zod.z.literal('pipe'),
            _zod.z.literal('ignore'),
            _zod.z.literal('inherit')
        ]).describe("How to handle stderr of the child process. This matches the semantics of Node's `child_process.spawn`").optional().default('inherit'),
        /**
       * The working directory to use when spawning the process.
       */ cwd: _zod.z.string().describe('The working directory to use when spawning the process').optional(),
        /**
       * Additional restart settings
       */ restart: createStdioRestartSchema().describe('Settings for automatically restarting the server').optional()
    }).describe('Configuration for stdio transport connection');
}
function createStreamableReconnectSchema() {
    return _zod.z.object({
        /**
       * Whether to automatically reconnect if the connection is lost
       */ enabled: _zod.z.boolean().describe('Whether to automatically reconnect if the connection is lost').optional(),
        /**
       * Maximum number of reconnection attempts
       */ maxAttempts: _zod.z.number().describe('The maximum number of reconnection attempts').optional(),
        /**
       * Delay in milliseconds between reconnection attempts
       */ delayMs: _zod.z.number().describe('The delay in milliseconds between reconnection attempts').optional()
    }).describe('Configuration for streamable HTTP transport reconnection');
}
function createStreamableHTTPConnectionSchema() {
    return _zod.z.object({
        /**
       * Optional transport type, inferred from the structure of the config. If "sse", will not attempt
       * to connect using streamable HTTP.
       */ transport: _zod.z.union([
            _zod.z.literal('http'),
            _zod.z.literal('sse')
        ]).optional(),
        /**
       * Optional transport type, inferred from the structure of the config. If "sse", will not attempt
       * to connect using streamable HTTP.
       */ type: _zod.z.union([
            _zod.z.literal('http'),
            _zod.z.literal('sse')
        ]).optional(),
        /**
       * The URL to connect to
       */ url: _zod.z.string().url(),
        /**
       * Additional headers to send with the request, useful for authentication
       */ headers: _zod.z.record(_zod.z.string()).optional(),
        /**
       * Whether to use Node's EventSource for SSE connections (not applicable to streamable HTTP)
       *
       * @default false
       */ useNodeEventSource: _zod.z.boolean().optional().default(false),
        /**
       * Additional reconnection settings.
       */ reconnect: createStreamableReconnectSchema().optional(),
        /**
       * Whether to automatically fallback to SSE if Streamable HTTP is not available or not supported
       *
       * @default true
       */ automaticSSEFallback: _zod.z.boolean().optional().default(true)
    }).describe('Configuration for streamable HTTP transport connection');
}
function createConnectionSchema() {
    return _zod.z.union([
        createStdioConnectionSchema(),
        createStreamableHTTPConnectionSchema()
    ]).describe('Configuration for a single MCP server');
}

//# sourceMappingURL=client.js.map