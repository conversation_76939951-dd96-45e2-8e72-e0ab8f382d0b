{"version": 3, "sources": ["../../../src/infra/redis/redis.service.ts"], "sourcesContent": ["// src/redis/redis.service.ts\r\nimport { Injectable, OnModuleDestroy } from '@nestjs/common';\r\nimport Redis from 'ioredis';\r\nimport { env } from '../../config';\r\n\r\n@Injectable()\r\nexport class RedisService implements OnModuleDestroy {\r\n  private readonly redis: Redis;\r\n\r\n  constructor() {\r\n    const url = env.external.REDIS_URL || 'redis://localhost:6379';\r\n    this.redis = new Redis(url);\r\n  }\r\n\r\n  async xadd(\r\n    stream: string,\r\n    data: Record<string, string>,\r\n  ): Promise<string | null> {\r\n    return this.redis.xadd(stream, '*', ...Object.entries(data).flat());\r\n  }\r\n\r\n  async xread(stream: string, lastId: string = '$'): Promise<any> {\r\n    return this.redis.xread('BLOCK', 0, 'STREAMS', stream, lastId);\r\n  }\r\n\r\n  getRawClient() {\r\n    return this.redis;\r\n  }\r\n\r\n  onModuleDestroy() {\r\n    this.redis.disconnect();\r\n  }\r\n}\r\n"], "names": ["RedisService", "xadd", "stream", "data", "redis", "Object", "entries", "flat", "xread", "lastId", "getRawClient", "onModuleDestroy", "disconnect", "constructor", "url", "env", "external", "REDIS_URL", "Redis"], "mappings": "AAAA,6BAA6B;;;;;+BAMhBA;;;eAAAA;;;wBAL+B;gEAC1B;wBACE;;;;;;;;;;;;;;;AAGb,IAAA,AAAMA,eAAN,MAAMA;IAQX,MAAMC,KACJC,MAAc,EACdC,IAA4B,EACJ;QACxB,OAAO,IAAI,CAACC,KAAK,CAACH,IAAI,CAACC,QAAQ,QAAQG,OAAOC,OAAO,CAACH,MAAMI,IAAI;IAClE;IAEA,MAAMC,MAAMN,MAAc,EAAEO,SAAiB,GAAG,EAAgB;QAC9D,OAAO,IAAI,CAACL,KAAK,CAACI,KAAK,CAAC,SAAS,GAAG,WAAWN,QAAQO;IACzD;IAEAC,eAAe;QACb,OAAO,IAAI,CAACN,KAAK;IACnB;IAEAO,kBAAkB;QAChB,IAAI,CAACP,KAAK,CAACQ,UAAU;IACvB;IAtBAC,aAAc;QACZ,MAAMC,MAAMC,WAAG,CAACC,QAAQ,CAACC,SAAS,IAAI;QACtC,IAAI,CAACb,KAAK,GAAG,IAAIc,gBAAK,CAACJ;IACzB;AAoBF"}