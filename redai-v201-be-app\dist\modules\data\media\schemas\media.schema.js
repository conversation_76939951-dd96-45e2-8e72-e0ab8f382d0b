"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MediaListResponseSchema = exports.MediaSchema = void 0;
const swagger_1 = require("@nestjs/swagger");
class MediaSchema {
    id;
    name;
    description;
    size;
    tags;
    storageKey;
    ownedBy;
    createdAt;
    updatedAt;
    constructor(partial) {
        Object.assign(this, partial);
    }
}
exports.MediaSchema = MediaSchema;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của media',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    __metadata("design:type", String)
], MediaSchema.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên media',
        example: 'Hình ảnh sản phẩm XYZ',
    }),
    __metadata("design:type", String)
], MediaSchema.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả về tài nguyên media',
        example: 'Đây là hình ảnh chất lượng cao của sản phẩm XYZ, chụp từ nhiều góc độ khác nhau.',
    }),
    __metadata("design:type", String)
], MediaSchema.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Kích thước media (bytes)',
        example: 1024000,
    }),
    __metadata("design:type", Number)
], MediaSchema.prototype, "size", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Các thẻ phân loại media',
        example: ['product', 'image', 'high-quality'],
        nullable: true,
    }),
    __metadata("design:type", Array)
], MediaSchema.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Khóa định danh trên hệ thống lưu trữ',
        example: 'media/products/123e4567-e89b-12d3-a456-426614174000.jpg',
    }),
    __metadata("design:type", String)
], MediaSchema.prototype, "storageKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mã người sở hữu media',
        example: 1,
    }),
    __metadata("design:type", Number)
], MediaSchema.prototype, "ownedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời điểm tạo bản ghi (unix timestamp)',
        example: 1625097600000,
    }),
    __metadata("design:type", Number)
], MediaSchema.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời điểm cập nhật bản ghi (unix timestamp)',
        example: 1625097600000,
    }),
    __metadata("design:type", Number)
], MediaSchema.prototype, "updatedAt", void 0);
class MediaListResponseSchema {
    items;
    meta;
}
exports.MediaListResponseSchema = MediaListResponseSchema;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách media',
        type: [MediaSchema],
    }),
    __metadata("design:type", Array)
], MediaListResponseSchema.prototype, "items", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin phân trang',
        type: 'object',
        properties: {
            totalItems: {
                type: 'number',
                example: 100,
                description: 'Tổng số media',
            },
            itemCount: {
                type: 'number',
                example: 10,
                description: 'Số media trên trang hiện tại',
            },
            itemsPerPage: {
                type: 'number',
                example: 10,
                description: 'Số media trên mỗi trang',
            },
            totalPages: {
                type: 'number',
                example: 10,
                description: 'Tổng số trang',
            },
            currentPage: {
                type: 'number',
                example: 1,
                description: 'Trang hiện tại',
            },
        },
    }),
    __metadata("design:type", Object)
], MediaListResponseSchema.prototype, "meta", void 0);
//# sourceMappingURL=media.schema.js.map