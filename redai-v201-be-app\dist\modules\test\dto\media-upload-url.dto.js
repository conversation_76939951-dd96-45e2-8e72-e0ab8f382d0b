"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PresignedUrlResponseDto = exports.MediaUploadUrlDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const file_size_util_1 = require("../../../shared/utils/file/file-size.util");
const class_transformer_1 = require("class-transformer");
class MediaUploadUrlDto {
    mediaType;
    fileSize;
    fileName;
}
exports.MediaUploadUrlDto = MediaUploadUrlDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại media (MIME type)',
        example: 'image/jpeg',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Loại media không được để trống' }),
    (0, class_validator_1.IsString)({ message: 'Loại media phải là chuỗi' }),
    __metadata("design:type", String)
], MediaUploadUrlDto.prototype, "mediaType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Kích thước tối đa của file (bytes)',
        enum: file_size_util_1.FileSizeEnum,
        example: file_size_util_1.FileSizeEnum.ONE_MB,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Kích thước tối đa không được để trống' }),
    (0, class_validator_1.IsEnum)(file_size_util_1.FileSizeEnum, { message: 'Kích thước tối đa không hợp lệ' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], MediaUploadUrlDto.prototype, "fileSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên file (tùy chọn)',
        example: 'my-image.jpg',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Tên file phải là chuỗi' }),
    __metadata("design:type", String)
], MediaUploadUrlDto.prototype, "fileName", void 0);
class PresignedUrlResponseDto {
    uploadUrl;
    key;
    expiresAt;
}
exports.PresignedUrlResponseDto = PresignedUrlResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'URL tạm thời để upload file',
        example: 'https://storage.example.com/upload?token=abc123...',
    }),
    __metadata("design:type", String)
], PresignedUrlResponseDto.prototype, "uploadUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Key của file trên storage',
        example: 'test/images/2023/04/12/my-image-1681289012345.jpg',
    }),
    __metadata("design:type", String)
], PresignedUrlResponseDto.prototype, "key", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian hết hạn của URL (timestamp)',
        example: 1681289912345,
    }),
    __metadata("design:type", Number)
], PresignedUrlResponseDto.prototype, "expiresAt", void 0);
//# sourceMappingURL=media-upload-url.dto.js.map