"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RuleContractAdminController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_employee_guard_1 = require("../../../auth/guards/jwt-employee.guard");
const response_1 = require("../../../../common/response");
const services_1 = require("../services");
const dto_1 = require("../dto");
const swagger_2 = require("../../../../common/swagger");
const current_employee_decorator_1 = require("../../../auth/decorators/current-employee.decorator");
const rule_contract_state_service_1 = require("../../state-machine/rule-contract-state.service");
const rule_contract_types_1 = require("../../state-machine/rule-contract.types");
let RuleContractAdminController = class RuleContractAdminController {
    ruleContractAdminService;
    ruleContractStateService;
    constructor(ruleContractAdminService, ruleContractStateService) {
        this.ruleContractAdminService = ruleContractAdminService;
        this.ruleContractStateService = ruleContractStateService;
    }
    async getContracts(queryDto) {
        const contracts = await this.ruleContractAdminService.getContracts(queryDto);
        return response_1.ApiResponseDto.success(contracts, 'Lấy danh sách hợp đồng nguyên tắc thành công');
    }
    async getContractById(id) {
        const contract = await this.ruleContractAdminService.getContractById(id);
        return response_1.ApiResponseDto.success(contract, 'Lấy chi tiết hợp đồng nguyên tắc thành công');
    }
    async approveContract(employee, id, dto) {
        await this.ruleContractAdminService.getContractById(id);
        const state = await this.ruleContractStateService.approveContract(id, {
            adminId: dto.adminId,
        });
        return response_1.ApiResponseDto.success({ state }, 'Phê duyệt hợp đồng nguyên tắc thành công');
    }
    async rejectContract(employee, id, dto) {
        await this.ruleContractAdminService.getContractById(id);
        const state = await this.ruleContractStateService.rejectContract(id, {
            adminId: dto.adminId,
            rejectionReason: dto.rejectionReason,
        });
        return response_1.ApiResponseDto.success({ state }, 'Từ chối hợp đồng nguyên tắc thành công');
    }
};
exports.RuleContractAdminController = RuleContractAdminController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách hợp đồng nguyên tắc' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lấy danh sách hợp đồng nguyên tắc thành công',
        schema: {
            allOf: [
                { $ref: (0, swagger_1.getSchemaPath)(response_1.ApiResponseDto) },
                {
                    properties: {
                        result: {
                            allOf: [
                                { $ref: (0, swagger_1.getSchemaPath)(response_1.PaginatedResult) },
                                {
                                    properties: {
                                        items: {
                                            type: 'array',
                                            items: { $ref: (0, swagger_1.getSchemaPath)(dto_1.RuleContractResponseDto) },
                                        },
                                    },
                                },
                            ],
                        },
                    },
                },
            ],
        },
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.RuleContractQueryDto]),
    __metadata("design:returntype", Promise)
], RuleContractAdminController.prototype, "getContracts", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy chi tiết hợp đồng nguyên tắc' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của hợp đồng',
        type: 'number',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lấy chi tiết hợp đồng nguyên tắc thành công',
        schema: {
            allOf: [
                { $ref: (0, swagger_1.getSchemaPath)(response_1.ApiResponseDto) },
                {
                    properties: {
                        result: { $ref: (0, swagger_1.getSchemaPath)(dto_1.RuleContractResponseDto) },
                    },
                },
            ],
        },
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RuleContractAdminController.prototype, "getContractById", null);
__decorate([
    (0, common_1.Post)(':id/approve'),
    (0, swagger_1.ApiOperation)({ summary: 'Phê duyệt hợp đồng nguyên tắc' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của hợp đồng',
        type: 'number',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Phê duyệt hợp đồng nguyên tắc thành công',
        schema: {
            allOf: [
                { $ref: (0, swagger_1.getSchemaPath)(response_1.ApiResponseDto) },
                {
                    properties: {
                        result: {
                            type: 'object',
                            properties: {
                                state: {
                                    type: 'string',
                                    enum: Object.values(rule_contract_types_1.RuleContractState),
                                    example: rule_contract_types_1.RuleContractState.APPROVED,
                                },
                            },
                        },
                    },
                },
            ],
        },
    }),
    __param(0, (0, current_employee_decorator_1.CurrentEmployee)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, dto_1.ApproveContractDto]),
    __metadata("design:returntype", Promise)
], RuleContractAdminController.prototype, "approveContract", null);
__decorate([
    (0, common_1.Post)(':id/reject'),
    (0, swagger_1.ApiOperation)({ summary: 'Từ chối hợp đồng nguyên tắc' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của hợp đồng',
        type: 'number',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Từ chối hợp đồng nguyên tắc thành công',
        schema: {
            allOf: [
                { $ref: (0, swagger_1.getSchemaPath)(response_1.ApiResponseDto) },
                {
                    properties: {
                        result: {
                            type: 'object',
                            properties: {
                                state: {
                                    type: 'string',
                                    enum: Object.values(rule_contract_types_1.RuleContractState),
                                    example: rule_contract_types_1.RuleContractState.REJECTED,
                                },
                            },
                        },
                    },
                },
            ],
        },
    }),
    __param(0, (0, current_employee_decorator_1.CurrentEmployee)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, dto_1.RejectContractDto]),
    __metadata("design:returntype", Promise)
], RuleContractAdminController.prototype, "rejectContract", null);
exports.RuleContractAdminController = RuleContractAdminController = __decorate([
    (0, common_1.Controller)('admin/rule-contracts'),
    (0, common_1.UseGuards)(jwt_employee_guard_1.JwtEmployeeGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiTags)(swagger_2.SWAGGER_API_TAGS.ADMIN_RULE_CONTRACT),
    (0, swagger_1.ApiExtraModels)(response_1.ApiResponseDto, dto_1.RuleContractResponseDto, response_1.PaginatedResult),
    __metadata("design:paramtypes", [services_1.RuleContractAdminService,
        rule_contract_state_service_1.RuleContractStateService])
], RuleContractAdminController);
//# sourceMappingURL=rule-contract-admin.controller.js.map