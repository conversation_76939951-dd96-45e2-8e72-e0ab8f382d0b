"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const enums_1 = require("../../../shared/enums");
class MessageResponseDto {
    runId;
    agentId;
    agentName;
    status;
    createdAt;
    message;
    constructor(data) {
        this.runId = data.runId;
        this.agentId = data.agentId;
        this.agentName = data.agentName;
        this.status = data.status;
        this.createdAt = data.createdAt;
        this.message = data.message || 'Message sent successfully. Run created and queued for processing.';
    }
}
exports.MessageResponseDto = MessageResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the created run',
        example: 'run_123456-789-abc'
    }),
    __metadata("design:type", String)
], MessageResponseDto.prototype, "runId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the agent processing the message',
        example: 'agent_123456'
    }),
    __metadata("design:type", String)
], MessageResponseDto.prototype, "agentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the agent processing the message',
        example: 'Customer Support Agent'
    }),
    __metadata("design:type", String)
], MessageResponseDto.prototype, "agentName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current status of the run',
        enum: enums_1.UserAgentRunStatus,
        example: enums_1.UserAgentRunStatus.CREATED
    }),
    __metadata("design:type", String)
], MessageResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when the run was created',
        example: 1672531200000
    }),
    __metadata("design:type", Number)
], MessageResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Message indicating successful creation',
        example: 'Message sent successfully. Run created and queued for processing.'
    }),
    __metadata("design:type", String)
], MessageResponseDto.prototype, "message", void 0);
//# sourceMappingURL=message-response.dto.js.map