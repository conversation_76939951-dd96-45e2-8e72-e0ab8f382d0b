{"version": 3, "sources": ["../../../../src/modules/database/entities/user.entity.ts"], "sourcesContent": ["import {\r\n  <PERSON><PERSON><PERSON>,\r\n  Column,\r\n  PrimaryGeneratedColumn,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n} from 'typeorm';\r\n\r\n@Entity('users')\r\nexport class User {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  @Column({ length: 100 })\r\n  fullName: string;\r\n\r\n  @Column({ unique: true })\r\n  email: string;\r\n\r\n  @Column({ select: false })\r\n  password: string;\r\n\r\n  @Column({ default: false })\r\n  isActive: boolean;\r\n\r\n  @Column({ nullable: true })\r\n  avatarUrl?: string;\r\n\r\n  @CreateDateColumn()\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn()\r\n  updatedAt: Date;\r\n\r\n  // <PERSON><PERSON><PERSON> phương thức bổ sung có thể được thêm vào đây\r\n}\r\n"], "names": ["User", "length", "unique", "select", "default", "nullable"], "mappings": ";;;;+BASaA;;;eAAAA;;;yBAHN;;;;;;;;;;AAGA,IAAA,AAAMA,OAAN,MAAMA;AA0Bb;;;;;;;QAtBYC,QAAQ;;;;;;QAGRC,QAAQ;;;;;;QAGRC,QAAQ;;;;;;QAGRC,SAAS;;;;;;QAGTC,UAAU"}