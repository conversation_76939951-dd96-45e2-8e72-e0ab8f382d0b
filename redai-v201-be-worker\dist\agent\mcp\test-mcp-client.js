"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get createMcpUserClient () {
        return createMcpUserClient;
    },
    get mcpUserClient () {
        return mcpUserClient;
    }
});
const _index = require("@modelcontextprotocol/sdk/client/index.js");
const _crypto = /*#__PURE__*/ _interop_require_wildcard(require("crypto"));
const _common = require("@nestjs/common");
const _stdio = require("@modelcontextprotocol/sdk/client/stdio.js");
const _config = require("../../config");
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
const apiSecretKey = _config.env.agent.API_SECRET_KEY;
const apiPrefixKey = _config.env.agent.API_PREFIX_KEY;
const logger = new _common.Logger('TestMCPClient');
const mcpUserClient = new _index.Client({
    name: 'swagger',
    version: '1.0.0'
}, {
    capabilities: {}
});
/**
 * Tạo key từ secret key
 * @returns Buffer chứa key
 */ function generateKey() {
    return _crypto.createHash('sha256').update(apiSecretKey).digest();
}
/**
 * Tạo iv (initialization vector) ngẫu nhiên
 * @returns Buffer chứa iv
 */ function generateIv() {
    return _crypto.randomBytes(16);
}
/**
 * Tạo API Key từ Agent ID và User ID
 * @param agentId ID của worker
 * @param userId ID của user
 * @returns API Key
 */ function generateApiKey(agentId = 'fb838815-f1dd-4e57-83e6-bb635b39e236', userId = 1) {
    try {
        const data = JSON.stringify({
            agentId,
            userId
        });
        const key = generateKey();
        const iv = generateIv();
        const cipher = _crypto.createCipheriv('aes-256-cbc', key, iv);
        let encrypted = cipher.update(data, 'utf8', 'base64');
        encrypted += cipher.final('base64');
        const result = iv.toString('base64') + ':' + encrypted;
        return `${apiPrefixKey}_${result}`;
    } catch (error) {
        throw new Error('Không thể tạo API Key');
    }
}
// Các hàm tạo API key giữ nguyên...
async function createMcpUserClient() {
    try {
        const apiKey = generateApiKey('fb838815-f1dd-4e57-83e6-bb635b39e236', 1);
        logger.log('API Key:', apiKey);
        // Tạo URL cho kết nối SSE và messages
        const sseUrl = new URL(`http://${_config.env.agent.MCP_HOST}:${_config.env.agent.MCP_PORT}/sse`);
        // Tùy chọn cho SSEClientTransport
        const transportOptions = {
            // Tùy chỉnh request SSE ban đầu
            eventSourceInit: {
                withCredentials: false,
                headers: {
                    Accept: 'text/event-stream',
                    'Cache-Control': 'no-cache',
                    Connection: 'keep-alive'
                }
            },
            // Tùy chỉnh các POST requests
            requestInit: {
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        };
        // Tạo transport với cả hai URL
        const transport = new _stdio.StdioClientTransport({
            command: 'npx',
            args: [
                '-y',
                'tavily-mcp@0.1.2'
            ],
            env: {
                TAVILY_API_KEY: process.env.TAVILY_API_KEY
            }
        });
        // Xử lý sự kiện đóng kết nối
        transport.onclose = ()=>{
            logger.log('Kết nối SSE đã đóng');
        };
        // Xử lý lỗi
        transport.onerror = (error)=>{
            console.error('Lỗi kết nối SSE:', error);
        };
        // Xử lý tin nhắn nhận được
        transport.onmessage = (message)=>{
            logger.log('Nhận tin nhắn từ server:', message);
        };
        // KẾT NỐI CLIENT VỚI TRANSPORT
        logger.log('Đang kết nối đến server...');
        await mcpUserClient.connect(transport);
        logger.log('Đã kết nối thành công!');
    } catch (error) {
        console.error('Lỗi:', error);
    }
}

//# sourceMappingURL=test-mcp-client.js.map