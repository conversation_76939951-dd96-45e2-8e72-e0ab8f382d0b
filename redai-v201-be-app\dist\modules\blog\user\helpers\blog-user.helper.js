"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlogUserHelper = void 0;
const typeorm_1 = require("typeorm");
const dto_1 = require("../../dto");
class BlogUserHelper {
    static createBlogQueryBuilder(blogRepository) {
        return blogRepository.createQueryBuilder('blog');
    }
    static applyFilters(queryBuilder, filters) {
        const { authorType, tags, search } = filters;
        if (authorType) {
            queryBuilder.andWhere('blog.authorType = :authorType', {
                authorType: authorType,
            });
        }
        if (tags && tags.length > 0) {
            queryBuilder.andWhere('blog.tags @> :tags', {
                tags: JSON.stringify(tags),
            });
        }
        if (search) {
            queryBuilder.andWhere('(blog.title ILIKE :search OR blog.content ILIKE :search)', { search: `%${search}%` });
        }
    }
    static async populateUserInfo(blogs, userRepository) {
        const userIds = blogs
            .filter((blog) => blog.userId)
            .map((blog) => blog.userId);
        if (userIds.length === 0) {
            return blogs.map((blog) => dto_1.BlogResponseDto.fromEntity(blog));
        }
        const users = await userRepository.find({
            where: { id: (0, typeorm_1.In)(userIds) },
            select: ['id', 'fullName', 'avatar'],
        });
        return blogs.map((blog) => {
            const user = users.find((u) => u.id === blog.userId) || null;
            return dto_1.BlogResponseDto.fromEntity(blog, user);
        });
    }
    static async findBlogsWithPagination(queryBuilder, dto, userRepository) {
        const { page, limit, authorType, search, sortBy, sortDirection } = dto;
        this.applyFilters(queryBuilder, { authorType, search });
        queryBuilder.orderBy(`blog.${sortBy || 'createdAt'}`, sortDirection || 'DESC');
        const offset = (page - 1) * limit;
        queryBuilder.skip(offset).take(limit);
        const [items, totalItems] = await queryBuilder.getManyAndCount();
        const blogs = await this.populateUserInfo(items, userRepository);
        return {
            content: blogs,
            totalItems,
            itemCount: blogs.length,
            itemsPerPage: limit,
            totalPages: Math.ceil(totalItems / limit),
            currentPage: page,
        };
    }
}
exports.BlogUserHelper = BlogUserHelper;
//# sourceMappingURL=blog-user.helper.js.map