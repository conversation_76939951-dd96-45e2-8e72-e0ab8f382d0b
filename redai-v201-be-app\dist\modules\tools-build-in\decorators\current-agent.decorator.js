"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CurrentAgent = void 0;
const common_1 = require("@nestjs/common");
const exceptions_1 = require("../../../common/exceptions");
const exceptions_2 = require("../exceptions");
exports.CurrentAgent = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const agent = request['agent'];
    if (!agent) {
        throw new exceptions_1.AppException(exceptions_2.TOOLS_BUILD_IN_API_KEY_ERROR_CODES.AGENT_NOT_FOUND, 'Không tìm thấy thông tin agent trong request');
    }
    if (!data) {
        return agent;
    }
    return agent[data];
});
//# sourceMappingURL=current-agent.decorator.js.map