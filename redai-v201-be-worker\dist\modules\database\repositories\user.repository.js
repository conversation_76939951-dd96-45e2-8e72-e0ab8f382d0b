"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "UserRepository", {
    enumerable: true,
    get: function() {
        return UserRepository;
    }
});
const _common = require("@nestjs/common");
const _typeorm = require("@nestjs/typeorm");
const _typeorm1 = require("typeorm");
const _userentity = require("../entities/user.entity");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let UserRepository = class UserRepository {
    // Tìm tất cả người dùng
    async findAll() {
        return this.userRepository.find();
    }
    // Tìm người dùng theo ID
    async findById(id) {
        return this.userRepository.findOne({
            where: {
                id
            }
        });
    }
    // Tìm người dùng theo email
    async findByEmail(email) {
        return this.userRepository.findOne({
            where: {
                email
            }
        });
    }
    // Tạo người dùng mới
    async create(userData) {
        const user = this.userRepository.create(userData);
        return this.userRepository.save(user);
    }
    // Cập nhật thông tin người dùng
    async update(id, userData) {
        await this.userRepository.update(id, userData);
        return this.findById(id);
    }
    // Xóa người dùng
    async remove(id) {
        await this.userRepository.delete(id);
    }
    constructor(userRepository){
        this.userRepository = userRepository;
    }
};
UserRepository = _ts_decorate([
    (0, _common.Injectable)(),
    _ts_param(0, (0, _typeorm.InjectRepository)(_userentity.User)),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _typeorm1.Repository === "undefined" ? Object : _typeorm1.Repository
    ])
], UserRepository);

//# sourceMappingURL=user.repository.js.map