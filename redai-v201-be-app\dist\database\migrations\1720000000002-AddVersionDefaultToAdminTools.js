"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddVersionDefaultToAdminTools1720000000002 = void 0;
class AddVersionDefaultToAdminTools1720000000002 {
    async up(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE "admin_tools"
      ADD COLUMN IF NOT EXISTS "version_default" UUID NULL
    `);
        await queryRunner.query(`
      COMMENT ON COLUMN "admin_tools"."version_default"
      IS 'ID của phiên bản mặc định, tham chiếu đến admin_tool_versions.id'
    `);
        await queryRunner.query(`
      ALTER TABLE "admin_tool_versions"
      ADD COLUMN IF NOT EXISTS "is_default" BOOLEAN NOT NULL DEFAULT FALSE
    `);
        await queryRunner.query(`
      COMMENT ON COLUMN "admin_tool_versions"."is_default"
      IS 'Đ<PERSON>h dấu phiên bản này có phải là phiên bản mặc định không'
    `);
        await queryRunner.query(`
      ALTER TABLE "admin_tool_versions"
      DROP CONSTRAINT IF EXISTS "unique_tool_version"
    `);
        await queryRunner.query(`
      ALTER TABLE "admin_tool_versions"
      ADD CONSTRAINT "unique_tool_version"
      UNIQUE ("tool_id", "version_name")
    `);
        await queryRunner.query(`
      ALTER TABLE "admin_tools"
      ADD CONSTRAINT "fk_admin_tools_version_default"
      FOREIGN KEY ("version_default") REFERENCES "admin_tool_versions"("id")
      ON DELETE SET NULL
    `);
        await queryRunner.query(`
      WITH latest_versions AS (
        SELECT DISTINCT ON (tool_id)
          id, tool_id
        FROM admin_tool_versions
        ORDER BY tool_id, created_at DESC
      )
      UPDATE admin_tool_versions
      SET is_default = TRUE
      WHERE id IN (SELECT id FROM latest_versions)
    `);
        await queryRunner.query(`
      UPDATE admin_tools
      SET version_default = (
        SELECT id
        FROM admin_tool_versions
        WHERE tool_id = admin_tools.id
        AND is_default = TRUE
        LIMIT 1
      )
      WHERE deleted_at IS NULL
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE "admin_tools"
      DROP CONSTRAINT IF EXISTS "fk_admin_tools_version_default"
    `);
        await queryRunner.query(`
      ALTER TABLE "admin_tool_versions"
      DROP CONSTRAINT IF EXISTS "unique_tool_version"
    `);
        await queryRunner.query(`
      ALTER TABLE "admin_tool_versions"
      ADD CONSTRAINT "unique_tool_version"
      UNIQUE ("tool_id", "version_number")
    `);
        await queryRunner.query(`
      ALTER TABLE "admin_tools"
      DROP COLUMN IF EXISTS "version_default"
    `);
        await queryRunner.query(`
      ALTER TABLE "admin_tool_versions"
      DROP COLUMN IF EXISTS "is_default"
    `);
    }
}
exports.AddVersionDefaultToAdminTools1720000000002 = AddVersionDefaultToAdminTools1720000000002;
//# sourceMappingURL=1720000000002-AddVersionDefaultToAdminTools.js.map