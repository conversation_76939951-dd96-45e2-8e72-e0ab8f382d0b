"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserConversationThread = void 0;
const typeorm_1 = require("typeorm");
let UserConversationThread = class UserConversationThread {
    threadId;
    name;
    userId;
    createdAt;
    updatedAt;
};
exports.UserConversationThread = UserConversationThread;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid', { name: 'thread_id' }),
    __metadata("design:type", String)
], UserConversationThread.prototype, "threadId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: false }),
    __metadata("design:type", String)
], UserConversationThread.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id', type: 'integer', nullable: false }),
    __metadata("design:type", Number)
], UserConversationThread.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'created_at',
        type: 'bigint',
        nullable: false,
        default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint'
    }),
    __metadata("design:type", Number)
], UserConversationThread.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'updated_at',
        type: 'bigint',
        nullable: false,
        default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint'
    }),
    __metadata("design:type", Number)
], UserConversationThread.prototype, "updatedAt", void 0);
exports.UserConversationThread = UserConversationThread = __decorate([
    (0, typeorm_1.Entity)('user_conversation_thread'),
    (0, typeorm_1.Index)('idx_conversation_thread_user_id', ['userId']),
    (0, typeorm_1.Index)('idx_conversation_thread_created_at', ['createdAt'])
], UserConversationThread);
//# sourceMappingURL=user-conversation-thread.entity.js.map