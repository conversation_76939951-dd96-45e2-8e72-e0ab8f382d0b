{"version": 3, "file": "knowledge-file-user.service.mock.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/data/knowledge-files/user/tests/knowledge-file-user.service.mock.ts"], "names": [], "mappings": ";;;AAAA,2HAAiH;AACjH,8FAAqF;AACrF,8FAAmF;AACnF,yDAAoE;AACpE,2CAAwE;AACxE,gFAA4E;AAC5E,0FAAuG;AAEvG,MAAa,4BAA4B;IAEvC,KAAK,CAAC,gBAAgB,CAAC,GAAG,EAAE,MAAM;QAChC,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;QACxB,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,cAAc,CAAC,EAAE,CAAC;YACrD,MAAM,IAAI,4BAAmB,CAAC,6BAA6B,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC,EAAE,CAAC;YAClD,MAAM,IAAI,4BAAY,CAAC,uCAA0B,CAAC,2BAA2B,CAAC,CAAC;QACjF,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;gBAChC,EAAE,EAAE,OAAO,KAAK,GAAG,CAAC,EAAE;gBACtB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,MAAM,EAAE,gDAAmB,CAAC,KAAK;gBACjC,SAAS,EAAE,2BAAS,CAAC,IAAI;gBACzB,OAAO,EAAE,MAAM;gBACf,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAC;SACJ,CAAC;IACJ,CAAC;IAGD,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM;QAC7B,MAAM,SAAS,GAAG;YAChB;gBACE,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE,UAAU;gBAChB,UAAU,EAAE,oBAAoB;gBAChC,SAAS,EAAE,2BAAS,CAAC,IAAI;gBACzB,OAAO,EAAE,MAAM;gBACf,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,MAAM,EAAE,gDAAmB,CAAC,KAAK;gBACjC,SAAS,EAAE,KAAK;gBAChB,OAAO,EAAE,4CAA4C;aACtD;YACD;gBACE,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE,WAAW;gBACjB,UAAU,EAAE,qBAAqB;gBACjC,SAAS,EAAE,2BAAS,CAAC,IAAI;gBACzB,OAAO,EAAE,MAAM;gBACf,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,MAAM,EAAE,gDAAmB,CAAC,KAAK;gBACjC,SAAS,EAAE,MAAM;gBACjB,OAAO,EAAE,6CAA6C;aACvD;SACF,CAAC;QAEF,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YACpB,OAAO;gBACL,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACpE,IAAI,EAAE;oBACJ,UAAU,EAAE,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;oBAChF,YAAY,EAAE,QAAQ,CAAC,KAAK;oBAC5B,WAAW,EAAE,QAAQ,CAAC,IAAI;iBAC3B;aACF,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;YACvB,OAAO;gBACL,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;gBACvE,IAAI,EAAE;oBACJ,UAAU,EAAE,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM;oBACnF,YAAY,EAAE,QAAQ,CAAC,KAAK;oBAC5B,WAAW,EAAE,QAAQ,CAAC,IAAI;iBAC3B;aACF,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,CAAC,aAAa,KAAK,OAAO,EAAE,CAAC;YACvC,MAAM,IAAI,4BAAY,CAAC,uCAA0B,CAAC,yBAAyB,CAAC,CAAC;QAC/E,CAAC;QAED,OAAO;YACL,KAAK,EAAE,SAAS;YAChB,IAAI,EAAE;gBACJ,UAAU,EAAE,SAAS,CAAC,MAAM;gBAC5B,YAAY,EAAE,QAAQ,CAAC,KAAK;gBAC5B,WAAW,EAAE,QAAQ,CAAC,IAAI;aAC3B;SACF,CAAC;IACJ,CAAC;IAGD,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM;QAC7B,IAAI,MAAM,KAAK,cAAc,EAAE,CAAC;YAC9B,MAAM,IAAI,0BAAiB,CAAC,8BAA8B,MAAM,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,MAAM,KAAK,oBAAoB,EAAE,CAAC;YACpC,MAAM,IAAI,4BAAY,CAAC,uCAA0B,CAAC,2BAA2B,EAAE,mCAAmC,CAAC,CAAC;QACtH,CAAC;QAED,IAAI,MAAM,KAAK,cAAc,EAAE,CAAC;YAC9B,MAAM,IAAI,4BAAY,CAAC,uCAA0B,CAAC,2BAA2B,EAAE,4BAA4B,CAAC,CAAC;QAC/G,CAAC;QAED,IAAI,MAAM,KAAK,UAAU,EAAE,CAAC;YAC1B,MAAM,IAAI,4BAAY,CAAC,uCAA0B,CAAC,2BAA2B,EAAE,wBAAwB,CAAC,CAAC;QAC3G,CAAC;QAED,IAAI,MAAM,KAAK,kBAAkB,EAAE,CAAC;YAClC,MAAM,IAAI,4BAAY,CAAC,uCAA0B,CAAC,2BAA2B,EAAE,gCAAgC,CAAC,CAAC;QACnH,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAGD,wBAAwB,CAAC,QAAQ;QAC/B,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,mCAAY,CAAC,GAAG;gBACnB,OAAO,MAAM,CAAC;YAChB,KAAK,mCAAY,CAAC,IAAI;gBACpB,OAAO,OAAO,CAAC;YACjB,KAAK,mCAAY,CAAC,GAAG;gBACnB,OAAO,MAAM,CAAC;YAChB;gBACE,MAAM,IAAI,4BAAmB,CAAC,6BAA6B,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED,uBAAuB,CAAC,QAAQ;QAC9B,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,mCAAY,CAAC,GAAG,CAAC;YACtB,KAAK,mCAAY,CAAC,IAAI,CAAC;YACvB,KAAK,mCAAY,CAAC,GAAG;gBACnB,OAAO,yBAAkB,CAAC,QAAQ,CAAC;YACrC;gBACE,MAAM,IAAI,4BAAmB,CAAC,6BAA6B,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,aAAa;QACxC,IAAI,aAAa,KAAK,iBAAiB,EAAE,CAAC;YACxC,MAAM,IAAI,0BAAiB,CAAC,sCAAsC,aAAa,EAAE,CAAC,CAAC;QACrF,CAAC;QAED,IAAI,aAAa,KAAK,UAAU,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,EAAE,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,mBAAmB,EAAE,CAAC;IAC1D,CAAC;IAED,aAAa,CAAC,SAAS;QACrB,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,MAAM,cAAc,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;QAC/C,QAAQ,cAAc,EAAE,CAAC;YACvB,KAAK,MAAM;gBACT,OAAO,MAAM,CAAC;YAChB,KAAK,WAAW;gBACd,OAAO,WAAW,CAAC;YACrB,KAAK,SAAS;gBACZ,OAAO,SAAS,CAAC;YACnB;gBACE,OAAO,WAAW,CAAC;QACvB,CAAC;IACH,CAAC;CACF;AA1KD,oEA0KC"}