"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AppModule", {
    enumerable: true,
    get: function() {
        return AppModule;
    }
});
const _common = require("@nestjs/common");
const _bullmq = require("@nestjs/bullmq");
const _config = require("./config");
const _queue = require("./queue");
const _agent = require("./agent");
const _infra = require("./infra");
const _sharedmodule = require("./shared/shared.module");
const _ioredis = /*#__PURE__*/ _interop_require_default(require("ioredis"));
const _typeorm = require("@nestjs/typeorm");
const _database = require("./modules/database");
const _emailsystemmodule = require("./modules/email-system/email-system.module");
const _emailmarketingmodule = require("./modules/marketing/email/email-marketing.module");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
let AppModule = class AppModule {
};
AppModule = _ts_decorate([
    (0, _common.Module)({
        imports: [
            _sharedmodule.SharedModule,
            _queue.QueueModule,
            _infra.InfraModule,
            _agent.AgentModule,
            _database.DatabaseModule,
            _emailsystemmodule.EmailSystemModule,
            _emailmarketingmodule.EmailMarketingModule,
            _typeorm.TypeOrmModule.forRoot(_config.databaseConfig),
            _bullmq.BullModule.forRoot({
                connection: {
                    url: _config.env.external.REDIS_URL
                }
            })
        ],
        providers: [
            {
                provide: _ioredis.default,
                useFactory: ()=>{
                    return new _ioredis.default(_config.env.external.REDIS_URL);
                }
            }
        ]
    })
], AppModule);

//# sourceMappingURL=app.module.js.map