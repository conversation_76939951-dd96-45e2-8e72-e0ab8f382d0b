"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PdfPositionUtils = void 0;
const contract_type_enum_1 = require("../../enums/contract-type.enum");
const enums_1 = require("../../../modules/user/enums");
class PdfPositionUtils {
    static getPositionSignatureAdmin(contract, type, signatureBase64) {
        const positions = [];
        switch (contract) {
            case contract_type_enum_1.ContractTypeEnum.RULE_CONTRACT:
                if (type === enums_1.UserTypeEnum.BUSINESS) {
                    const position = {
                        signatureBase64,
                        signatureWidthMm: 30,
                        signatureHeightMm: 20,
                        pageIndex: 13,
                        xMm: 120,
                        yMm: 75,
                    };
                    positions.push(position);
                }
                else if (type === enums_1.UserTypeEnum.INDIVIDUAL) {
                    const position = {
                        signatureBase64,
                        signatureWidthMm: 30,
                        signatureHeightMm: 20,
                        pageIndex: 13,
                        xMm: 120,
                        yMm: 115,
                    };
                    positions.push(position);
                }
                break;
            case contract_type_enum_1.ContractTypeEnum.AFFILIATE_CONTRACT:
                if (type === enums_1.UserTypeEnum.BUSINESS) {
                    const position = {
                        signatureBase64,
                        signatureWidthMm: 30,
                        signatureHeightMm: 30,
                        pageIndex: 14,
                        xMm: 50,
                        yMm: 220,
                    };
                    positions.push(position);
                }
                else if (type === enums_1.UserTypeEnum.INDIVIDUAL) {
                    const position = {
                        signatureBase64,
                        signatureWidthMm: 30,
                        signatureHeightMm: 30,
                        pageIndex: 15,
                        xMm: 50,
                        yMm: 50,
                    };
                    positions.push(position);
                }
                break;
        }
        return positions;
    }
    static getPositionSignatureCustomer(contract, type, signatureBase64) {
        const positions = [];
        switch (contract) {
            case contract_type_enum_1.ContractTypeEnum.RULE_CONTRACT:
                if (type === enums_1.UserTypeEnum.BUSINESS) {
                    const position = {
                        signatureBase64,
                        signatureWidthMm: 30,
                        signatureHeightMm: 30,
                        pageIndex: 13,
                        xMm: 48,
                        yMm: 67,
                    };
                    positions.push(position);
                }
                else if (type === enums_1.UserTypeEnum.INDIVIDUAL) {
                    const position = {
                        signatureBase64,
                        signatureWidthMm: 30,
                        signatureHeightMm: 30,
                        pageIndex: 13,
                        xMm: 48,
                        yMm: 110,
                    };
                    positions.push(position);
                }
                break;
            case contract_type_enum_1.ContractTypeEnum.AFFILIATE_CONTRACT:
                if (type === enums_1.UserTypeEnum.BUSINESS) {
                    const position = {
                        signatureBase64,
                        signatureWidthMm: 30,
                        signatureHeightMm: 30,
                        pageIndex: 14,
                        xMm: 125,
                        yMm: 220,
                    };
                    positions.push(position);
                }
                else if (type === enums_1.UserTypeEnum.INDIVIDUAL) {
                    const position = {
                        signatureBase64,
                        signatureWidthMm: 30,
                        signatureHeightMm: 30,
                        pageIndex: 15,
                        xMm: 125,
                        yMm: 50,
                    };
                    positions.push(position);
                }
                break;
        }
        return positions;
    }
    static affiliateContractCustomer(userId, time, name, dateOfBirth, cccd, issueDate, issuePlace, phoneNumber, address, taxCode) {
        const pdfPositions = [];
        const formatter = (date) => {
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        };
        const formattedTime = formatter(new Date(time));
        pdfPositions.push({ pageIndex: 1, text: `${userId}CN`, size: 12, xMm: 119, yMm: 87 });
        pdfPositions.push({ pageIndex: 1, text: formattedTime, size: 10, xMm: 49, yMm: 163 });
        pdfPositions.push({ pageIndex: 2, text: name, size: 12, xMm: 77, yMm: 25.5 });
        pdfPositions.push({ pageIndex: 2, text: formatter(dateOfBirth), size: 10, xMm: 57.5, yMm: 32.5 });
        pdfPositions.push({ pageIndex: 2, text: cccd, size: 10, xMm: 57.5, yMm: 39.4 });
        pdfPositions.push({ pageIndex: 2, text: formatter(issueDate), size: 10, xMm: 57.5, yMm: 46.8 });
        pdfPositions.push({ pageIndex: 2, text: issuePlace, size: 10, xMm: 57.5, yMm: 53.9 });
        pdfPositions.push({ pageIndex: 2, text: phoneNumber, size: 10, xMm: 57.5, yMm: 61.2 });
        pdfPositions.push({ pageIndex: 2, text: address, size: 10, xMm: 57.5, yMm: 68.4 });
        if (taxCode) {
            pdfPositions.push({ pageIndex: 2, text: taxCode, size: 10, xMm: 57.5, yMm: 75.8 });
        }
        pdfPositions.push({
            pageIndex: 15,
            text: name.toUpperCase(),
            size: 12,
            xMm: 136,
            yMm: 104.6,
            isCenter: true,
            fontWeight: 600
        });
        return pdfPositions;
    }
    static affiliateContractBusiness(userId, time, representativeName, representativePosition, email, phoneNumber, address, taxCode, businessName) {
        const pdfPositions = [];
        const formatter = (date) => {
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        };
        const formattedTime = formatter(new Date(time));
        pdfPositions.push({ pageIndex: 1, text: `${userId}DN`, size: 12, xMm: 119, yMm: 87 });
        pdfPositions.push({ pageIndex: 1, text: formattedTime, size: 10, xMm: 49, yMm: 163 });
        pdfPositions.push({ pageIndex: 1, text: businessName, size: 12, xMm: 77, yMm: 257.5 });
        pdfPositions.push({ pageIndex: 1, text: representativeName, size: 10, xMm: 57.5, yMm: 265 });
        pdfPositions.push({ pageIndex: 2, text: representativePosition, size: 10, xMm: 57.5, yMm: 25 });
        pdfPositions.push({ pageIndex: 2, text: email, size: 10, xMm: 57.5, yMm: 32 });
        pdfPositions.push({ pageIndex: 2, text: phoneNumber, size: 10, xMm: 57.5, yMm: 39.5 });
        pdfPositions.push({ pageIndex: 2, text: address, size: 10, xMm: 57.5, yMm: 46.5 });
        pdfPositions.push({ pageIndex: 2, text: taxCode, size: 10, xMm: 57.5, yMm: 54 });
        pdfPositions.push({
            pageIndex: 14,
            text: representativeName.toUpperCase(),
            size: 12,
            xMm: 136,
            yMm: 268,
            isCenter: true
        });
        return pdfPositions;
    }
    static ruleContractCustomer(userId, time, name, dateOfBirth, cccd, issueDate, issuePlace, phoneNumber, address, taxCode) {
        const pdfPositions = [];
        const formatter = (date) => {
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        };
        const formattedTime = formatter(new Date(time));
        pdfPositions.push({ pageIndex: 1, text: `${userId}CN`, size: 12, xMm: 115, yMm: 86.8 });
        pdfPositions.push({ pageIndex: 1, text: formattedTime, size: 10, xMm: 49, yMm: 163 });
        pdfPositions.push({ pageIndex: 1, text: name, size: 12, xMm: 76, yMm: 170.5 });
        pdfPositions.push({ pageIndex: 1, text: formatter(dateOfBirth), size: 10, xMm: 57.5, yMm: 177.5 });
        pdfPositions.push({ pageIndex: 1, text: cccd, size: 10, xMm: 57.5, yMm: 184.8 });
        pdfPositions.push({ pageIndex: 1, text: formatter(issueDate), size: 10, xMm: 57.5, yMm: 192 });
        pdfPositions.push({ pageIndex: 1, text: issuePlace, size: 10, xMm: 57.5, yMm: 199 });
        pdfPositions.push({ pageIndex: 1, text: phoneNumber, size: 10, xMm: 57.5, yMm: 206.6 });
        pdfPositions.push({ pageIndex: 1, text: address, size: 10, xMm: 57.5, yMm: 213.6 });
        if (taxCode) {
            pdfPositions.push({ pageIndex: 1, text: taxCode, size: 10, xMm: 57.5, yMm: 221 });
        }
        pdfPositions.push({
            pageIndex: 13,
            text: name.toUpperCase(),
            size: 12,
            xMm: 60,
            yMm: 159,
            isCenter: true
        });
        return pdfPositions;
    }
    static ruleContractBusiness(userId, time, businessName, representativeName, representativePosition, businessEmail, businessPhone, businessAddress, taxCode) {
        const pdfPositions = [];
        const formatter = (date) => {
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        };
        const formattedTime = formatter(new Date(time));
        pdfPositions.push({ pageIndex: 1, text: `${userId}DN`, size: 12, xMm: 116, yMm: 87 });
        pdfPositions.push({ pageIndex: 1, text: formattedTime, size: 10, xMm: 49, yMm: 163 });
        pdfPositions.push({ pageIndex: 1, text: businessName, size: 12, xMm: 75, yMm: 171 });
        pdfPositions.push({ pageIndex: 1, text: representativeName, size: 10, xMm: 57.5, yMm: 177.5 });
        pdfPositions.push({ pageIndex: 1, text: representativePosition, size: 10, xMm: 57.5, yMm: 185 });
        pdfPositions.push({ pageIndex: 1, text: businessEmail, size: 10, xMm: 57.5, yMm: 192.3 });
        pdfPositions.push({ pageIndex: 1, text: businessPhone, size: 10, xMm: 57.5, yMm: 199.3 });
        pdfPositions.push({ pageIndex: 1, text: businessAddress, size: 10, xMm: 57.5, yMm: 206.3 });
        pdfPositions.push({ pageIndex: 1, text: taxCode, size: 10, xMm: 57.5, yMm: 213.7 });
        pdfPositions.push({
            pageIndex: 13,
            text: representativeName.toUpperCase(),
            size: 12,
            xMm: 60,
            yMm: 118.4,
            isCenter: true
        });
        return pdfPositions;
    }
}
exports.PdfPositionUtils = PdfPositionUtils;
//# sourceMappingURL=pdf-position-utils.js.map