"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ApiKeyEncryptionHelper", {
    enumerable: true,
    get: function() {
        return ApiKeyEncryptionHelper;
    }
});
const _common = require("@nestjs/common");
const _crypto = /*#__PURE__*/ _interop_require_wildcard(require("crypto"));
const _config = require("../../config");
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let ApiKeyEncryptionHelper = class ApiKeyEncryptionHelper {
    /**
   * Mã hóa API key cho admin
   * @param apiKey API key cần mã hóa
   * @returns API key đã được mã hóa
   */ encryptAdminApiKey(apiKey) {
        if (!this.adminSecretKey) {
            throw new Error('ADMIN_SECRECT_MODEL không được cấu hình');
        }
        return this.encrypt(apiKey, this.adminSecretKey);
    }
    /**
   * Giải mã API key cho admin
   * @param encryptedApiKey API key đã mã hóa
   * @returns API key gốc
   */ decryptAdminApiKey(encryptedApiKey) {
        if (!this.adminSecretKey) {
            throw new Error('ADMIN_SECRECT_MODEL không được cấu hình');
        }
        return this.decrypt(encryptedApiKey, this.adminSecretKey);
    }
    /**
   * Mã hóa API key cho user
   * @param apiKey API key cần mã hóa
   * @param userId ID của user
   * @returns API key đã được mã hóa
   */ encryptUserApiKey(apiKey, userId) {
        if (!this.userSecretKey) {
            throw new Error('USER_SECRECT_MODEL không được cấu hình');
        }
        // Kết hợp secretKey với userId để tạo key mã hóa riêng cho mỗi user
        const userSpecificKey = `${this.userSecretKey}_${userId}`;
        return this.encrypt(apiKey, userSpecificKey);
    }
    /**
   * Giải mã API key cho user
   * @param encryptedApiKey API key đã mã hóa
   * @param userId ID của user
   * @returns API key gốc
   */ decryptUserApiKey(encryptedApiKey, userId) {
        if (!this.userSecretKey) {
            throw new Error('USER_SECRECT_MODEL không được cấu hình');
        }
        // Kết hợp secretKey với userId để tạo key mã hóa riêng cho mỗi user
        const userSpecificKey = `${this.userSecretKey}_${userId}`;
        return this.decrypt(encryptedApiKey, userSpecificKey);
    }
    /**
   * Hàm mã hóa chung
   * @param text Chuỗi cần mã hóa
   * @param secretKey Khóa bí mật
   * @returns Chuỗi đã mã hóa
   */ encrypt(text, secretKey) {
        // Tạo key từ secretKey bằng cách hash với SHA-256
        const key = _crypto.createHash('sha256').update(secretKey).digest('base64').substring(0, 32);
        // Tạo IV (Initialization Vector) ngẫu nhiên
        const iv = _crypto.randomBytes(16);
        // Tạo cipher
        const cipher = _crypto.createCipheriv(this.algorithm, key, iv);
        // Mã hóa
        let encrypted = cipher.update(text, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        // Kết hợp IV và chuỗi đã mã hóa (IV cần được lưu cùng để giải mã)
        return iv.toString('hex') + ':' + encrypted;
    }
    /**
   * Hàm giải mã chung
   * @param encryptedText Chuỗi đã mã hóa
   * @param secretKey Khóa bí mật
   * @returns Chuỗi gốc
   */ decrypt(encryptedText, secretKey) {
        // Tạo key từ secretKey bằng cách hash với SHA-256
        const key = _crypto.createHash('sha256').update(secretKey).digest('base64').substring(0, 32);
        // Tách IV và chuỗi đã mã hóa
        const textParts = encryptedText.split(':');
        const iv = Buffer.from(textParts[0], 'hex');
        const encryptedData = textParts[1];
        // Tạo decipher
        const decipher = _crypto.createDecipheriv(this.algorithm, key, iv);
        // Giải mã
        let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    }
    constructor(){
        this.algorithm = 'aes-256-cbc';
        this.adminSecretKey = _config.env.llmSystemEncryptionKey.ADMIN_SECRECT_MODEL;
        this.userSecretKey = _config.env.llmSystemEncryptionKey.USER_SECRECT_MODEL;
    }
};
ApiKeyEncryptionHelper = _ts_decorate([
    (0, _common.Injectable)(),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [])
], ApiKeyEncryptionHelper);

//# sourceMappingURL=api-key-encryption.helper.js.map