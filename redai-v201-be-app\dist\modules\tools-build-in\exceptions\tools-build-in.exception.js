"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TOOLS_BUILD_IN_ERROR_CODES = void 0;
const common_1 = require("@nestjs/common");
const exceptions_1 = require("../../../common/exceptions");
exports.TOOLS_BUILD_IN_ERROR_CODES = {
    FACEBOOK_API_ERROR: new exceptions_1.ErrorCode(30001, 'Lỗi khi gọi Facebook API', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    FACEBOOK_AUTH_ERROR: new exceptions_1.ErrorCode(30002, 'Lỗi xác thực Facebook', common_1.HttpStatus.UNAUTHORIZED),
    FACEBOOK_PERMISSION_ERROR: new exceptions_1.ErrorCode(30003, 'Không có quyền truy cập Facebook', common_1.HttpStatus.FORBIDDEN),
    WEBSITE_FETCH_ERROR: new exceptions_1.ErrorCode(30101, 'Lỗi khi lấy thông tin website', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    WEBSITE_ANALYSIS_ERROR: new exceptions_1.ErrorCode(30102, 'Lỗi khi phân tích website', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    WEBSITE_SEO_ERROR: new exceptions_1.ErrorCode(30103, 'Lỗi khi phân tích SEO website', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    INVALID_URL: new exceptions_1.ErrorCode(30104, 'URL không hợp lệ', common_1.HttpStatus.BAD_REQUEST),
    ADDRESS_FETCH_ERROR: new exceptions_1.ErrorCode(30201, 'Lỗi khi lấy thông tin địa chỉ', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    ADDRESS_SEARCH_ERROR: new exceptions_1.ErrorCode(30202, 'Lỗi khi tìm kiếm địa chỉ', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    SHIPPING_CALCULATION_ERROR: new exceptions_1.ErrorCode(30203, 'Lỗi khi tính phí vận chuyển', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    PRODUCT_NOT_FOUND: new exceptions_1.ErrorCode(30301, 'Không tìm thấy sản phẩm', common_1.HttpStatus.NOT_FOUND),
    PRODUCT_FETCH_ERROR: new exceptions_1.ErrorCode(30302, 'Lỗi khi lấy thông tin sản phẩm', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    PRODUCT_SEARCH_ERROR: new exceptions_1.ErrorCode(30303, 'Lỗi khi tìm kiếm sản phẩm', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    PRODUCT_UPLOAD_ERROR: new exceptions_1.ErrorCode(30304, 'Lỗi khi tải lên hình ảnh sản phẩm', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    PRODUCT_TEMPLATE_ERROR: new exceptions_1.ErrorCode(30305, 'Lỗi khi tạo mẫu sản phẩm', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    PRODUCT_ACCESS_DENIED: new exceptions_1.ErrorCode(30306, 'Không có quyền truy cập sản phẩm', common_1.HttpStatus.FORBIDDEN),
    INVALID_S3_KEY: new exceptions_1.ErrorCode(30307, 'Key S3 không hợp lệ', common_1.HttpStatus.BAD_REQUEST),
    CUSTOMER_NOT_FOUND: new exceptions_1.ErrorCode(30401, 'Không tìm thấy khách hàng', common_1.HttpStatus.NOT_FOUND),
    CUSTOMER_FETCH_ERROR: new exceptions_1.ErrorCode(30402, 'Lỗi khi lấy thông tin khách hàng', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    CONVERSION_NOT_FOUND: new exceptions_1.ErrorCode(30403, 'Không tìm thấy chuyển đổi', common_1.HttpStatus.NOT_FOUND),
    CONVERSION_FETCH_ERROR: new exceptions_1.ErrorCode(30404, 'Lỗi khi lấy thông tin chuyển đổi', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    CONVERSION_SEARCH_ERROR: new exceptions_1.ErrorCode(30405, 'Lỗi khi tìm kiếm chuyển đổi', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    CUSTOMER_UPLOAD_ERROR: new exceptions_1.ErrorCode(30406, 'Lỗi khi tải lên avatar khách hàng', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    EMAIL_SEND_ERROR: new exceptions_1.ErrorCode(30501, 'Lỗi khi gửi email', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    SMS_SEND_ERROR: new exceptions_1.ErrorCode(30502, 'Lỗi khi gửi SMS', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    PUSH_NOTIFICATION_ERROR: new exceptions_1.ErrorCode(30503, 'Lỗi khi gửi thông báo đẩy', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    EMAIL_HISTORY_ERROR: new exceptions_1.ErrorCode(30504, 'Lỗi khi lấy lịch sử email', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    SMS_HISTORY_ERROR: new exceptions_1.ErrorCode(30505, 'Lỗi khi lấy lịch sử SMS', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    AGENT_NOT_FOUND: new exceptions_1.ErrorCode(30601, 'Không tìm thấy agent', common_1.HttpStatus.NOT_FOUND),
    AGENT_TYPE_NOT_FOUND: new exceptions_1.ErrorCode(30602, 'Không tìm thấy loại agent', common_1.HttpStatus.NOT_FOUND),
    AGENT_TOOLS_FETCH_FAILED: new exceptions_1.ErrorCode(30603, 'Lỗi khi lấy danh sách tool của agent', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
};
//# sourceMappingURL=tools-build-in.exception.js.map