"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var GenericPageUserService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericPageUserService = void 0;
const common_1 = require("@nestjs/common");
const common_2 = require("../../../../common");
const generic_page_error_code_1 = require("../../exceptions/generic-page-error.code");
const generic_page_repository_1 = require("../../repositories/generic-page.repository");
const generic_page_submission_repository_1 = require("../../repositories/generic-page-submission.repository");
const generic_page_submission_entity_1 = require("../../entities/generic-page-submission.entity");
const generic_page_enum_1 = require("../../constants/generic-page.enum");
const dto_1 = require("../dto");
let GenericPageUserService = GenericPageUserService_1 = class GenericPageUserService {
    genericPageRepository;
    genericPageSubmissionRepository;
    logger = new common_1.Logger(GenericPageUserService_1.name);
    constructor(genericPageRepository, genericPageSubmissionRepository) {
        this.genericPageRepository = genericPageRepository;
        this.genericPageSubmissionRepository = genericPageSubmissionRepository;
    }
    async getPublishedGenericPageByPath(path) {
        try {
            const genericPage = await this.genericPageRepository.findPublishedByPath(path);
            return this.mapToResponseDto(genericPage);
        }
        catch (error) {
            if (error instanceof common_2.AppException) {
                throw error;
            }
            this.logger.error(`Error getting published generic page by path: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_FOUND, `Không tìm thấy trang với đường dẫn ${path}`);
        }
    }
    async submitForm(pageId, submitFormDto, userId, ipAddress, userAgent) {
        try {
            const genericPage = await this.genericPageRepository.findById(pageId);
            if (genericPage.status !== generic_page_enum_1.GenericPageStatusEnum.PUBLISHED) {
                throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_PUBLISHED, 'Trang chưa được xuất bản');
            }
            const submission = new generic_page_submission_entity_1.GenericPageSubmission();
            submission.pageId = pageId;
            submission.data = submitFormDto.data;
            submission.createdAt = Date.now();
            submission.updatedAt = Date.now();
            submission.ipAddress = ipAddress || '';
            submission.userAgent = userAgent || '';
            submission.userId = userId || '';
            const savedSubmission = await this.genericPageSubmissionRepository.save(submission);
            return this.mapToSubmissionResponseDto(savedSubmission);
        }
        catch (error) {
            if (error instanceof common_2.AppException) {
                throw error;
            }
            this.logger.error(`Error submitting form: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_SUBMISSION_CREATE_ERROR, 'Lỗi khi gửi dữ liệu form');
        }
    }
    mapToResponseDto(genericPage) {
        const responseDto = new dto_1.GenericPageResponseDto();
        responseDto.id = genericPage.id;
        responseDto.name = genericPage.name;
        responseDto.description = genericPage.description;
        responseDto.path = genericPage.path;
        responseDto.config = genericPage.config;
        responseDto.publishedAt = genericPage.publishedAt;
        return responseDto;
    }
    mapToSubmissionResponseDto(submission) {
        const responseDto = new dto_1.SubmissionResponseDto();
        responseDto.id = submission.id;
        responseDto.pageId = submission.pageId;
        responseDto.status = submission.status;
        responseDto.createdAt = submission.createdAt;
        responseDto.message = 'Dữ liệu đã được gửi thành công';
        return responseDto;
    }
};
exports.GenericPageUserService = GenericPageUserService;
exports.GenericPageUserService = GenericPageUserService = GenericPageUserService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [generic_page_repository_1.GenericPageRepository,
        generic_page_submission_repository_1.GenericPageSubmissionRepository])
], GenericPageUserService);
//# sourceMappingURL=generic-page-user.service.js.map