{"version": 3, "sources": ["../../src/infra/infra.module.ts"], "sourcesContent": ["import { Global, Module } from '@nestjs/common';\r\nimport { RedisService } from './redis';\r\n\r\n@Global()\r\n@Module({\r\n  providers: [RedisService],\r\n  exports: [RedisService],\r\n})\r\nexport class InfraModule {}\r\n"], "names": ["InfraModule", "providers", "RedisService", "exports"], "mappings": ";;;;+BAQaA;;;eAAAA;;;wBARkB;uBACF;;;;;;;AAOtB,IAAA,AAAMA,cAAN,MAAMA;AAAa;;;;QAHxBC,WAAW;YAACC,mBAAY;SAAC;QACzBC,SAAS;YAACD,mBAAY;SAAC"}