// src/modules/worker/helpers/cleanModelConfig.spec.ts
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _modelresolver = require("./model-resolver");
const _enums = require("../enums");
describe('cleanModelConfig()', ()=>{
    const baseConfig = (overrides = {})=>({
            name: 'test-model',
            provider: 'OPENAI',
            capabilities: [],
            parameters: {
                topK: 5,
                topP: 0.9,
                temperature: 0.7
            },
            type: 'OUT_SYSTEM',
            ...overrides
        });
    it('returns empty object when no MODIFY_* capabilities are present', ()=>{
        const cfg = baseConfig({
            capabilities: []
        });
        const cleaned = (0, _modelresolver.cleanModelConfig)(cfg);
        expect(cleaned.parameters).toEqual({});
    });
    it('keeps topK only when MODIFY_TOP_K is present', ()=>{
        const cfg = baseConfig({
            capabilities: [
                _enums.ModelFeature.MODIFY_TOP_K
            ]
        });
        const cleaned = (0, _modelresolver.cleanModelConfig)(cfg);
        expect(cleaned.parameters).toEqual({
            topK: 5
        });
    });
    it('keeps topP only when MODIFY_TOP_P is present', ()=>{
        const cfg = baseConfig({
            capabilities: [
                _enums.ModelFeature.MODIFY_TOP_P
            ]
        });
        const cleaned = (0, _modelresolver.cleanModelConfig)(cfg);
        expect(cleaned.parameters).toEqual({
            topP: 0.9
        });
    });
    it('keeps temperature only when MODIFY_TEMPERATURE is present', ()=>{
        const cfg = baseConfig({
            capabilities: [
                _enums.ModelFeature.MODIFY_TEMPERATURE
            ]
        });
        const cleaned = (0, _modelresolver.cleanModelConfig)(cfg);
        expect(cleaned.parameters).toEqual({
            temperature: 0.7
        });
    });
    it('keeps multiple when multiple capabilities are present', ()=>{
        const cfg = baseConfig({
            capabilities: [
                _enums.ModelFeature.MODIFY_TOP_K,
                _enums.ModelFeature.MODIFY_TOP_P
            ]
        });
        const cleaned = (0, _modelresolver.cleanModelConfig)(cfg);
        expect(cleaned.parameters).toEqual({
            topK: 5,
            topP: 0.9
        });
    });
    it('initializes empty object even if original parameters was undefined', ()=>{
        const cfg = baseConfig({
            parameters: undefined
        });
        const cleaned = (0, _modelresolver.cleanModelConfig)(cfg);
        expect(cleaned.parameters).toEqual({});
    });
});

//# sourceMappingURL=test.spec.js.map