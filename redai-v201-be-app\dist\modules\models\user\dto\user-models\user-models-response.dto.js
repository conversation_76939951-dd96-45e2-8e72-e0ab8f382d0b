"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserModelsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const provider_enum_1 = require("../../../constants/provider.enum");
class UserModelsResponseDto {
    id;
    modelId;
    provider;
    inputModalities;
    outputModalities;
    samplingParameters;
    features;
    basePricing;
    fineTunePricing;
    trainingPricing;
}
exports.UserModelsResponseDto = UserModelsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của user model',
        example: '123e4567-e89b-12d3-a456-426614174000'
    }),
    __metadata("design:type", String)
], UserModelsResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID định danh của model',
        example: 'gpt-4-turbo'
    }),
    __metadata("design:type", String)
], UserModelsResponseDto.prototype, "modelId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nhà cung cấp model',
        enum: provider_enum_1.ProviderEnum,
        example: provider_enum_1.ProviderEnum.OPENAI
    }),
    __metadata("design:type", String)
], UserModelsResponseDto.prototype, "provider", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Các phương thức input được hỗ trợ',
        example: ['text', 'image'],
        type: [String]
    }),
    __metadata("design:type", Array)
], UserModelsResponseDto.prototype, "inputModalities", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Các phương thức output được hỗ trợ',
        example: ['text'],
        type: [String]
    }),
    __metadata("design:type", Array)
], UserModelsResponseDto.prototype, "outputModalities", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Các tham số sampling được hỗ trợ',
        example: ['temperature', 'top_p', 'max_tokens'],
        type: [String]
    }),
    __metadata("design:type", Array)
], UserModelsResponseDto.prototype, "samplingParameters", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Các tính năng được hỗ trợ',
        example: ['function_calling', 'streaming'],
        type: [String]
    }),
    __metadata("design:type", Array)
], UserModelsResponseDto.prototype, "features", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Giá cơ bản cho model (input/output rate)',
        example: { inputRate: 0.01, outputRate: 0.03 }
    }),
    __metadata("design:type", Object)
], UserModelsResponseDto.prototype, "basePricing", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Giá fine-tune cho model (input/output rate)',
        example: { inputRate: 0.02, outputRate: 0.06 }
    }),
    __metadata("design:type", Object)
], UserModelsResponseDto.prototype, "fineTunePricing", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Giá training cho model',
        example: 100
    }),
    __metadata("design:type", Number)
], UserModelsResponseDto.prototype, "trainingPricing", void 0);
//# sourceMappingURL=user-models-response.dto.js.map