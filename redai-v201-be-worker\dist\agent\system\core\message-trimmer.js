"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get tokenMessageTrimmer () {
        return tokenMessageTrimmer;
    },
    get trimMessagesWithStrategy () {
        return trimMessagesWithStrategy;
    }
});
const _messages = require("@langchain/core/messages");
const _common = require("@nestjs/common");
const _helpers = require("./helpers");
const logger = new _common.Logger('MessageTrimmer');
async function topKMessageTrimmer(messages, threshold, model) {
    if (messages.length <= threshold) {
        logger.log(`📭 No trimming needed: ${messages.length} <= ${threshold}`);
        return {
            messages: []
        };
    }
    const toDelete = messages.slice(0, Math.max(0, messages.length - threshold));
    const toKeep = messages.slice(-threshold);
    const removals = toDelete.map((msg)=>new _messages.RemoveMessage({
            id: msg.id
        }));
    const removedOrphan = (0, _helpers.dropLeadingOrphanAsRemovals)(toKeep);
    logger.log(`🗑️ Deleting ${removals.length + removedOrphan.length} messages, keeping last ${toKeep.length - removedOrphan.length}`);
    return {
        messages: [
            ...removals,
            ...removedOrphan
        ]
    };
}
async function tokenMessageTrimmer(messages, threshold, model) {
    if (!model) {
        logger.warn('No model provided for token trimming, falling back to no trimming');
        return {
            messages: []
        };
    }
    // 1️⃣ Token‐budget trim (no copy needed)
    const trimmed = await (0, _messages.trimMessages)(messages, {
        maxTokens: threshold,
        strategy: 'last',
        tokenCounter: model,
        startOn: 'human'
    });
    // 2️⃣ Orphan‐drop at the tail
    const cleaned = (0, _helpers.dropTrailingOrphanBlock)(trimmed);
    if (cleaned.length !== trimmed.length) {
        logger.log(`🗑️ Dropped ${trimmed.length - cleaned.length} trailing orphan msg(s)`);
    }
    // 3️⃣ ONE PASS over the original to emit RemoveMessage for any msg
    //    that didn’t survive into `cleaned`.
    const keptIds = new Set(cleaned.map((m)=>m.id));
    const removals = [];
    for (const msg of messages){
        // Skip if it’s in the final kept set
        if (msg.id && !keptIds.has(msg.id)) {
            removals.push(new _messages.RemoveMessage({
                id: msg.id
            }));
        }
    }
    logger.log(`📨 Emitting ${removals.length} RemoveMessage cmds`);
    // 4️⃣ Return just the RemoveMessage directives
    return {
        messages: removals
    };
}
async function aiMessageTrimmer(messages, threshold, model) {
    if (messages.length <= threshold) {
        logger.log(`📭 No trimming needed: ${messages.length} <= ${threshold}`);
        return {
            messages: []
        }; // Return empty messages array instead of empty object
    }
    // Summarize the *earliest* messages beyond the threshold
    const toSummarize = messages.slice(0, messages.length - threshold);
    const summarizationInput = `${toSummarize.map((msg)=>{
        if (msg instanceof _messages.AIMessage) {
            return `Assistant: ${JSON.stringify(msg?.content)}`;
        } else if (msg instanceof _messages.HumanMessage) {
            return `User: ${JSON.stringify(msg?.content)}`;
        } else if (msg instanceof _messages.ToolMessage) {
            return `Tool: ${JSON.stringify(msg?.content)}`;
        } else {
            return '';
        }
    }).join('\n')}`;
    const summaryPrompt = [
        new _messages.SystemMessage({
            content: `You are an AI assistant that summarizes conversations. You are picky, choosing and synthesizing the most important points from the conversation.`
        }),
        new _messages.HumanMessage({
            content: `<task>Summarize the following conversation ${summarizationInput}</task>
<requirement>Only give the output, do not add any filler text.</requirement>`
        })
    ];
    const summaryResult = await model.invoke(summaryPrompt);
    const summaryMsg = new _messages.AIMessage({
        content: `[Summary of earlier chat]: ${summaryResult.content}`
    });
    logger.debug(`summary: ${summaryMsg.content}`);
    // Build deletions for all the old messages we just summarized
    const deletions = toSummarize.map((msg)=>new _messages.RemoveMessage({
            id: msg.id
        }));
    // And then append the summary + the last `threshold` raw messages
    const recent = messages.slice(-threshold);
    console.log(`🗑️ Deleting ${deletions.length} messages to stay under ${threshold} messages`);
    return {
        messages: [
            ...deletions,
            summaryMsg,
            ...recent
        ]
    };
}
const trimmingStrategies = {
    top_k: topKMessageTrimmer,
    ai: aiMessageTrimmer,
    token: tokenMessageTrimmer
};
async function trimMessagesWithStrategy(messages, type, threshold, model) {
    if (!trimmingStrategies[type]) {
        throw new Error(`Unknown trimming strategy: ${type}`);
    }
    const result = await trimmingStrategies[type](messages, threshold, model);
    // Ensure result has a valid messages property
    if (!result || typeof result !== 'object') {
        logger.warn(`Trimming strategy '${type}' returned invalid result, using empty messages array`);
        return {
            messages: []
        };
    }
    if (!Array.isArray(result.messages)) {
        logger.warn(`Trimming strategy '${type}' returned non-array messages, using empty messages array`);
        return {
            messages: []
        };
    }
    return result;
}

//# sourceMappingURL=message-trimmer.js.map