"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelsUserModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const services_module_1 = require("../../../shared/services/services.module");
const entities = require("../entities");
const repositories = require("../repositories");
const user_models_repository_1 = require("../repositories/user-models.repository");
const system_model_key_llm_repository_1 = require("../repositories/system-model-key-llm.repository");
const user_model_key_llm_repository_1 = require("../repositories/user-model-key-llm.repository");
const controllers_1 = require("./controllers");
const services_1 = require("./services");
const user_model_sync_service_1 = require("../services/user-model-sync.service");
const model_discovery_service_1 = require("../services/model-discovery.service");
const pattern_matching_engine_service_1 = require("../services/pattern-matching-engine.service");
const bulk_model_operations_service_1 = require("../services/bulk-model-operations.service");
const api_key_encryption_helper_1 = require("../helpers/api-key-encryption.helper");
const fine_tuning_job_controller_1 = require("./controllers/fine-tuning-job.controller");
let ModelsUserModule = class ModelsUserModule {
};
exports.ModelsUserModule = ModelsUserModule;
exports.ModelsUserModule = ModelsUserModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                entities.ModelRegistry,
                entities.SystemKeyLlm,
                entities.FineTuneHistories,
                entities.UserKeyLlm,
                entities.UserDataFineTune,
                entities.AdminDataFineTune,
                entities.SystemModels,
                entities.UserModels,
                entities.UserModelKeyLlm,
                entities.UserModelFineTune,
                entities.SystemModelKeyLlm,
            ]),
            config_1.ConfigModule,
            services_module_1.ServicesModule,
        ],
        controllers: [
            controllers_1.UserDataFineTuneController,
            controllers_1.UserKeyLlmController,
            controllers_1.UserModelsController,
            fine_tuning_job_controller_1.FineTuningJobController
        ],
        providers: [
            services_1.UserDataFineTuneService,
            services_1.UserKeyLlmService,
            services_1.UserModelsService,
            services_1.FineTuningJobService,
            user_model_sync_service_1.UserModelSyncService,
            model_discovery_service_1.ModelDiscoveryService,
            pattern_matching_engine_service_1.PatternMatchingEngineService,
            bulk_model_operations_service_1.BulkModelOperationsService,
            repositories.ModelRegistryRepository,
            repositories.SystemKeyLlmRepository,
            repositories.FineTuneHistoriesRepository,
            repositories.UserKeyLlmRepository,
            repositories.UserDataFineTuneRepository,
            repositories.AdminDataFineTuneRepository,
            repositories.SystemModelsRepository,
            repositories.UserKeyLlmRepository,
            repositories.UserModelFineTuneRepository,
            user_models_repository_1.UserModelsRepository,
            system_model_key_llm_repository_1.SystemModelKeyLlmRepository,
            user_model_key_llm_repository_1.UserModelKeyLlmRepository,
            api_key_encryption_helper_1.ApiKeyEncryptionHelper,
        ],
        exports: [
            services_1.UserDataFineTuneService,
            services_1.UserKeyLlmService,
            services_1.UserModelsService,
        ],
    })
], ModelsUserModule);
//# sourceMappingURL=models-user.module.js.map