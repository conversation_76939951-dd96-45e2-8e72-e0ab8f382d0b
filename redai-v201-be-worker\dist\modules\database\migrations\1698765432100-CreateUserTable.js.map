{"version": 3, "sources": ["../../../../src/modules/database/migrations/1698765432100-CreateUserTable.ts"], "sourcesContent": ["import { MigrationInterface, QueryRunner, Table } from 'typeorm';\r\n\r\nexport class CreateUserTable1698765432100 implements MigrationInterface {\r\n  public async up(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.createTable(\r\n      new Table({\r\n        name: 'users',\r\n        columns: [\r\n          {\r\n            name: 'id',\r\n            type: 'uuid',\r\n            isPrimary: true,\r\n            generationStrategy: 'uuid',\r\n            default: 'uuid_generate_v4()',\r\n          },\r\n          {\r\n            name: 'fullName',\r\n            type: 'varchar',\r\n            length: '100',\r\n          },\r\n          {\r\n            name: 'email',\r\n            type: 'varchar',\r\n            isUnique: true,\r\n          },\r\n          {\r\n            name: 'password',\r\n            type: 'varchar',\r\n          },\r\n          {\r\n            name: 'isActive',\r\n            type: 'boolean',\r\n            default: false,\r\n          },\r\n          {\r\n            name: 'avatarUrl',\r\n            type: 'varchar',\r\n            isNullable: true,\r\n          },\r\n          {\r\n            name: 'createdAt',\r\n            type: 'timestamp',\r\n            default: 'now()',\r\n          },\r\n          {\r\n            name: 'updatedAt',\r\n            type: 'timestamp',\r\n            default: 'now()',\r\n          },\r\n        ],\r\n      }),\r\n      true,\r\n    );\r\n\r\n    // Tạo extension uuid-ossp nếu chưa có\r\n    await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"`);\r\n  }\r\n\r\n  public async down(queryRunner: QueryRunner): Promise<void> {\r\n    await queryRunner.dropTable('users');\r\n  }\r\n}\r\n"], "names": ["CreateUserTable1698765432100", "up", "queryRunner", "createTable", "Table", "name", "columns", "type", "isPrimary", "generationStrategy", "default", "length", "isUnique", "isNullable", "query", "down", "dropTable"], "mappings": ";;;;+BAEaA;;;eAAAA;;;yBAF0C;AAEhD,IAAA,AAAMA,+BAAN,MAAMA;IACX,MAAaC,GAAGC,WAAwB,EAAiB;QACvD,MAAMA,YAAYC,WAAW,CAC3B,IAAIC,cAAK,CAAC;YACRC,MAAM;YACNC,SAAS;gBACP;oBACED,MAAM;oBACNE,MAAM;oBACNC,WAAW;oBACXC,oBAAoB;oBACpBC,SAAS;gBACX;gBACA;oBACEL,MAAM;oBACNE,MAAM;oBACNI,QAAQ;gBACV;gBACA;oBACEN,MAAM;oBACNE,MAAM;oBACNK,UAAU;gBACZ;gBACA;oBACEP,MAAM;oBACNE,MAAM;gBACR;gBACA;oBACEF,MAAM;oBACNE,MAAM;oBACNG,SAAS;gBACX;gBACA;oBACEL,MAAM;oBACNE,MAAM;oBACNM,YAAY;gBACd;gBACA;oBACER,MAAM;oBACNE,MAAM;oBACNG,SAAS;gBACX;gBACA;oBACEL,MAAM;oBACNE,MAAM;oBACNG,SAAS;gBACX;aACD;QACH,IACA;QAGF,sCAAsC;QACtC,MAAMR,YAAYY,KAAK,CAAC,CAAC,0CAA0C,CAAC;IACtE;IAEA,MAAaC,KAAKb,WAAwB,EAAiB;QACzD,MAAMA,YAAYc,SAAS,CAAC;IAC9B;AACF"}