"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddStrategyToAgent1716097508000 = void 0;
class AddStrategyToAgent1716097508000 {
    async up(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE "agents" 
      ADD COLUMN IF NOT EXISTS "strategy_id" VARCHAR(100) NULL
    `);
        await queryRunner.query(`
      ALTER TABLE "agents" 
      ADD COLUMN IF NOT EXISTS "strategy_config" JSONB NULL DEFAULT '{}'
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE "agents" 
      DROP COLUMN IF EXISTS "strategy_config"
    `);
        await queryRunner.query(`
      ALTER TABLE "agents" 
      DROP COLUMN IF EXISTS "strategy_id"
    `);
    }
}
exports.AddStrategyToAgent1716097508000 = AddStrategyToAgent1716097508000;
//# sourceMappingURL=1716097508000-AddStrategyToAgent.js.map