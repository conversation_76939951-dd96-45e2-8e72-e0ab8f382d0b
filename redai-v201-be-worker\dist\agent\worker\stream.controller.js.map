{"version": 3, "sources": ["../../../src/agent/worker/stream.controller.ts"], "sourcesContent": ["// src/stream/stream.controller.ts\r\nimport {\r\n  BadRequestException,\r\n  Controller,\r\n  Get,\r\n  HttpCode,\r\n  Post,\r\n  Query,\r\n  Req,\r\n  Res,\r\n} from '@nestjs/common';\r\nimport { Request, Response } from 'express';\r\nimport { RedisService } from '../../infra';\r\nimport { nanoid } from 'nanoid';\r\nimport { workflow } from '../system/core';\r\n\r\n@Controller('stream')\r\nexport class StreamController {\r\n  constructor(private readonly redisService: RedisService) {}\r\n\r\n  @Post('cancel')\r\n  @HttpCode(204)\r\n  async cancelStream(@Query('threadId') threadId: string) {\r\n    if (!threadId) {\r\n      throw new BadRequestException('threadId is required');\r\n    }\r\n    const client = this.redisService.getRawClient();\r\n    // publish on cancel:<threadId> channel\r\n    await client.publish(`cancel:${threadId}`, '');\r\n  }\r\n\r\n  @Get('events')\r\n  async streamEvents(\r\n    @Req() req: Request,\r\n    @Res() res: Response,\r\n    @Query('threadId') threadId: string,\r\n  ) {\r\n    if (!threadId) {\r\n      throw new BadRequestException('threadId is required');\r\n    }\r\n    const streamKey = `agent_stream:${threadId}`;\r\n\r\n    const client = this.redisService.getRawClient();\r\n    const groupName = `sse-group:${threadId}`;\r\n    const consumerId = `consumer:${nanoid()}`;\r\n\r\n    const subscriber = client.duplicate();\r\n\r\n    // Create (or ignore if exists) the consumer group\r\n    try {\r\n      await client.xgroup('CREATE', streamKey, groupName, '0', 'MKSTREAM');\r\n    } catch (e: any) {\r\n      if (!/BUSYGROUP/.test(e.message)) throw e;\r\n    }\r\n\r\n    // SSE headers\r\n    res.set({\r\n      'Content-Type': 'text/event-stream',\r\n      'Cache-Control': 'no-cache, no-transform',\r\n      Connection: 'keep-alive',\r\n      'Retry-After': '2',\r\n    });\r\n    res.flushHeaders();\r\n\r\n    // Clean up on disconnect\r\n    req.on('close', async () => {\r\n      await subscriber.unsubscribe(streamKey);\r\n      res.end();\r\n    });\r\n\r\n    // helper to parse field arrays\r\n    const parseFields = (fields: string[]): Record<string, any> => {\r\n      const obj: Record<string, any> = {};\r\n      for (let i = 0; i < fields.length; i += 2) {\r\n        const key = fields[i];\r\n        try {\r\n          obj[key] = JSON.parse(fields[i + 1]);\r\n        } catch {\r\n          obj[key] = fields[i + 1];\r\n        }\r\n      }\r\n      return obj;\r\n    };\r\n\r\n    // Read only new messages (\">\") for this group\r\n    const readNew = async () => {\r\n      const chunks = await client.xreadgroup(\r\n        'GROUP',\r\n        groupName,\r\n        consumerId,\r\n        'COUNT',\r\n        20,\r\n        'STREAMS',\r\n        streamKey,\r\n        '>',\r\n      );\r\n      if (!chunks) return;\r\n      // @ts-ignore\r\n      const [[, messages]] = chunks;\r\n      for (const [id, fields] of messages) {\r\n        const payload = parseFields(fields);\r\n\r\n        // include SSE id header so client could reconnect with Last-Event-ID\r\n        res.write(`id: ${id}\\n`);\r\n        res.write(`data: ${JSON.stringify(payload)}\\n\\n`);\r\n\r\n        if (payload.event === 'llm_stream_end') {\r\n          res.write(`event: end\\ndata: {}\\n\\n`);\r\n          await subscriber.unsubscribe(streamKey);\r\n          res.end();\r\n          return;\r\n        }\r\n      }\r\n    };\r\n\r\n    // Subscribe to pub/sub notifications on this thread’s channel\r\n    await subscriber.subscribe(streamKey);\r\n    subscriber.on('message', async () => {\r\n      await readNew();\r\n    });\r\n\r\n    // Initial catch-up: in case events arrived before we subscribed\r\n    await readNew();\r\n  }\r\n}\r\n"], "names": ["StreamController", "cancelStream", "threadId", "BadRequestException", "client", "redisService", "getRawClient", "publish", "streamEvents", "req", "res", "streamKey", "groupName", "consumerId", "nanoid", "subscriber", "duplicate", "xgroup", "e", "test", "message", "set", "Connection", "flushHeaders", "on", "unsubscribe", "end", "parseFields", "fields", "obj", "i", "length", "key", "JSON", "parse", "readNew", "chunks", "xreadgroup", "messages", "id", "payload", "write", "stringify", "event", "subscribe", "constructor"], "mappings": "AAAA,kCAAkC;;;;;+BAiBrBA;;;eAAAA;;;wBAPN;yBAC2B;uBACL;wBACN;;;;;;;;;;;;;;;AAIhB,IAAA,AAAMA,mBAAN,MAAMA;IAGX,MAEMC,aAAa,AAAmBC,QAAgB,EAAE;QACtD,IAAI,CAACA,UAAU;YACb,MAAM,IAAIC,2BAAmB,CAAC;QAChC;QACA,MAAMC,SAAS,IAAI,CAACC,YAAY,CAACC,YAAY;QAC7C,uCAAuC;QACvC,MAAMF,OAAOG,OAAO,CAAC,CAAC,OAAO,EAAEL,UAAU,EAAE;IAC7C;IAEA,MACMM,aACJ,AAAOC,GAAY,EACnB,AAAOC,GAAa,EACpB,AAAmBR,QAAgB,EACnC;QACA,IAAI,CAACA,UAAU;YACb,MAAM,IAAIC,2BAAmB,CAAC;QAChC;QACA,MAAMQ,YAAY,CAAC,aAAa,EAAET,UAAU;QAE5C,MAAME,SAAS,IAAI,CAACC,YAAY,CAACC,YAAY;QAC7C,MAAMM,YAAY,CAAC,UAAU,EAAEV,UAAU;QACzC,MAAMW,aAAa,CAAC,SAAS,EAAEC,IAAAA,cAAM,KAAI;QAEzC,MAAMC,aAAaX,OAAOY,SAAS;QAEnC,kDAAkD;QAClD,IAAI;YACF,MAAMZ,OAAOa,MAAM,CAAC,UAAUN,WAAWC,WAAW,KAAK;QAC3D,EAAE,OAAOM,GAAQ;YACf,IAAI,CAAC,YAAYC,IAAI,CAACD,EAAEE,OAAO,GAAG,MAAMF;QAC1C;QAEA,cAAc;QACdR,IAAIW,GAAG,CAAC;YACN,gBAAgB;YAChB,iBAAiB;YACjBC,YAAY;YACZ,eAAe;QACjB;QACAZ,IAAIa,YAAY;QAEhB,yBAAyB;QACzBd,IAAIe,EAAE,CAAC,SAAS;YACd,MAAMT,WAAWU,WAAW,CAACd;YAC7BD,IAAIgB,GAAG;QACT;QAEA,+BAA+B;QAC/B,MAAMC,cAAc,CAACC;YACnB,MAAMC,MAA2B,CAAC;YAClC,IAAK,IAAIC,IAAI,GAAGA,IAAIF,OAAOG,MAAM,EAAED,KAAK,EAAG;gBACzC,MAAME,MAAMJ,MAAM,CAACE,EAAE;gBACrB,IAAI;oBACFD,GAAG,CAACG,IAAI,GAAGC,KAAKC,KAAK,CAACN,MAAM,CAACE,IAAI,EAAE;gBACrC,EAAE,OAAM;oBACND,GAAG,CAACG,IAAI,GAAGJ,MAAM,CAACE,IAAI,EAAE;gBAC1B;YACF;YACA,OAAOD;QACT;QAEA,8CAA8C;QAC9C,MAAMM,UAAU;YACd,MAAMC,SAAS,MAAMhC,OAAOiC,UAAU,CACpC,SACAzB,WACAC,YACA,SACA,IACA,WACAF,WACA;YAEF,IAAI,CAACyB,QAAQ;YACb,aAAa;YACb,MAAM,CAAC,GAAGE,SAAS,CAAC,GAAGF;YACvB,KAAK,MAAM,CAACG,IAAIX,OAAO,IAAIU,SAAU;gBACnC,MAAME,UAAUb,YAAYC;gBAE5B,qEAAqE;gBACrElB,IAAI+B,KAAK,CAAC,CAAC,IAAI,EAAEF,GAAG,EAAE,CAAC;gBACvB7B,IAAI+B,KAAK,CAAC,CAAC,MAAM,EAAER,KAAKS,SAAS,CAACF,SAAS,IAAI,CAAC;gBAEhD,IAAIA,QAAQG,KAAK,KAAK,kBAAkB;oBACtCjC,IAAI+B,KAAK,CAAC,CAAC,wBAAwB,CAAC;oBACpC,MAAM1B,WAAWU,WAAW,CAACd;oBAC7BD,IAAIgB,GAAG;oBACP;gBACF;YACF;QACF;QAEA,8DAA8D;QAC9D,MAAMX,WAAW6B,SAAS,CAACjC;QAC3BI,WAAWS,EAAE,CAAC,WAAW;YACvB,MAAMW;QACR;QAEA,gEAAgE;QAChE,MAAMA;IACR;IAzGAU,YAAY,AAAiBxC,YAA0B,CAAE;aAA5BA,eAAAA;IAA6B;AA0G5D"}