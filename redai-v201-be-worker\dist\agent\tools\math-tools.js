"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get additionTool () {
        return additionTool;
    },
    get divisionTool () {
        return divisionTool;
    },
    get mathTools () {
        return mathTools;
    },
    get modularTool () {
        return modularTool;
    },
    get multiplicationTool () {
        return multiplicationTool;
    },
    get subtractionTool () {
        return subtractionTool;
    }
});
const _tools = require("@langchain/core/tools");
const _zod = require("zod");
const additionTool = (0, _tools.tool)(async ({ a, b })=>{
    return `${a} + ${b} = ${a + b}`;
}, {
    name: 'addition',
    description: 'Add two numbers together',
    schema: _zod.z.object({
        a: _zod.z.number().describe('The first number'),
        b: _zod.z.number().describe('The second number')
    })
});
const subtractionTool = (0, _tools.tool)(async ({ a, b })=>{
    return `${a} - ${b} = ${a - b}`;
}, {
    name: 'subtraction',
    description: 'Subtract the second number from the first',
    schema: _zod.z.object({
        a: _zod.z.number().describe('The first number'),
        b: _zod.z.number().describe('The second number')
    })
});
const multiplicationTool = (0, _tools.tool)(async ({ a, b })=>{
    return `${a} * ${b} = ${a * b}`;
}, {
    name: 'multiplication',
    description: 'Multiply two numbers together',
    schema: _zod.z.object({
        a: _zod.z.number().describe('The first number'),
        b: _zod.z.number().describe('The second number')
    })
});
const divisionTool = (0, _tools.tool)(async ({ a, b })=>{
    if (b === 0) {
        return 'Error: Division by zero is not allowed';
    }
    return `${a} / ${b} = ${a / b}`;
}, {
    name: 'division',
    description: 'Divide the first number by the second',
    schema: _zod.z.object({
        a: _zod.z.number().describe('The first number'),
        b: _zod.z.number().describe('The second number (cannot be zero)')
    })
});
const modularTool = (0, _tools.tool)(async ({ a, b })=>{
    if (b === 0) {
        return 'Error: Modulo by zero is not allowed';
    }
    return `${a} % ${b} = ${a % b}`;
}, {
    name: 'modular',
    description: 'Calculate the remainder when the first number is divided by the second',
    schema: _zod.z.object({
        a: _zod.z.number().describe('The first number'),
        b: _zod.z.number().describe('The second number (cannot be zero)')
    })
});
const mathTools = {
    addition: additionTool,
    subtraction: subtractionTool,
    multiplication: multiplicationTool,
    division: divisionTool,
    modular: modularTool
};

//# sourceMappingURL=math-tools.js.map