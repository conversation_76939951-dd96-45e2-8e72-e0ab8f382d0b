"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "databaseConfig", {
    enumerable: true,
    get: function() {
        return databaseConfig;
    }
});
const _env = require("./env");
const databaseConfig = {
    type: 'postgres',
    host: _env.env.database.DB_HOST,
    port: Number(_env.env.database.DB_PORT),
    username: _env.env.database.DB_USERNAME,
    password: _env.env.database.DB_PASSWORD,
    database: _env.env.database.DB_DATABASE,
    ssl: _env.env.database.DB_SSL ? {
        rejectUnauthorized: false
    } : false,
    entities: [
        __dirname + '/../**/*.entity{.ts,.js}'
    ],
    migrations: [
        __dirname + '/../modules/database/migrations/*{.ts,.js}'
    ],
    synchronize: false,
    logging: _env.env.misc.NODE_ENV !== 'production',
    autoLoadEntities: true,
    migrationsRun: _env.env.misc.NODE_ENV === 'production'
};

//# sourceMappingURL=database.config.js.map