"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AffiliateService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AffiliateService = void 0;
const common_1 = require("@nestjs/common");
const exceptions_1 = require("../../../../common/exceptions");
const errors_1 = require("../../errors");
const typeorm_transactional_1 = require("typeorm-transactional");
let AffiliateService = AffiliateService_1 = class AffiliateService {
    logger = new common_1.Logger(AffiliateService_1.name);
    async createAffiliateOrder(createAffiliateOrderDto) {
        try {
            this.logger.log(`Creating affiliate order for user ${createAffiliateOrderDto.userId} with referrer ${createAffiliateOrderDto.referrerId}`);
            this.logger.log(`Affiliate order created successfully for order ${createAffiliateOrderDto.orderId}`);
        }
        catch (error) {
            this.logger.error(`Error creating affiliate order: ${error.message}`, error.stack);
            if (error instanceof exceptions_1.AppException) {
                throw error;
            }
            throw new exceptions_1.AppException(errors_1.AFFILIATE_ERROR_CODES.ORDER_CREATION_FAILED, 'Lỗi khi tạo đơn hàng affiliate');
        }
    }
    async processCommission(userId, referrerId, amount) {
        try {
            this.logger.log(`Processing commission for referrer ${referrerId} from user ${userId}`);
            this.logger.log(`Commission processed successfully for referrer ${referrerId}`);
        }
        catch (error) {
            this.logger.error(`Error processing commission: ${error.message}`, error.stack);
            if (error instanceof exceptions_1.AppException) {
                throw error;
            }
            throw new exceptions_1.AppException(errors_1.AFFILIATE_ERROR_CODES.COMMISSION_PROCESSING_FAILED, 'Lỗi khi xử lý hoa hồng affiliate');
        }
    }
};
exports.AffiliateService = AffiliateService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AffiliateService.prototype, "createAffiliateOrder", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number]),
    __metadata("design:returntype", Promise)
], AffiliateService.prototype, "processCommission", null);
exports.AffiliateService = AffiliateService = AffiliateService_1 = __decorate([
    (0, common_1.Injectable)()
], AffiliateService);
//# sourceMappingURL=affiliate.service.js.map