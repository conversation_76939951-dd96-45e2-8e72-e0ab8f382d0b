"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateIndividualRuleContractDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateIndividualRuleContractDto {
    name;
    address;
    phone;
    dateOfBirth;
    cccd;
    issuePlace;
    issueDate;
    taxCode;
}
exports.CreateIndividualRuleContractDto = CreateIndividualRuleContractDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Họ tên đầy đủ',
        example: 'Nguyễn Văn A',
        required: true,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Họ tên không được để trống' }),
    (0, class_validator_1.IsString)({ message: 'Họ tên phải là chuỗi' }),
    (0, class_validator_1.MinLength)(2, { message: 'Họ tên phải có ít nhất 2 ký tự' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Họ tên không được vượt quá 255 ký tự' }),
    __metadata("design:type", String)
], CreateIndividualRuleContractDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Địa chỉ',
        example: 'Số 123, Đường ABC, Quận XYZ, TP. Hồ Chí Minh',
        required: true,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Địa chỉ không được để trống' }),
    (0, class_validator_1.IsString)({ message: 'Địa chỉ phải là chuỗi' }),
    (0, class_validator_1.MinLength)(5, { message: 'Địa chỉ phải có ít nhất 5 ký tự' }),
    (0, class_validator_1.MaxLength)(1000, { message: 'Địa chỉ không được vượt quá 1000 ký tự' }),
    __metadata("design:type", String)
], CreateIndividualRuleContractDto.prototype, "address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số điện thoại',
        example: '0912345678',
        required: true,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Số điện thoại không được để trống' }),
    (0, class_validator_1.IsString)({ message: 'Số điện thoại phải là chuỗi' }),
    __metadata("design:type", String)
], CreateIndividualRuleContractDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Ngày sinh (định dạng YYYY-MM-DD)',
        example: '1990-01-01',
        required: true,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Ngày sinh không được để trống' }),
    (0, class_validator_1.IsDateString)({}, { message: 'Ngày sinh phải có định dạng YYYY-MM-DD' }),
    __metadata("design:type", String)
], CreateIndividualRuleContractDto.prototype, "dateOfBirth", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số CCCD/CMND',
        example: '079123456789',
        required: true,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Số CCCD/CMND không được để trống' }),
    (0, class_validator_1.IsString)({ message: 'Số CCCD/CMND phải là chuỗi' }),
    __metadata("design:type", String)
], CreateIndividualRuleContractDto.prototype, "cccd", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nơi cấp CCCD/CMND',
        example: 'Cục Cảnh sát quản lý hành chính về trật tự xã hội',
        required: true,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Nơi cấp CCCD/CMND không được để trống' }),
    (0, class_validator_1.IsString)({ message: 'Nơi cấp CCCD/CMND phải là chuỗi' }),
    __metadata("design:type", String)
], CreateIndividualRuleContractDto.prototype, "issuePlace", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Ngày cấp CCCD/CMND (định dạng YYYY-MM-DD)',
        example: '2020-01-01',
        required: true,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Ngày cấp CCCD/CMND không được để trống' }),
    (0, class_validator_1.IsDateString)({}, { message: 'Ngày cấp CCCD/CMND phải có định dạng YYYY-MM-DD' }),
    __metadata("design:type", String)
], CreateIndividualRuleContractDto.prototype, "issueDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mã số thuế (nếu có)',
        example: '1234567890',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Mã số thuế phải là chuỗi' }),
    __metadata("design:type", String)
], CreateIndividualRuleContractDto.prototype, "taxCode", void 0);
//# sourceMappingURL=create-individual-rule-contract.dto.js.map