"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericPageTemplateResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class GenericPageTemplateResponseDto {
    id;
    name;
    description;
    category;
    thumbnail;
    tags;
    config;
    createdAt;
    updatedAt;
    createdBy;
    updatedBy;
}
exports.GenericPageTemplateResponseDto = GenericPageTemplateResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của mẫu trang',
        example: 'e2f3a4b5-c6d7-4e5f-8a9b-0c1d2e3f4a5b',
    }),
    __metadata("design:type", String)
], GenericPageTemplateResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên của mẫu trang',
        example: 'Mẫu form liên hệ',
    }),
    __metadata("design:type", String)
], GenericPageTemplateResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả về mẫu trang',
        example: 'Mẫu form liên hệ cơ bản với các trường thông tin liên hệ',
        nullable: true,
    }),
    __metadata("design:type", Object)
], GenericPageTemplateResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh mục của mẫu trang',
        example: 'Form',
        nullable: true,
    }),
    __metadata("design:type", Object)
], GenericPageTemplateResponseDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'URL hình thu nhỏ minh họa cho mẫu trang',
        example: '/assets/images/templates/contact-form.jpg',
        nullable: true,
    }),
    __metadata("design:type", Object)
], GenericPageTemplateResponseDto.prototype, "thumbnail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách tag cho mẫu trang',
        example: ['form', 'liên hệ', 'cơ bản'],
        type: [String],
    }),
    __metadata("design:type", Array)
], GenericPageTemplateResponseDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cấu hình mẫu trang dạng JSON',
        example: {
            formId: 'contact-form-template',
            title: 'Liên hệ với chúng tôi',
            subtitle: 'Hãy để lại thông tin, chúng tôi sẽ liên hệ lại với bạn',
            groups: [],
        },
    }),
    __metadata("design:type", Object)
], GenericPageTemplateResponseDto.prototype, "config", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời điểm tạo mẫu trang (Unix timestamp)',
        example: 1672918200000,
    }),
    __metadata("design:type", Number)
], GenericPageTemplateResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời điểm cập nhật mẫu trang gần nhất (Unix timestamp)',
        example: 1672918200000,
    }),
    __metadata("design:type", Number)
], GenericPageTemplateResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của người tạo mẫu trang',
        example: 'admin-user-id',
    }),
    __metadata("design:type", String)
], GenericPageTemplateResponseDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của người cập nhật mẫu trang gần nhất',
        example: 'admin-user-id',
    }),
    __metadata("design:type", String)
], GenericPageTemplateResponseDto.prototype, "updatedBy", void 0);
//# sourceMappingURL=generic-page-template-response.dto.js.map