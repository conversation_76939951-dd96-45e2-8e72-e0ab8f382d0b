"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var StreamController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const redis_service_1 = require("../../../shared/services/redis.service");
const nanoid_1 = require("nanoid");
let StreamController = StreamController_1 = class StreamController {
    redisService;
    logger = new common_1.Logger(StreamController_1.name);
    constructor(redisService) {
        this.redisService = redisService;
    }
    async streamEvents(req, res, threadId, fromMessageId) {
        if (!threadId || threadId.trim() === '') {
            throw new common_1.BadRequestException('threadId is required and cannot be empty');
        }
        this.logger.log(`🔥 Starting chat SSE stream for thread ${threadId}`);
        try {
            res.set({
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache, no-transform',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Cache-Control',
                'X-Accel-Buffering': 'no',
            });
            const resumeMode = fromMessageId ? 'resume' : 'unconsumed';
            res.write(`event: connected\n`);
            res.write(`data: {"threadId":"${threadId}","from":"${fromMessageId || 'unconsumed'}","timestamp":${Date.now()},"status":"connected","mode":"${resumeMode}"}\n\n`);
            this.logger.log(`✅ Chat SSE headers set for thread ${threadId} (${resumeMode}: ${fromMessageId || 'unconsumed'})`);
            const streamKey = `agent_stream:${threadId}`;
            const groupName = `sse-group:${threadId}:${(0, nanoid_1.nanoid)()}`;
            const consumerId = `consumer-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
            this.logger.log(`🔥 Creating consumer group ${groupName} for stream ${streamKey}`);
            try {
                const startPosition = fromMessageId || '$';
                await this.redisService.createConsumerGroup(streamKey, groupName, startPosition);
                this.logger.log(`✅ Consumer group ${groupName} created successfully (starting from: ${startPosition})`);
                await this.consumeStreamMessages(res, streamKey, groupName, consumerId, threadId, fromMessageId);
            }
            catch (error) {
                this.logger.error(`💥 Failed to create consumer group ${groupName}:`, error);
                if (!res.destroyed) {
                    res.write(`event: error\n`);
                    res.write(`data: {"error":"Failed to initialize stream consumer","threadId":"${threadId}"}\n\n`);
                    res.end();
                }
                return;
            }
            req.on('close', () => {
                this.logger.log(`🔌 Client disconnected from chat thread ${threadId}`);
                this.cleanupConsumerGroup(streamKey, groupName);
            });
            req.on('error', (error) => {
                this.logger.error(`❌ Chat SSE error for thread ${threadId}:`, error);
                this.cleanupConsumerGroup(streamKey, groupName);
                if (!res.destroyed) {
                    res.end();
                }
            });
        }
        catch (error) {
            this.logger.error(`💥 Failed to establish chat SSE for thread ${threadId}:`, error);
            if (!res.headersSent) {
                throw error;
            }
        }
    }
    async consumeStreamMessages(res, streamKey, groupName, consumerId, threadId, _fromMessageId) {
        this.logger.log(`🔄 Starting stream consumption for thread ${threadId} (pub/sub + initial read)`);
        const client = this.redisService.getDuplicateClient();
        const subscriber = client.duplicate();
        const parseFields = (fields) => {
            const obj = {};
            for (let i = 0; i < fields.length; i += 2) {
                const key = fields[i];
                try {
                    obj[key] = JSON.parse(fields[i + 1]);
                }
                catch {
                    obj[key] = fields[i + 1];
                }
            }
            return obj;
        };
        const readNew = async () => {
            try {
                const chunks = await client.xreadgroup('GROUP', groupName, consumerId, 'COUNT', 20, 'STREAMS', streamKey, '>');
                if (!chunks)
                    return;
                const [[, messages]] = chunks;
                for (const [id, fields] of messages) {
                    const payload = parseFields(fields);
                    res.write(`id: ${id}\n`);
                    res.write(`data: ${JSON.stringify(payload)}\n\n`);
                    this.logger.debug(`� Sent message ${id} for thread ${threadId}: ${payload.event}`);
                    if (payload.event === 'llm_stream_end') {
                        this.logger.log(`🏁 Stream ended for thread ${threadId}`);
                        res.write(`event: end\n`);
                        res.write(`data: ${JSON.stringify(payload.data || {})}\n\n`);
                        await subscriber.unsubscribe(streamKey);
                        res.end();
                        return;
                    }
                    if (payload.event === 'stream_error') {
                        this.logger.error(`💥 Stream error for thread ${threadId}:`, payload.data);
                        res.write(`event: error\n`);
                        res.write(`data: ${JSON.stringify(payload.data)}\n\n`);
                    }
                }
            }
            catch (error) {
                this.logger.error(`💥 Error reading new messages for thread ${threadId}:`, error);
                if (!res.destroyed) {
                    res.write(`event: error\n`);
                    res.write(`data: {"error":"Stream reading error","threadId":"${threadId}"}\n\n`);
                    res.end();
                }
            }
        };
        try {
            await readNew();
            this.logger.log(`✅ Initial catch-up complete for thread ${threadId}`);
            await subscriber.subscribe(streamKey);
            subscriber.on('message', async () => {
                await readNew();
            });
            res.on('close', async () => {
                this.logger.log(`🛑 SSE connection closed for thread ${threadId}`);
                await subscriber.unsubscribe(streamKey);
                subscriber.disconnect();
            });
        }
        catch (error) {
            this.logger.error(`💥 Error setting up stream consumption for thread ${threadId}:`, error);
            if (!res.destroyed) {
                res.write(`event: error\n`);
                res.write(`data: {"error":"Setup error","threadId":"${threadId}"}\n\n`);
                res.end();
            }
        }
    }
    async cleanupConsumerGroup(streamKey, groupName) {
        try {
            await this.redisService.deleteConsumerGroup(streamKey, groupName);
            this.logger.log(`🧹 Cleaned up consumer group ${groupName} for stream ${streamKey}`);
        }
        catch (error) {
            this.logger.error(`💥 Error cleaning up consumer group ${groupName}:`, {
                message: error.message,
                stack: error.stack,
                name: error.name,
                groupName,
                streamKey,
                error: error,
            });
        }
    }
    getHealth() {
        return {
            status: 'healthy',
            timestamp: Date.now(),
            service: 'chat-streaming',
        };
    }
};
exports.StreamController = StreamController;
__decorate([
    (0, common_1.Get)('events/:threadId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Stream chat events via Server-Sent Events',
        description: 'Establishes SSE connection to stream real-time chat events for a specific thread. Resumes from last unread position.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'threadId',
        description: 'Chat thread ID to stream events for',
        example: 'thread_123456',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'from',
        description: 'Optional message ID to resume from. If not provided, starts from latest.',
        example: '1749266123456-0',
        required: false,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'SSE stream established successfully',
        headers: {
            'Content-Type': { description: 'text/event-stream' },
            'Cache-Control': { description: 'no-cache' },
            'Connection': { description: 'keep-alive' },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Invalid threadId parameter',
    }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __param(2, (0, common_1.Param)('threadId')),
    __param(3, (0, common_1.Query)('from')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, String, String]),
    __metadata("design:returntype", Promise)
], StreamController.prototype, "streamEvents", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({
        summary: 'Health check for chat streaming service',
        description: 'Returns the health status of the chat streaming service',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Service is healthy',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Object)
], StreamController.prototype, "getHealth", null);
exports.StreamController = StreamController = StreamController_1 = __decorate([
    (0, swagger_1.ApiTags)('Chat'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Controller)('chat/stream'),
    __metadata("design:paramtypes", [redis_service_1.RedisService])
], StreamController);
//# sourceMappingURL=stream.controller.js.map