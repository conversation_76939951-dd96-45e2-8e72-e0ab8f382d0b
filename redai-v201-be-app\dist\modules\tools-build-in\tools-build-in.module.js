"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolsBuildInModule = void 0;
const axios_1 = require("@nestjs/axios");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const s3_service_1 = require("../../shared/services/s3.service");
const controllers_1 = require("./controllers");
const services_1 = require("./services");
const guards_1 = require("./guards");
const utils_1 = require("./utils");
let ToolsBuildInModule = class ToolsBuildInModule {
};
exports.ToolsBuildInModule = ToolsBuildInModule;
exports.ToolsBuildInModule = ToolsBuildInModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([]),
            axios_1.HttpModule,
            config_1.ConfigModule
        ],
        controllers: [
            controllers_1.WebsiteController,
            controllers_1.AgentToolsController,
        ],
        providers: [
            services_1.WebsiteService,
            services_1.AddressService,
            services_1.ProductService,
            services_1.ConversionService,
            services_1.SendService,
            s3_service_1.S3Service,
            guards_1.ApiKeyAuthGuard,
            services_1.AgentToolsService,
            utils_1.ApiKeyUtil
        ],
        exports: [
            services_1.WebsiteService,
            services_1.AddressService,
            services_1.ProductService,
            services_1.ConversionService,
            services_1.SendService,
            s3_service_1.S3Service,
            guards_1.ApiKeyAuthGuard,
            services_1.AgentToolsService,
            utils_1.ApiKeyUtil
        ],
    })
], ToolsBuildInModule);
//# sourceMappingURL=tools-build-in.module.js.map