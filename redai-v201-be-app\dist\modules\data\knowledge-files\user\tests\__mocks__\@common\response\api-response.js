"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiResponse = void 0;
const common_1 = require("@nestjs/common");
class ApiResponse {
    code;
    message;
    result;
    constructor(result, message = 'Success', code = common_1.HttpStatus.OK) {
        this.code = code;
        this.message = message;
        this.result = result;
    }
    static success(data, message = 'Success') {
        return new ApiResponse(data, message, common_1.HttpStatus.OK);
    }
    static created(data, message = 'Created Successfully') {
        return new ApiResponse(data, message, common_1.HttpStatus.CREATED);
    }
    static updated(data, message = 'Updated Successfully') {
        return new ApiResponse(data, message, common_1.HttpStatus.OK);
    }
    static deleted(data = null, message = 'Deleted Successfully', useNoContent = false) {
        const status = useNoContent ? common_1.HttpStatus.NO_CONTENT : common_1.HttpStatus.OK;
        return new ApiResponse(data, message, status);
    }
    static paginated(result, message = 'Success') {
        return new ApiResponse(result, message, common_1.HttpStatus.OK);
    }
    static noContent(message = 'No Content') {
        return new ApiResponse(null, message, common_1.HttpStatus.NO_CONTENT);
    }
}
exports.ApiResponse = ApiResponse;
//# sourceMappingURL=api-response.js.map