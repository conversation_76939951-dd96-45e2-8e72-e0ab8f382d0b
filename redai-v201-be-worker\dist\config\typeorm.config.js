"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get dataSourceOptions () {
        return dataSourceOptions;
    },
    get default () {
        return _default;
    }
});
const _typeorm = require("typeorm");
const _env = require("./env");
const dataSourceOptions = {
    type: 'postgres',
    host: _env.env.database.DB_HOST,
    port: Number(_env.env.database.DB_PORT),
    username: _env.env.database.DB_USERNAME,
    password: _env.env.database.DB_PASSWORD,
    database: _env.env.database.DB_DATABASE,
    entities: [
        __dirname + '/../**/*.entity{.ts,.js}'
    ],
    migrations: [
        __dirname + '/../modules/database/migrations/*{.ts,.js}'
    ],
    synchronize: false,
    logging: true
};
const dataSource = new _typeorm.DataSource(dataSourceOptions);
const _default = dataSource;

//# sourceMappingURL=typeorm.config.js.map