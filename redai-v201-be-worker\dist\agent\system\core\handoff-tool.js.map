{"version": 3, "sources": ["../../../../src/agent/system/core/handoff-tool.ts"], "sourcesContent": ["import { z } from 'zod';\r\nimport { tool } from '@langchain/core/tools';\r\nimport { AIMessage, ToolMessage } from '@langchain/core/messages';\r\nimport { Command, getCurrentTaskInput } from '@langchain/langgraph';\r\nimport { AgentState } from './react-agent-executor';\r\nimport { DynamicStructuredTool } from '@langchain/core/dist/tools';\r\n\r\n// Factory to create Zod schema based on available worker IDs\r\nconst createHandoffSchema = (agentIds: string[]) =>\r\n  z.object({\r\n    agentId: z\r\n      .string()\r\n      .nonempty()\r\n      .refine((id) => agentIds.includes(id), { message: 'Invalid agentId' })\r\n      .describe(`ID of the worker agent to handoff to, must be of the following values: \r\n${agentIds.join('\\n')}`),\r\n    taskDescription: z\r\n      .string()\r\n      .nonempty()\r\n      .describe('Optional description for the worker worker'),\r\n  });\r\n\r\nexport function createHandoffTool(\r\n  workerAgentIds: string[],\r\n): DynamicStructuredTool {\r\n  if (workerAgentIds.length === 0) {\r\n    throw new Error('createHandoffTool requires at least one workerAgentId');\r\n  }\r\n\r\n  const schema = createHandoffSchema(workerAgentIds);\r\n  const toolName = 'handoff_to_worker_agents';\r\n\r\n  const handler = async (args: z.infer<typeof schema>, config: any) => {\r\n    const state = getCurrentTaskInput() as AgentState;\r\n    const { agentId, taskDescription } = args;\r\n    const toolMessage = new ToolMessage({\r\n      content: `Successfully transferred to ${agentId}`,\r\n      name: toolName,\r\n      tool_call_id: config.toolCall.id,\r\n      response_metadata: {\r\n        invoker: state.activeAgent,\r\n      },\r\n    });\r\n\r\n    const aiMessage = new AIMessage({\r\n      content: `<task-from-supervisor>\r\n${taskDescription}\r\n<task-from-supervisor>`,\r\n      response_metadata: {\r\n        invoker: agentId,\r\n      },\r\n    });\r\n\r\n    // Emit Command to switch context\r\n    return new Command({\r\n      goto: 'worker',\r\n      graph: Command.PARENT,\r\n      update: {\r\n        messages: state.messages.concat(toolMessage, aiMessage),\r\n        activeAgent: agentId,\r\n        isSupervisor: false,\r\n      },\r\n    });\r\n  };\r\n\r\n  return tool(handler, {\r\n    name: toolName,\r\n    description: 'Handoff the task to a worker worker',\r\n    schema,\r\n  });\r\n}\r\n"], "names": ["createHandoffTool", "createHandoffSchema", "agentIds", "z", "object", "agentId", "string", "nonempty", "refine", "id", "includes", "message", "describe", "join", "taskDescription", "workerAgentIds", "length", "Error", "schema", "toolName", "handler", "args", "config", "state", "getCurrentTaskInput", "toolMessage", "ToolMessage", "content", "name", "tool_call_id", "toolCall", "response_metadata", "invoker", "activeAgent", "aiMessage", "AIMessage", "Command", "goto", "graph", "PARENT", "update", "messages", "concat", "isSupervisor", "tool", "description"], "mappings": ";;;;+BAsBgBA;;;eAAAA;;;qBAtBE;uBACG;0BACkB;2BACM;AAI7C,6DAA6D;AAC7D,MAAMC,sBAAsB,CAACC,WAC3BC,MAAC,CAACC,MAAM,CAAC;QACPC,SAASF,MAAC,CACPG,MAAM,GACNC,QAAQ,GACRC,MAAM,CAAC,CAACC,KAAOP,SAASQ,QAAQ,CAACD,KAAK;YAAEE,SAAS;QAAkB,GACnEC,QAAQ,CAAC,CAAC;AACjB,EAAEV,SAASW,IAAI,CAAC,OAAO;QACnBC,iBAAiBX,MAAC,CACfG,MAAM,GACNC,QAAQ,GACRK,QAAQ,CAAC;IACd;AAEK,SAASZ,kBACde,cAAwB;IAExB,IAAIA,eAAeC,MAAM,KAAK,GAAG;QAC/B,MAAM,IAAIC,MAAM;IAClB;IAEA,MAAMC,SAASjB,oBAAoBc;IACnC,MAAMI,WAAW;IAEjB,MAAMC,UAAU,OAAOC,MAA8BC;QACnD,MAAMC,QAAQC,IAAAA,8BAAmB;QACjC,MAAM,EAAEnB,OAAO,EAAES,eAAe,EAAE,GAAGO;QACrC,MAAMI,cAAc,IAAIC,qBAAW,CAAC;YAClCC,SAAS,CAAC,4BAA4B,EAAEtB,SAAS;YACjDuB,MAAMT;YACNU,cAAcP,OAAOQ,QAAQ,CAACrB,EAAE;YAChCsB,mBAAmB;gBACjBC,SAAST,MAAMU,WAAW;YAC5B;QACF;QAEA,MAAMC,YAAY,IAAIC,mBAAS,CAAC;YAC9BR,SAAS,CAAC;AAChB,EAAEb,gBAAgB;sBACI,CAAC;YACjBiB,mBAAmB;gBACjBC,SAAS3B;YACX;QACF;QAEA,iCAAiC;QACjC,OAAO,IAAI+B,kBAAO,CAAC;YACjBC,MAAM;YACNC,OAAOF,kBAAO,CAACG,MAAM;YACrBC,QAAQ;gBACNC,UAAUlB,MAAMkB,QAAQ,CAACC,MAAM,CAACjB,aAAaS;gBAC7CD,aAAa5B;gBACbsC,cAAc;YAChB;QACF;IACF;IAEA,OAAOC,IAAAA,WAAI,EAACxB,SAAS;QACnBQ,MAAMT;QACN0B,aAAa;QACb3B;IACF;AACF"}