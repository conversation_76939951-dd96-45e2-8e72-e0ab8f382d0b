"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SmsResponseDto = exports.EmailResponseDto = exports.SendResultResponseDto = exports.RecipientResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class RecipientResponseDto {
    email;
    phone;
    name;
    userId;
    convertCustomerId;
}
exports.RecipientResponseDto = RecipientResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email người nhận',
        example: '<EMAIL>',
        nullable: true
    }),
    __metadata("design:type", String)
], RecipientResponseDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số điện thoại người nhận',
        example: '0987654321',
        nullable: true
    }),
    __metadata("design:type", String)
], RecipientResponseDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên người nhận',
        example: 'Nguyễn Văn A',
        nullable: true
    }),
    __metadata("design:type", String)
], RecipientResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID người nhận (nếu là người dùng trong hệ thống)',
        example: 1,
        nullable: true
    }),
    __metadata("design:type", Number)
], RecipientResponseDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID khách hàng chuyển đổi',
        example: 2,
        nullable: true
    }),
    __metadata("design:type", Number)
], RecipientResponseDto.prototype, "convertCustomerId", void 0);
class SendResultResponseDto {
    success;
    message;
    messageId;
    sentAt;
    successfulRecipients;
    failedRecipients;
}
exports.SendResultResponseDto = SendResultResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái thành công',
        example: true
    }),
    __metadata("design:type", Boolean)
], SendResultResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thông báo',
        example: 'Gửi email thành công'
    }),
    __metadata("design:type", String)
], SendResultResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của tin nhắn đã gửi',
        example: 'msg_123456789',
        nullable: true
    }),
    __metadata("design:type", String)
], SendResultResponseDto.prototype, "messageId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian gửi',
        example: 1672531200000,
        nullable: true
    }),
    __metadata("design:type", Number)
], SendResultResponseDto.prototype, "sentAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách người nhận thành công',
        type: [RecipientResponseDto],
        nullable: true
    }),
    __metadata("design:type", Array)
], SendResultResponseDto.prototype, "successfulRecipients", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách người nhận thất bại',
        type: [RecipientResponseDto],
        nullable: true
    }),
    __metadata("design:type", Array)
], SendResultResponseDto.prototype, "failedRecipients", void 0);
class EmailResponseDto {
    id;
    recipients;
    subject;
    status;
    sentAt;
    openedAt;
    openCount;
}
exports.EmailResponseDto = EmailResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của email',
        example: 'email_123456789'
    }),
    __metadata("design:type", String)
], EmailResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Người nhận',
        type: [RecipientResponseDto]
    }),
    __metadata("design:type", Array)
], EmailResponseDto.prototype, "recipients", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tiêu đề email',
        example: 'Thông báo khuyến mãi tháng 12'
    }),
    __metadata("design:type", String)
], EmailResponseDto.prototype, "subject", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái gửi',
        example: 'delivered'
    }),
    __metadata("design:type", String)
], EmailResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian gửi',
        example: 1672531200000
    }),
    __metadata("design:type", Number)
], EmailResponseDto.prototype, "sentAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian mở email',
        example: 1672531800000,
        nullable: true
    }),
    __metadata("design:type", Number)
], EmailResponseDto.prototype, "openedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số lần mở',
        example: 2,
        nullable: true
    }),
    __metadata("design:type", Number)
], EmailResponseDto.prototype, "openCount", void 0);
class SmsResponseDto {
    id;
    recipients;
    content;
    status;
    sentAt;
    type;
}
exports.SmsResponseDto = SmsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của SMS',
        example: 'sms_123456789'
    }),
    __metadata("design:type", String)
], SmsResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Người nhận',
        type: [RecipientResponseDto]
    }),
    __metadata("design:type", Array)
], SmsResponseDto.prototype, "recipients", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nội dung SMS',
        example: 'Mã xác nhận của bạn là 123456'
    }),
    __metadata("design:type", String)
], SmsResponseDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái gửi',
        example: 'delivered'
    }),
    __metadata("design:type", String)
], SmsResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian gửi',
        example: 1672531200000
    }),
    __metadata("design:type", Number)
], SmsResponseDto.prototype, "sentAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại SMS',
        example: 'otp',
        nullable: true
    }),
    __metadata("design:type", String)
], SmsResponseDto.prototype, "type", void 0);
//# sourceMappingURL=send-response.dto.js.map