"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlogPurchaseStatusResponseDto = exports.PaginatedBlogPurchaseResponseDto = exports.BlogPurchaseDetailResponseDto = exports.BuyerDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const blog_response_dto_1 = require("./blog-response.dto");
class BuyerDto {
    id;
    name;
    avatar;
}
exports.BuyerDto = BuyerDto;
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID của người mua',
        example: 10,
        nullable: true,
    }),
    __metadata("design:type", Object)
], BuyerDto.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Tên người mua',
        example: 'Nguyễn Văn B',
        nullable: true,
    }),
    __metadata("design:type", String)
], BuyerDto.prototype, "name", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Avatar của người mua',
        example: 'https://cdn.example.com/avatars/user20.jpg',
        nullable: true,
    }),
    __metadata("design:type", String)
], BuyerDto.prototype, "avatar", void 0);
class BlogPurchaseDetailResponseDto {
    id;
    userId;
    blogId;
    point;
    purchasedAt;
    platformFeePercent;
    sellerReceivePrice;
    blog;
    buyer;
}
exports.BlogPurchaseDetailResponseDto = BlogPurchaseDetailResponseDto;
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID của giao dịch mua bài viết',
        example: 1,
    }),
    __metadata("design:type", Number)
], BlogPurchaseDetailResponseDto.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID của người mua',
        example: 10,
    }),
    __metadata("design:type", Number)
], BlogPurchaseDetailResponseDto.prototype, "userId", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID của bài viết',
        example: 1,
    }),
    __metadata("design:type", Number)
], BlogPurchaseDetailResponseDto.prototype, "blogId", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Số điểm đã sử dụng để mua',
        example: 100,
    }),
    __metadata("design:type", Number)
], BlogPurchaseDetailResponseDto.prototype, "point", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian mua (Unix timestamp)',
        example: 1632474086123,
    }),
    __metadata("design:type", Number)
], BlogPurchaseDetailResponseDto.prototype, "purchasedAt", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Phần trăm phí nền tảng',
        example: 5,
    }),
    __metadata("design:type", Number)
], BlogPurchaseDetailResponseDto.prototype, "platformFeePercent", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Số điểm người bán nhận được',
        example: 95,
    }),
    __metadata("design:type", Number)
], BlogPurchaseDetailResponseDto.prototype, "sellerReceivePrice", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => blog_response_dto_1.BlogResponseDto),
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin bài viết',
        type: blog_response_dto_1.BlogResponseDto,
    }),
    __metadata("design:type", blog_response_dto_1.BlogResponseDto)
], BlogPurchaseDetailResponseDto.prototype, "blog", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => BuyerDto),
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin người mua',
        type: BuyerDto,
    }),
    __metadata("design:type", BuyerDto)
], BlogPurchaseDetailResponseDto.prototype, "buyer", void 0);
class PaginatedBlogPurchaseResponseDto {
    content;
    totalItems;
    itemCount;
    itemsPerPage;
    totalPages;
    currentPage;
}
exports.PaginatedBlogPurchaseResponseDto = PaginatedBlogPurchaseResponseDto;
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => blog_response_dto_1.BlogResponseDto),
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách bài viết đã mua',
        type: [blog_response_dto_1.BlogResponseDto],
    }),
    __metadata("design:type", Array)
], PaginatedBlogPurchaseResponseDto.prototype, "content", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Tổng số bản ghi',
        example: 100,
    }),
    __metadata("design:type", Number)
], PaginatedBlogPurchaseResponseDto.prototype, "totalItems", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Số lượng bản ghi trên trang hiện tại',
        example: 10,
    }),
    __metadata("design:type", Number)
], PaginatedBlogPurchaseResponseDto.prototype, "itemCount", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Số lượng bản ghi trên mỗi trang',
        example: 10,
    }),
    __metadata("design:type", Number)
], PaginatedBlogPurchaseResponseDto.prototype, "itemsPerPage", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Tổng số trang',
        example: 10,
    }),
    __metadata("design:type", Number)
], PaginatedBlogPurchaseResponseDto.prototype, "totalPages", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Trang hiện tại',
        example: 1,
    }),
    __metadata("design:type", Number)
], PaginatedBlogPurchaseResponseDto.prototype, "currentPage", void 0);
class BlogPurchaseStatusResponseDto {
    purchased;
    purchased_at;
}
exports.BlogPurchaseStatusResponseDto = BlogPurchaseStatusResponseDto;
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Đã mua hay chưa',
        example: true,
    }),
    __metadata("design:type", Boolean)
], BlogPurchaseStatusResponseDto.prototype, "purchased", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian mua (Unix timestamp)',
        example: 1632474086123,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogPurchaseStatusResponseDto.prototype, "purchased_at", void 0);
//# sourceMappingURL=blog-purchase-response.dto.js.map