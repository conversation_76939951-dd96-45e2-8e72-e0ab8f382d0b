"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _dispatch = require("@langchain/core/callbacks/dispatch");
const _langgraph = require("@langchain/langgraph");
const graphNode = async (_state)=>{
    const chunks = [
        'Four',
        'score',
        'and',
        'seven',
        'years',
        'ago',
        'our',
        'fathers',
        '...'
    ];
    for (const chunk of chunks){
        await (0, _dispatch.dispatchCustomEvent)('my_custom_event', {
            chunk
        });
    }
    return {
        messages: [
            {
                role: 'assistant',
                content: chunks.join(' ')
            }
        ]
    };
};
const graphWithDispatch = new _langgraph.StateGraph(_langgraph.MessagesAnnotation).addNode('model', graphNode).addEdge('__start__', 'model').compile();
async function test() {
    const eventStream = graphWithDispatch.streamEvents({
        messages: [
            {
                role: 'user',
                content: 'What are you thinking about?'
            }
        ]
    }, {
        version: 'v2'
    });
    for await (const { event, name, data } of eventStream){
        if (event === 'on_custom_event' && name === 'my_custom_event') {
            console.log(`${data.chunk}|`);
        }
    }
}
test().then();

//# sourceMappingURL=custom-event.js.map