"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserStepConnection = void 0;
const typeorm_1 = require("typeorm");
let UserStepConnection = class UserStepConnection {
    connectionId;
    taskId;
    fromStepId;
    toStepId;
    outputField;
    inputField;
    createdAt;
    updatedAt;
};
exports.UserStepConnection = UserStepConnection;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid', { name: 'connection_id' }),
    __metadata("design:type", String)
], UserStepConnection.prototype, "connectionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'task_id', type: 'uuid', nullable: false }),
    __metadata("design:type", String)
], UserStepConnection.prototype, "taskId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'from_step_id', type: 'uuid', nullable: false }),
    __metadata("design:type", String)
], UserStepConnection.prototype, "fromStepId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'to_step_id', type: 'uuid', nullable: false }),
    __metadata("design:type", String)
], UserStepConnection.prototype, "toStepId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'output_field', type: 'varchar', length: 255, nullable: false }),
    __metadata("design:type", String)
], UserStepConnection.prototype, "outputField", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'input_field', type: 'varchar', length: 255, nullable: false }),
    __metadata("design:type", String)
], UserStepConnection.prototype, "inputField", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'created_at',
        type: 'bigint',
        default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    }),
    __metadata("design:type", Number)
], UserStepConnection.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'updated_at',
        type: 'bigint',
        default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    }),
    __metadata("design:type", Number)
], UserStepConnection.prototype, "updatedAt", void 0);
exports.UserStepConnection = UserStepConnection = __decorate([
    (0, typeorm_1.Entity)('user_step_connections')
], UserStepConnection);
//# sourceMappingURL=user-step-connection.entity.js.map