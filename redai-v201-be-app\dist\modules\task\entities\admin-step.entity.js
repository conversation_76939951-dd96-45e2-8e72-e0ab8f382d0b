"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminStep = void 0;
const typeorm_1 = require("typeorm");
const step_config_interface_1 = require("../interfaces/step-config.interface");
let AdminStep = class AdminStep {
    stepId;
    taskId;
    orderIndex;
    stepName;
    stepDescription;
    stepType;
    stepConfig;
    createdAt;
    updatedAt;
};
exports.AdminStep = AdminStep;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid', { name: 'step_id' }),
    __metadata("design:type", String)
], AdminStep.prototype, "stepId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'task_id', type: 'uuid', nullable: false }),
    __metadata("design:type", String)
], AdminStep.prototype, "taskId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'order_index', type: 'int', nullable: false }),
    __metadata("design:type", Number)
], AdminStep.prototype, "orderIndex", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'step_name', type: 'varchar', length: 255, nullable: false }),
    __metadata("design:type", String)
], AdminStep.prototype, "stepName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'step_description', type: 'text', nullable: true }),
    __metadata("design:type", String)
], AdminStep.prototype, "stepDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'step_type',
        type: 'varchar',
        length: 50,
        nullable: false,
    }),
    __metadata("design:type", String)
], AdminStep.prototype, "stepType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'step_config', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], AdminStep.prototype, "stepConfig", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'created_at',
        type: 'bigint',
        default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    }),
    __metadata("design:type", Number)
], AdminStep.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'updated_at',
        type: 'bigint',
        default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    }),
    __metadata("design:type", Number)
], AdminStep.prototype, "updatedAt", void 0);
exports.AdminStep = AdminStep = __decorate([
    (0, typeorm_1.Entity)('admin_steps'),
    (0, typeorm_1.Unique)('unique_admin_task_order', ['taskId', 'orderIndex'])
], AdminStep);
//# sourceMappingURL=admin-step.entity.js.map