"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentValidationHelper = void 0;
const exceptions_1 = require("../../../../common/exceptions");
const exceptions_2 = require("../../exceptions");
class AgentValidationHelper {
    static async validateAgentDataByTypeConfig(createDto, typeAgentConfig, userId, vectorStoreRepository) {
        this.validateProfileBlock(createDto, typeAgentConfig);
        this.validateOutputBlock(createDto, typeAgentConfig);
        this.validateResourcesBlock(createDto, typeAgentConfig);
        this.validateStrategyBlock(createDto, typeAgentConfig);
        this.validateMultiAgentBlock(createDto, typeAgentConfig);
        this.validateConversionBlock(createDto, typeAgentConfig);
        await this.validateVectorStore(createDto.vectorStoreId, typeAgentConfig, userId, vectorStoreRepository);
    }
    static validateProfileBlock(createDto, typeAgentConfig) {
        if (typeAgentConfig.enableAgentProfileCustomization) {
            if (createDto.profile) {
                console.log('Profile được cung cấp:', createDto.profile);
            }
        }
        else {
            if (createDto.profile) {
                throw new exceptions_1.AppException(exceptions_2.AGENT_ERROR_CODES.AGENT_PROFILE_NOT_SUPPORTED, 'Loại agent này không hỗ trợ profile', { typeAgentConfig: typeAgentConfig.enableAgentProfileCustomization });
            }
        }
    }
    static validateOutputBlock(createDto, typeAgentConfig) {
        if (typeAgentConfig.enableOutputToMessenger) {
            if (createDto.outputMessenger) {
                console.log('Output được cung cấp:', createDto.outputMessenger);
            }
        }
        else {
            if (createDto.outputMessenger) {
                throw new exceptions_1.AppException(exceptions_2.AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED, 'Loại agent này không hỗ trợ output configuration', { typeAgentConfig: typeAgentConfig.enableOutputToMessenger });
            }
        }
    }
    static validateResourcesBlock(createDto, typeAgentConfig) {
        if (typeAgentConfig.enableResourceUsage) {
            if (createDto.resources) {
                console.log('Resources được cung cấp:', createDto.resources);
            }
        }
        else {
            if (createDto.resources) {
                throw new exceptions_1.AppException(exceptions_2.AGENT_ERROR_CODES.AGENT_RESOURCES_NOT_SUPPORTED, 'Loại agent này không hỗ trợ resources', { typeAgentConfig: typeAgentConfig.enableResourceUsage });
            }
        }
    }
    static validateStrategyBlock(createDto, typeAgentConfig) {
        if (typeAgentConfig.enableDynamicStrategyExecution) {
            if (createDto.strategy) {
                console.log('Strategy được cung cấp:', createDto.strategy);
            }
        }
        else {
            if (createDto.strategy) {
                throw new exceptions_1.AppException(exceptions_2.AGENT_ERROR_CODES.AGENT_STRATEGY_NOT_SUPPORTED, 'Loại agent này không hỗ trợ strategy', { typeAgentConfig: typeAgentConfig.enableDynamicStrategyExecution });
            }
        }
    }
    static validateMultiAgentBlock(createDto, typeAgentConfig) {
        if (typeAgentConfig.enableMultiAgentCollaboration) {
            if (createDto.multiAgent) {
                console.log('Multi Agent được cung cấp:', createDto.multiAgent);
                if (createDto.multiAgent.multiAgent && createDto.multiAgent.multiAgent.length > 0) {
                    for (const item of createDto.multiAgent.multiAgent) {
                        if (!item.agent_id || !item.prompt) {
                            throw new exceptions_1.AppException(exceptions_2.AGENT_ERROR_CODES.AGENT_MULTI_AGENT_INCOMPLETE, 'Mỗi agent trong multi agent phải có agent_id và prompt', { invalidItem: item });
                        }
                    }
                }
            }
        }
        else {
            if (createDto.multiAgent) {
                throw new exceptions_1.AppException(exceptions_2.AGENT_ERROR_CODES.AGENT_MULTI_AGENT_NOT_SUPPORTED, 'Loại agent này không hỗ trợ multi agent', { typeAgentConfig: typeAgentConfig.enableMultiAgentCollaboration });
            }
        }
    }
    static validateConversionBlock(createDto, typeAgentConfig) {
        if (typeAgentConfig.enableTaskConversionTracking) {
            console.log('TypeAgent supports conversion:', typeAgentConfig.enableTaskConversionTracking);
        }
    }
    static validateInstruction(instruction, typeAgentConfig) {
        if (instruction && instruction.trim().length === 0) {
            throw new exceptions_1.AppException(exceptions_2.AGENT_ERROR_CODES.AGENT_INSTRUCTION_INVALID, 'Instruction không được để trống nếu được cung cấp', { instruction });
        }
    }
    static async validateVectorStore(vectorStoreId, typeAgentConfig, userId, vectorStoreRepository) {
        if (vectorStoreId) {
            if (vectorStoreId.trim().length === 0) {
                throw new exceptions_1.AppException(exceptions_2.AGENT_ERROR_CODES.AGENT_VECTOR_STORE_INVALID, 'Vector store ID không được để trống nếu được cung cấp', { vectorStoreId });
            }
            const vectorStore = await vectorStoreRepository.findOneByIdAndUserId(vectorStoreId, userId);
            if (!vectorStore) {
                throw new exceptions_1.AppException(exceptions_2.AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND, `Vector store với ID ${vectorStoreId} không tồn tại hoặc bạn không có quyền truy cập`);
            }
            console.log('Vector store hợp lệ:', vectorStore.name);
        }
    }
}
exports.AgentValidationHelper = AgentValidationHelper;
//# sourceMappingURL=agent-validation.helper.js.map