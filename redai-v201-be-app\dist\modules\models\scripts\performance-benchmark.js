"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runPerformanceBenchmark = runPerformanceBenchmark;
const core_1 = require("@nestjs/core");
const app_module_1 = require("../../../app.module");
const performance_monitor_service_1 = require("../services/performance-monitor.service");
const model_discovery_service_1 = require("../services/model-discovery.service");
const system_model_sync_service_1 = require("../services/system-model-sync.service");
const user_model_sync_service_1 = require("../services/user-model-sync.service");
async function runPerformanceBenchmark() {
    console.log('🚀 Starting Model Auto-Discovery Performance Benchmark...\n');
    try {
        const app = await core_1.NestFactory.createApplicationContext(app_module_1.AppModule);
        const performanceMonitor = app.get(performance_monitor_service_1.PerformanceMonitorService);
        const modelDiscovery = app.get(model_discovery_service_1.ModelDiscoveryService);
        const systemModelSync = app.get(system_model_sync_service_1.SystemModelSyncService);
        const userModelSync = app.get(user_model_sync_service_1.UserModelSyncService);
        console.log('✅ Application context initialized\n');
        const testScenarios = [
            { modelCounts: [10, 25, 50], provider: 'openai', description: 'Small scale test' },
            { modelCounts: [100, 200], provider: 'openai', description: 'Medium scale test' },
            { modelCounts: [500], provider: 'openai', description: 'Large scale test' }
        ];
        for (const scenario of testScenarios) {
            console.log(`📊 Running ${scenario.description} with ${scenario.provider}...`);
            const results = await performanceMonitor.runSyncModelsBenchmark(scenario.modelCounts, scenario.provider);
            const report = performanceMonitor.generatePerformanceReport(results);
            console.log(report);
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        console.log('🔬 Testing Real ModelDiscoveryService Performance...\n');
        await testRealModelDiscovery(performanceMonitor, modelDiscovery);
        console.log('🔬 Testing SystemModelSyncService Performance...\n');
        await testSystemModelSync(performanceMonitor, systemModelSync);
        console.log('✅ Performance benchmark completed successfully!');
    }
    catch (error) {
        console.error('❌ Benchmark failed:', error.message);
        console.error(error.stack);
    }
}
async function testRealModelDiscovery(monitor, service) {
    const operationId = 'real_model_discovery_test';
    try {
        monitor.startMonitoring(operationId, {
            testType: 'real_model_discovery',
            service: 'ModelDiscoveryService'
        });
        const mockModels = generateMockModels(50);
        console.log(`Testing with ${mockModels.length} mock models...`);
        const systemResult = await service.discoverSystemModels('mock-encrypted-key', 'openai', 'test-key-id');
        monitor.updateMetrics(operationId, {
            modelsProcessed: mockModels.length,
            newModelsCreated: systemResult.newModelsCreated,
            patternsMatched: systemResult.modelsMatched,
            mappingsCreated: systemResult.mappingsCreated
        });
        const metrics = monitor.stopMonitoring(operationId);
        if (metrics) {
            console.log('📈 Real ModelDiscoveryService Results:');
            console.log(`   Duration: ${metrics.duration}ms`);
            console.log(`   Models Processed: ${metrics.modelsProcessed}`);
            console.log(`   New Models: ${metrics.newModelsCreated}`);
            console.log(`   Mappings Created: ${metrics.mappingsCreated}`);
            console.log(`   Memory Used: ${(metrics.peakMemory - metrics.memoryBefore).toFixed(2)}MB`);
            console.log(`   Throughput: ${(metrics.modelsProcessed / (metrics.duration / 1000)).toFixed(2)} models/sec\n`);
        }
    }
    catch (error) {
        monitor.addError(operationId, error.message);
        console.error('❌ Real ModelDiscovery test failed:', error.message);
    }
}
async function testSystemModelSync(monitor, service) {
    const operationId = 'system_model_sync_test';
    try {
        monitor.startMonitoring(operationId, {
            testType: 'system_model_sync',
            service: 'SystemModelSyncService'
        });
        console.log('Testing SystemModelSyncService with mock data...');
        const mockRequest = {
            keyId: 'test-system-key-id',
            provider: 'openai',
            encryptedApiKey: 'mock-encrypted-key'
        };
        await simulateSystemModelSync(monitor, operationId);
        const metrics = monitor.stopMonitoring(operationId);
        if (metrics) {
            console.log('📈 SystemModelSyncService Results:');
            console.log(`   Duration: ${metrics.duration}ms`);
            console.log(`   Models Processed: ${metrics.modelsProcessed}`);
            console.log(`   Memory Used: ${(metrics.peakMemory - metrics.memoryBefore).toFixed(2)}MB`);
            console.log(`   DB Queries: ${metrics.databaseQueries}`);
            console.log(`   Errors: ${metrics.errors.length}\n`);
        }
    }
    catch (error) {
        monitor.addError(operationId, error.message);
        console.error('❌ SystemModelSync test failed:', error.message);
    }
}
function generateMockModels(count) {
    const models = [];
    const modelTypes = ['gpt', 'claude', 'llama', 'gemini', 'mistral'];
    const versions = ['3.5', '4', '4-turbo', '2', '7b', '13b', '70b'];
    for (let i = 0; i < count; i++) {
        const type = modelTypes[i % modelTypes.length];
        const version = versions[i % versions.length];
        models.push({
            id: `${type}-${version}-${i}`,
            name: `${type}-${version}`,
            description: `Mock model ${type} version ${version}`
        });
    }
    return models;
}
async function simulateSystemModelSync(monitor, operationId) {
    const modelCount = 75;
    await new Promise(resolve => setTimeout(resolve, 200));
    monitor.incrementQueryCount(operationId, 1);
    for (let i = 0; i < modelCount; i++) {
        await new Promise(resolve => setTimeout(resolve, 2));
        monitor.incrementQueryCount(operationId, 1);
    }
    await new Promise(resolve => setTimeout(resolve, 150));
    monitor.incrementQueryCount(operationId, 3);
    await new Promise(resolve => setTimeout(resolve, 100));
    monitor.incrementQueryCount(operationId, 2);
    monitor.updateMetrics(operationId, {
        modelsProcessed: modelCount,
        newModelsCreated: Math.floor(modelCount * 0.6),
        patternsMatched: modelCount,
        mappingsCreated: modelCount
    });
}
if (require.main === module) {
    runPerformanceBenchmark()
        .then(() => {
        console.log('🎉 Benchmark completed!');
        process.exit(0);
    })
        .catch((error) => {
        console.error('💥 Benchmark failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=performance-benchmark.js.map