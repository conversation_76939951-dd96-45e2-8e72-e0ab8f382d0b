"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ChatService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatService = void 0;
const common_1 = require("@nestjs/common");
const microservices_1 = require("@nestjs/microservices");
const agent_config_service_1 = require("./agent-config.service");
const database_1 = require("../database");
const message_response_dto_1 = require("../dto/message-response.dto");
const enums_1 = require("../../../shared/enums");
const constants_1 = require("../constants");
let ChatService = ChatService_1 = class ChatService {
    agentConfigService;
    userAgentRunsQueries;
    userMessagesQueries;
    chatDatabaseService;
    redisClient;
    logger = new common_1.Logger(ChatService_1.name);
    constructor(agentConfigService, userAgentRunsQueries, userMessagesQueries, chatDatabaseService, redisClient) {
        this.agentConfigService = agentConfigService;
        this.userAgentRunsQueries = userAgentRunsQueries;
        this.userMessagesQueries = userMessagesQueries;
        this.chatDatabaseService = chatDatabaseService;
        this.redisClient = redisClient;
    }
    async processMessage(messageRequest, userId, jwt = '') {
        const queryRunner = await this.chatDatabaseService.startTransaction();
        try {
            this.logger.log(`Processing message for agent ${messageRequest.agentId} from user ${userId}`);
            const agentConfig = await this.agentConfigService.getAgentConfig(messageRequest.agentId);
            if (!agentConfig) {
                throw new Error(`Agent not found: ${messageRequest.agentId}`);
            }
            if (!this.agentConfigService.validateAgentConfig(agentConfig)) {
                throw new Error(`Invalid agent configuration: ${messageRequest.agentId}`);
            }
            const agentConfigMap = await this.agentConfigService.buildAgentConfigMap();
            const runPayload = this.buildRunPayload(messageRequest, agentConfig, agentConfigMap, userId);
            const createRunQuery = `
        INSERT INTO user_agent_runs (payload, status, created_by)
        VALUES ($1, $2, $3)
        RETURNING id
      `;
            const runResult = await queryRunner.query(createRunQuery, [
                JSON.stringify(runPayload),
                enums_1.UserAgentRunStatus.CREATED,
                userId
            ]);
            const runId = runResult[0].id;
            this.logger.log(`Created run ${runId} for agent ${messageRequest.agentId}`);
            const threadId = messageRequest.threadId || runId;
            const createMessageQuery = `
        INSERT INTO user_messages (thread_id, role, content, timestamp, created_by)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING message_id
      `;
            const messageResult = await queryRunner.query(createMessageQuery, [
                threadId,
                'user',
                JSON.stringify({
                    contentBlocks: messageRequest.contentBlocks,
                    agentId: messageRequest.agentId,
                    runId: runId,
                }),
                Date.now(),
                userId
            ]);
            const userMessageId = messageResult[0].message_id;
            this.logger.log(`Persisted user message ${userMessageId} for thread ${threadId}`);
            const runTriggerEvent = {
                eventType: constants_1.REDIS_EVENTS.RUN_TRIGGER,
                runId,
                threadId: messageRequest.threadId || runId,
                agentId: agentConfig.id,
                userId,
                jwt,
                timestamp: Date.now(),
                priority: 'medium',
                publishedAt: Date.now(),
            };
            this.redisClient.emit(constants_1.REDIS_EVENTS.RUN_TRIGGER, runTriggerEvent);
            this.logger.log(`Published run trigger event for run ${runId}`);
            await queryRunner.commitTransaction();
            this.logger.log(`Transaction committed for run ${runId}`);
            return new message_response_dto_1.MessageResponseDto({
                runId,
                agentId: agentConfig.id,
                agentName: agentConfig.name,
                status: enums_1.UserAgentRunStatus.CREATED,
                createdAt: Date.now(),
            });
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`Transaction rolled back for agent ${messageRequest.agentId}: ${error.message}`, error.stack);
            throw new Error(`Message processing failed: ${error.message}`);
        }
        finally {
            await queryRunner.release();
        }
    }
    buildRunPayload(messageRequest, agentConfig, agentConfigMap, userId) {
        const contentBlocks = Array.isArray(messageRequest.contentBlocks)
            ? messageRequest.contentBlocks
            : [messageRequest.contentBlocks];
        const textContent = contentBlocks
            .filter(block => block.type === 'text' && block.content)
            .map(block => block.content)
            .join('\n');
        const payload = {
            message: {
                content: textContent,
                contentBlocks,
                threadId: messageRequest.threadId,
            },
            primaryAgentId: agentConfig.id,
            agentConfigMap,
            metadata: {
                userId,
                requestId: `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
                version: '1.0',
            },
            processing: {
                maxRetries: 3,
                timeoutMs: 300000,
                enableMultiAgent: Object.keys(agentConfigMap).length > 1,
                alwaysApproveToolCall: messageRequest.alwaysApproveToolCall || false,
            },
        };
        this.logger.debug(`Built run payload for agent ${agentConfig.id}`, {
            agentId: agentConfig.id,
            messageLength: textContent.length,
            contentBlockCount: contentBlocks.length,
            multiAgentEnabled: payload.processing.enableMultiAgent,
            agentCount: Object.keys(agentConfigMap).length,
            alwaysApproveToolCall: payload.processing.alwaysApproveToolCall,
        });
        return payload;
    }
    async getAgentConfigSummary() {
        return this.agentConfigService.getConfigSummary();
    }
    async cancelRun(runId, reason = 'User requested cancellation') {
        try {
            this.logger.log(`Cancelling run ${runId}: ${reason}`);
            const runData = await this.userAgentRunsQueries.getRunById(runId);
            if (!runData) {
                this.logger.error(`Run not found for cancellation: ${runId}`);
                return false;
            }
            const threadId = runData.payload?.message?.threadId || runId;
            const updateSuccess = await this.userAgentRunsQueries.updateRunStatus(runId, enums_1.UserAgentRunStatus.FAILED);
            if (!updateSuccess) {
                this.logger.error(`Failed to update run status for cancellation: ${runId}`);
                return false;
            }
            try {
                const runCancelEvent = {
                    eventType: constants_1.REDIS_EVENTS.RUN_CANCEL,
                    threadId,
                    runId,
                    reason,
                    timestamp: Date.now(),
                    publishedAt: Date.now(),
                };
                this.redisClient.emit(constants_1.REDIS_EVENTS.RUN_CANCEL, runCancelEvent);
                this.logger.log(`Published run cancel event for thread ${threadId} (run ${runId})`);
            }
            catch (error) {
                this.logger.warn(`Failed to publish run cancel event for run ${runId}: ${error.message}`);
            }
            this.logger.log(`Successfully cancelled run ${runId} (thread ${threadId})`);
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to cancel run ${runId}: ${error.message}`, error.stack);
            return false;
        }
    }
    async validateAgent(agentId) {
        try {
            const agentConfig = await this.agentConfigService.getAgentConfig(agentId);
            return agentConfig ? this.agentConfigService.validateAgentConfig(agentConfig) : false;
        }
        catch (error) {
            this.logger.error(`Error validating agent ${agentId}: ${error.message}`);
            return false;
        }
    }
    async getRedisHealth() {
        try {
            this.redisClient.emit('health.check', { timestamp: Date.now() });
            return true;
        }
        catch (error) {
            this.logger.error(`Redis health check failed: ${error.message}`);
            return false;
        }
    }
};
exports.ChatService = ChatService;
exports.ChatService = ChatService = ChatService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(4, (0, common_1.Inject)('REDIS_CLIENT')),
    __metadata("design:paramtypes", [agent_config_service_1.AgentConfigService,
        database_1.UserAgentRunsQueries,
        database_1.UserMessagesQueries,
        database_1.ChatDatabaseService,
        microservices_1.ClientProxy])
], ChatService);
//# sourceMappingURL=chat.service.js.map