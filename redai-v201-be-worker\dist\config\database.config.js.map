{"version": 3, "sources": ["../../src/config/database.config.ts"], "sourcesContent": ["import { TypeOrmModuleOptions } from '@nestjs/typeorm';\r\nimport { env } from './env';\r\n\r\n// <PERSON><PERSON><PERSON> hình kết nối cơ sở dữ liệu sử dụng TypeORM\r\nexport const databaseConfig: TypeOrmModuleOptions = {\r\n  type: 'postgres',\r\n  host: env.database.DB_HOST,\r\n  port: Number(env.database.DB_PORT),\r\n  username: env.database.DB_USERNAME,\r\n  password: env.database.DB_PASSWORD,\r\n  database: env.database.DB_DATABASE,\r\n  ssl: env.database.DB_SSL ? { rejectUnauthorized: false } : false,\r\n  entities: [__dirname + '/../**/*.entity{.ts,.js}'],\r\n  migrations: [__dirname + '/../modules/database/migrations/*{.ts,.js}'],\r\n  synchronize: false, // Tắt synchronize để tránh lỗi quyền trên production database\r\n  logging: env.misc.NODE_ENV !== 'production',\r\n  autoLoadEntities: true,\r\n  migrationsRun: env.misc.NODE_ENV === 'production', // Tự động chạy migrations trong môi trường production\r\n};\r\n"], "names": ["databaseConfig", "type", "host", "env", "database", "DB_HOST", "port", "Number", "DB_PORT", "username", "DB_USERNAME", "password", "DB_PASSWORD", "DB_DATABASE", "ssl", "DB_SSL", "rejectUnauthorized", "entities", "__dirname", "migrations", "synchronize", "logging", "misc", "NODE_ENV", "autoLoadEntities", "migrationsRun"], "mappings": ";;;;+BAIaA;;;eAAAA;;;qBAHO;AAGb,MAAMA,iBAAuC;IAClDC,MAAM;IACNC,MAAMC,QAAG,CAACC,QAAQ,CAACC,OAAO;IAC1BC,MAAMC,OAAOJ,QAAG,CAACC,QAAQ,CAACI,OAAO;IACjCC,UAAUN,QAAG,CAACC,QAAQ,CAACM,WAAW;IAClCC,UAAUR,QAAG,CAACC,QAAQ,CAACQ,WAAW;IAClCR,UAAUD,QAAG,CAACC,QAAQ,CAACS,WAAW;IAClCC,KAAKX,QAAG,CAACC,QAAQ,CAACW,MAAM,GAAG;QAAEC,oBAAoB;IAAM,IAAI;IAC3DC,UAAU;QAACC,YAAY;KAA2B;IAClDC,YAAY;QAACD,YAAY;KAA6C;IACtEE,aAAa;IACbC,SAASlB,QAAG,CAACmB,IAAI,CAACC,QAAQ,KAAK;IAC/BC,kBAAkB;IAClBC,eAAetB,QAAG,CAACmB,IAAI,CAACC,QAAQ,KAAK;AACvC"}