"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const generic_admin_module_1 = require("./admin/generic-admin.module");
const generic_user_module_1 = require("./user/generic-user.module");
const entities_1 = require("./entities");
const repositories_1 = require("./repositories");
let GenericModule = class GenericModule {
};
exports.GenericModule = GenericModule;
exports.GenericModule = GenericModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                entities_1.GenericPage,
                entities_1.GenericPageTemplate,
                entities_1.GenericPageTemplateTag,
                entities_1.GenericPageSubmission,
            ]),
            generic_admin_module_1.GenericAdminModule,
            generic_user_module_1.GenericUserModule,
        ],
        providers: [
            repositories_1.GenericPageRepository,
            repositories_1.GenericPageTemplateRepository,
            repositories_1.GenericPageTemplateTagRepository,
            repositories_1.GenericPageSubmissionRepository,
        ],
        exports: [
            generic_admin_module_1.GenericAdminModule,
            generic_user_module_1.GenericUserModule,
            typeorm_1.TypeOrmModule,
        ],
    })
], GenericModule);
//# sourceMappingURL=generic.module.js.map