"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ConversationThreadService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationThreadService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const typeorm_transactional_1 = require("typeorm-transactional");
const class_transformer_1 = require("class-transformer");
const exceptions_1 = require("../../../common/exceptions");
const chat_error_codes_1 = require("../exceptions/chat-error-codes");
const conversation_thread_dto_1 = require("../dto/conversation-thread.dto");
let ConversationThreadService = ConversationThreadService_1 = class ConversationThreadService {
    dataSource;
    logger = new common_1.Logger(ConversationThreadService_1.name);
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async create(createDto) {
        try {
            const query = `
        INSERT INTO user_conversation_thread (name, user_id, created_at, updated_at)
        VALUES ($1, $2, $3, $3)
        RETURNING thread_id, name, user_id, created_at, updated_at
      `;
            const now = Date.now();
            const result = await this.dataSource.query(query, [createDto.name, createDto.userId, now]);
            if (!result || result.length === 0) {
                throw new exceptions_1.AppException(chat_error_codes_1.CHAT_ERROR_CODES.THREAD_CREATION_FAILED, 'Failed to create conversation thread in database');
            }
            const thread = result[0];
            this.logger.log(`Created conversation thread ${thread.thread_id} for user ${createDto.userId}`);
            return (0, class_transformer_1.plainToInstance)(conversation_thread_dto_1.ConversationThreadResponseDto, {
                threadId: thread.thread_id,
                name: thread.name,
                userId: thread.user_id,
                createdAt: parseInt(thread.created_at),
                updatedAt: parseInt(thread.updated_at),
            }, { excludeExtraneousValues: true });
        }
        catch (error) {
            this.logger.error(`Error creating conversation thread: ${error.message}`, error.stack);
            if (error instanceof exceptions_1.AppException) {
                throw error;
            }
            throw new exceptions_1.AppException(chat_error_codes_1.CHAT_ERROR_CODES.THREAD_CREATION_FAILED, `Failed to create conversation thread: ${error.message}`);
        }
    }
    async findAllByUser(queryDto, userId) {
        const { page = 1, limit = 10, search, sortBy = 'created_at', sortDirection = 'DESC' } = queryDto;
        const offset = (page - 1) * limit;
        let whereClause = 'WHERE user_id = $1';
        const queryParams = [userId];
        let paramIndex = 2;
        if (search) {
            whereClause += ` AND name ILIKE $${paramIndex}`;
            queryParams.push(`%${search}%`);
            paramIndex++;
        }
        const allowedSortFields = ['created_at', 'updated_at', 'name'];
        const safeSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
        const safeSortDirection = ['ASC', 'DESC'].includes(sortDirection) ? sortDirection : 'DESC';
        const orderClause = `ORDER BY ${safeSortBy} ${safeSortDirection}`;
        const countQuery = `
      SELECT COUNT(*) as total
      FROM user_conversation_thread
      ${whereClause}
    `;
        const countResult = await this.dataSource.query(countQuery, queryParams);
        const total = parseInt(countResult[0].total);
        const threadsQuery = `
      SELECT thread_id, name, user_id, created_at, updated_at
      FROM user_conversation_thread
      ${whereClause}
      ${orderClause}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
        queryParams.push(limit, offset);
        const threadsResult = await this.dataSource.query(threadsQuery, queryParams);
        const threads = threadsResult.map((thread) => (0, class_transformer_1.plainToInstance)(conversation_thread_dto_1.ConversationThreadResponseDto, {
            threadId: thread.thread_id,
            name: thread.name,
            userId: thread.user_id,
            createdAt: parseInt(thread.created_at),
            updatedAt: parseInt(thread.updated_at),
        }, { excludeExtraneousValues: true }));
        const totalPages = Math.ceil(total / limit);
        this.logger.debug(`Retrieved ${threads.length} threads for user ${userId} (page ${page}/${totalPages})`);
        return {
            items: threads,
            meta: {
                totalItems: total,
                itemCount: threads.length,
                itemsPerPage: limit,
                totalPages,
                currentPage: page,
                hasItems: total > 0,
            },
        };
    }
    async findOne(threadId, userId) {
        const query = `
      SELECT thread_id, name, user_id, created_at, updated_at
      FROM user_conversation_thread
      WHERE thread_id = $1 AND user_id = $2
    `;
        const result = await this.dataSource.query(query, [threadId, userId]);
        if (!result || result.length === 0) {
            throw new exceptions_1.AppException(chat_error_codes_1.CHAT_ERROR_CODES.THREAD_NOT_FOUND, `Conversation thread ${threadId} not found or access denied`);
        }
        const thread = result[0];
        return (0, class_transformer_1.plainToInstance)(conversation_thread_dto_1.ConversationThreadResponseDto, {
            threadId: thread.thread_id,
            name: thread.name,
            userId: thread.user_id,
            createdAt: parseInt(thread.created_at),
            updatedAt: parseInt(thread.updated_at),
        }, { excludeExtraneousValues: true });
    }
    async update(threadId, userId, updateDto) {
        await this.findOne(threadId, userId);
        const now = Date.now();
        const query = `
      UPDATE user_conversation_thread
      SET name = COALESCE($1, name), updated_at = $2
      WHERE thread_id = $3 AND user_id = $4
      RETURNING thread_id, name, user_id, created_at, updated_at
    `;
        const result = await this.dataSource.query(query, [updateDto.name, now, threadId, userId]);
        if (!result || result.length === 0) {
            throw new exceptions_1.AppException(chat_error_codes_1.CHAT_ERROR_CODES.THREAD_NOT_FOUND, `Conversation thread ${threadId} not found or access denied`);
        }
        const thread = result[0];
        this.logger.log(`Updated conversation thread ${threadId} for user ${userId}`);
        return (0, class_transformer_1.plainToInstance)(conversation_thread_dto_1.ConversationThreadResponseDto, {
            threadId: thread.thread_id,
            name: thread.name,
            userId: thread.user_id,
            createdAt: parseInt(thread.created_at),
            updatedAt: parseInt(thread.updated_at),
        }, { excludeExtraneousValues: true });
    }
    async remove(threadId, userId) {
        await this.findOne(threadId, userId);
        const query = `
      DELETE FROM user_conversation_thread
      WHERE thread_id = $1 AND user_id = $2
    `;
        await this.dataSource.query(query, [threadId, userId]);
        this.logger.log(`Deleted conversation thread ${threadId} for user ${userId}`);
    }
    async exists(threadId, userId) {
        const query = `
      SELECT 1
      FROM user_conversation_thread
      WHERE thread_id = $1 AND user_id = $2
    `;
        const result = await this.dataSource.query(query, [threadId, userId]);
        return result && result.length > 0;
    }
    async getStats(userId) {
        const query = `
      SELECT
        COUNT(*) as total_threads,
        COUNT(CASE WHEN created_at > $1 THEN 1 END) as recent_threads
      FROM user_conversation_thread
      WHERE user_id = $2
    `;
        const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
        const result = await this.dataSource.query(query, [sevenDaysAgo, userId]);
        return {
            totalThreads: parseInt(result[0].total_threads),
            recentThreads: parseInt(result[0].recent_threads),
        };
    }
    async getThreadMessages(threadId, userId, queryDto) {
        const thread = await this.findOne(threadId, userId);
        const { page = 1, limit = 50, role, sortBy = 'timestamp', sortDirection = 'DESC' } = queryDto;
        const offset = (page - 1) * limit;
        let whereClause = 'WHERE m.thread_id = $1';
        const queryParams = [threadId];
        let paramIndex = 2;
        if (role) {
            whereClause += ` AND m.role = $${paramIndex}`;
            queryParams.push(role);
            paramIndex++;
        }
        const allowedMessageSortFields = ['timestamp', 'role'];
        const safeSortBy = allowedMessageSortFields.includes(sortBy) ? sortBy : 'timestamp';
        const safeSortDirection = ['ASC', 'DESC'].includes(sortDirection) ? sortDirection : 'DESC';
        const orderClause = `ORDER BY m.${safeSortBy} ${safeSortDirection}`;
        const countQuery = `
      SELECT COUNT(*) as total
      FROM user_messages m
      ${whereClause}
    `;
        const countResult = await this.dataSource.query(countQuery, queryParams);
        const total = parseInt(countResult[0].total);
        const messagesQuery = `
      SELECT
        m.message_id,
        m.thread_id,
        m.role,
        m.content,
        m.timestamp,
        m.created_by
      FROM user_messages m
      ${whereClause}
      ${orderClause}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
        queryParams.push(limit, offset);
        const messagesResult = await this.dataSource.query(messagesQuery, queryParams);
        const messages = messagesResult.map((message) => (0, class_transformer_1.plainToInstance)(conversation_thread_dto_1.ThreadMessageResponseDto, {
            messageId: message.message_id,
            threadId: message.thread_id,
            role: message.role,
            content: message.content,
            timestamp: parseInt(message.timestamp),
            createdBy: message.created_by,
        }, { excludeExtraneousValues: true }));
        const totalPages = Math.ceil(total / limit);
        this.logger.debug(`Retrieved ${messages.length} messages for thread ${threadId} (page ${page}/${totalPages})`);
        return {
            items: messages,
            meta: {
                totalItems: total,
                itemCount: messages.length,
                itemsPerPage: limit,
                totalPages,
                currentPage: page,
                hasItems: total > 0,
            },
        };
    }
};
exports.ConversationThreadService = ConversationThreadService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [conversation_thread_dto_1.CreateConversationThreadDto]),
    __metadata("design:returntype", Promise)
], ConversationThreadService.prototype, "create", null);
exports.ConversationThreadService = ConversationThreadService = ConversationThreadService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], ConversationThreadService);
//# sourceMappingURL=conversation-thread.service.js.map