"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentStrategy = void 0;
const typeorm_1 = require("typeorm");
let AgentStrategy = class AgentStrategy {
    id;
    content;
    exampleDefault;
    createdBy;
    updatedBy;
    deletedBy;
    modelName;
    modelRegistryId;
    keyLlmId;
};
exports.AgentStrategy = AgentStrategy;
__decorate([
    (0, typeorm_1.PrimaryColumn)('uuid'),
    __metadata("design:type", String)
], AgentStrategy.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'content', type: 'jsonb', default: '[]' }),
    __metadata("design:type", Array)
], AgentStrategy.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'example_default', type: 'jsonb', default: '[]' }),
    __metadata("design:type", Array)
], AgentStrategy.prototype, "exampleDefault", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'integer', nullable: true }),
    __metadata("design:type", Object)
], AgentStrategy.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'integer', nullable: true }),
    __metadata("design:type", Object)
], AgentStrategy.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'deleted_by', type: 'integer', nullable: true }),
    __metadata("design:type", Object)
], AgentStrategy.prototype, "deletedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'model_name', type: 'varchar', length: 255, nullable: false }),
    __metadata("design:type", String)
], AgentStrategy.prototype, "modelName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'model_registry_id', type: 'uuid', nullable: false }),
    __metadata("design:type", String)
], AgentStrategy.prototype, "modelRegistryId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'key_llm_id', type: 'uuid', nullable: false }),
    __metadata("design:type", String)
], AgentStrategy.prototype, "keyLlmId", void 0);
exports.AgentStrategy = AgentStrategy = __decorate([
    (0, typeorm_1.Entity)('agents_strategy')
], AgentStrategy);
//# sourceMappingURL=agents-strategy.entity.js.map