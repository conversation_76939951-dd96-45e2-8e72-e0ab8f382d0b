"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserConvertDetailResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const user_convert_customer_response_dto_1 = require("./user-convert-customer-response.dto");
class UserConvertDetailResponseDto {
    id;
    convertCustomerId;
    userId;
    conversionType;
    source;
    notes;
    content;
    createdAt;
    updatedAt;
    customer;
}
exports.UserConvertDetailResponseDto = UserConvertDetailResponseDto;
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID bản ghi chuyển đổi',
        example: 1,
    }),
    __metadata("design:type", Number)
], UserConvertDetailResponseDto.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID khách hàng được chuyển đổi',
        example: 101,
        nullable: true,
    }),
    __metadata("design:type", Number)
], UserConvertDetailResponseDto.prototype, "convertCustomerId", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID người dùng thực hiện chuyển đổi',
        example: 1001,
        nullable: true,
    }),
    __metadata("design:type", Number)
], UserConvertDetailResponseDto.prototype, "userId", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Loại chuyển đổi',
        example: 'online',
        nullable: true,
    }),
    __metadata("design:type", String)
], UserConvertDetailResponseDto.prototype, "conversionType", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Nguồn gốc chuyển đổi',
        example: 'website',
        nullable: true,
    }),
    __metadata("design:type", String)
], UserConvertDetailResponseDto.prototype, "source", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Ghi chú thêm về chuyển đổi',
        example: 'Khách hàng đăng ký qua form liên hệ',
        nullable: true,
    }),
    __metadata("design:type", String)
], UserConvertDetailResponseDto.prototype, "notes", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin bổ sung',
        example: { campaign: 'summer_sale', referrer: 'google' },
        nullable: true,
    }),
    __metadata("design:type", Object)
], UserConvertDetailResponseDto.prototype, "content", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo (millis)',
        example: 1741708800000,
    }),
    __metadata("design:type", Number)
], UserConvertDetailResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian cập nhật (millis)',
        example: 1741708800000,
    }),
    __metadata("design:type", Number)
], UserConvertDetailResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin chi tiết của khách hàng được chuyển đổi',
        type: user_convert_customer_response_dto_1.UserConvertCustomerResponseDto,
        nullable: true,
    }),
    (0, class_transformer_1.Type)(() => user_convert_customer_response_dto_1.UserConvertCustomerResponseDto),
    __metadata("design:type", user_convert_customer_response_dto_1.UserConvertCustomerResponseDto)
], UserConvertDetailResponseDto.prototype, "customer", void 0);
//# sourceMappingURL=user-convert-detail-response.dto.js.map