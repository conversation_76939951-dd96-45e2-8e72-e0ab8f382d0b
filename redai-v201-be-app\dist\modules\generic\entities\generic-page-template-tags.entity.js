"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericPageTemplateTag = void 0;
const typeorm_1 = require("typeorm");
let GenericPageTemplateTag = class GenericPageTemplateTag {
    template_id;
    tag;
};
exports.GenericPageTemplateTag = GenericPageTemplateTag;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', length: 36 }),
    __metadata("design:type", String)
], GenericPageTemplateTag.prototype, "template_id", void 0);
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', length: 50 }),
    __metadata("design:type", String)
], GenericPageTemplateTag.prototype, "tag", void 0);
exports.GenericPageTemplateTag = GenericPageTemplateTag = __decorate([
    (0, typeorm_1.Entity)('generic_page_template_tags')
], GenericPageTemplateTag);
//# sourceMappingURL=generic-page-template-tags.entity.js.map