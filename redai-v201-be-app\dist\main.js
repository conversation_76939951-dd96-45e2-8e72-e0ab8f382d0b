"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const _app_1 = require("./app.module");
const swagger_1 = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const swagger_2 = require("./common/swagger");
const config_1 = require("@nestjs/config");
const dotenv = require("dotenv");
const typeorm_transactional_config_1 = require("./config/typeorm-transactional.config");
const cors_config_1 = require("./common/filters/cors.config");
const bodyParser = require("body-parser");
const custom_validation_pipe_1 = require("./common/pipes/custom-validation.pipe");
(0, typeorm_transactional_config_1.initializeTypeOrmTransactional)();
dotenv.config();
async function bootstrap() {
    const app = await core_1.NestFactory.create(_app_1.AppModule);
    app.use(bodyParser.json({ limit: '50mb' }));
    app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));
    app.enableVersioning({
        type: common_1.VersioningType.URI,
        defaultVersion: '1',
    });
    app.useGlobalPipes(new custom_validation_pipe_1.CustomValidationPipe());
    app.enableCors(cors_config_1.corsConfig);
    const configService = app.get(config_1.ConfigService);
    const swaggerConfig = (0, swagger_2.createSwaggerConfig)(configService);
    const document = swagger_1.SwaggerModule.createDocument(app, swaggerConfig);
    swagger_1.SwaggerModule.setup('api/docs', app, document, swagger_2.swaggerCustomOptions);
    const port = process.env.PORT ?? 3000;
    await app.listen(port);
    console.log(`Application is running on: http://localhost:${port}`);
    console.log(`Swagger documentation is available at: http://localhost:${port}/api/docs`);
}
bootstrap().catch((err) => {
    console.error(err);
    process.exit(1);
});
//# sourceMappingURL=main.js.map