{"version": 3, "file": "conversation-thread.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/chat/services/conversation-thread.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,qCAAqC;AACrC,iEAAsD;AACtD,yDAAoD;AACpD,2DAAkD;AAClD,qEAAkE;AAClE,4EAOwC;AAIjC,IAAM,yBAAyB,iCAA/B,MAAM,yBAAyB;IAGP;IAFZ,MAAM,GAAG,IAAI,eAAM,CAAC,2BAAyB,CAAC,IAAI,CAAC,CAAC;IAErE,YAA6B,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IAAG,CAAC;IAMjD,AAAN,KAAK,CAAC,MAAM,CAAC,SAAsC;QACjD,IAAI,CAAC;YACH,MAAM,KAAK,GAAG;;;;OAIb,CAAC;YAEF,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;YAE3F,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACnC,MAAM,IAAI,yBAAY,CACpB,mCAAgB,CAAC,sBAAsB,EACvC,kDAAkD,CACnD,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACzB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,MAAM,CAAC,SAAS,aAAa,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;YAEhG,OAAO,IAAA,mCAAe,EAAC,uDAA6B,EAAE;gBACpD,QAAQ,EAAE,MAAM,CAAC,SAAS;gBAC1B,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,MAAM,EAAE,MAAM,CAAC,OAAO;gBACtB,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC;gBACtC,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC;aACvC,EAAE,EAAE,uBAAuB,EAAE,IAAI,EAAE,CAAC,CAAC;QAExC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAEvF,IAAI,KAAK,YAAY,yBAAY,EAAE,CAAC;gBAClC,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,yBAAY,CACpB,mCAAgB,CAAC,sBAAsB,EACvC,yCAAyC,KAAK,CAAC,OAAO,EAAE,CACzD,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,aAAa,CAAC,QAAwC,EAAE,MAAc;QAC1E,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,EAAE,MAAM,GAAG,YAAY,EAAE,aAAa,GAAG,MAAM,EAAE,GAAG,QAAQ,CAAC;QACjG,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAGlC,IAAI,WAAW,GAAG,oBAAoB,CAAC;QACvC,MAAM,WAAW,GAAU,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,IAAI,MAAM,EAAE,CAAC;YACX,WAAW,IAAI,oBAAoB,UAAU,EAAE,CAAC;YAChD,WAAW,CAAC,IAAI,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;YAChC,UAAU,EAAE,CAAC;QACf,CAAC;QAGD,MAAM,iBAAiB,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;QAC/D,MAAM,UAAU,GAAG,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC;QAC9E,MAAM,iBAAiB,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC;QAC3F,MAAM,WAAW,GAAG,YAAY,UAAU,IAAI,iBAAiB,EAAE,CAAC;QAGlE,MAAM,UAAU,GAAG;;;QAGf,WAAW;KACd,CAAC;QAEF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QACzE,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAG7C,MAAM,YAAY,GAAG;;;QAGjB,WAAW;QACX,WAAW;eACJ,UAAU,YAAY,UAAU,GAAG,CAAC;KAC9C,CAAC;QAEF,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAChC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;QAE7E,MAAM,OAAO,GAAoC,aAAa,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE,CACjF,IAAA,mCAAe,EAAC,uDAA6B,EAAE;YAC7C,QAAQ,EAAE,MAAM,CAAC,SAAS;YAC1B,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,MAAM,EAAE,MAAM,CAAC,OAAO;YACtB,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC;YACtC,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC;SACvC,EAAE,EAAE,uBAAuB,EAAE,IAAI,EAAE,CAAC,CACtC,CAAC;QAEF,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAE5C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,OAAO,CAAC,MAAM,qBAAqB,MAAM,UAAU,IAAI,IAAI,UAAU,GAAG,CAAC,CAAC;QAEzG,OAAO;YACL,KAAK,EAAE,OAAO;YACd,IAAI,EAAE;gBACJ,UAAU,EAAE,KAAK;gBACjB,SAAS,EAAE,OAAO,CAAC,MAAM;gBACzB,YAAY,EAAE,KAAK;gBACnB,UAAU;gBACV,WAAW,EAAE,IAAI;gBACjB,QAAQ,EAAE,KAAK,GAAG,CAAC;aACpB;SACF,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,OAAO,CAAC,QAAgB,EAAE,MAAc;QAC5C,MAAM,KAAK,GAAG;;;;KAIb,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;QAEtE,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,yBAAY,CACpB,mCAAgB,CAAC,gBAAgB,EACjC,uBAAuB,QAAQ,6BAA6B,CAC7D,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAEzB,OAAO,IAAA,mCAAe,EAAC,uDAA6B,EAAE;YACpD,QAAQ,EAAE,MAAM,CAAC,SAAS;YAC1B,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,MAAM,EAAE,MAAM,CAAC,OAAO;YACtB,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC;YACtC,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC;SACvC,EAAE,EAAE,uBAAuB,EAAE,IAAI,EAAE,CAAC,CAAC;IACxC,CAAC;IAKD,KAAK,CAAC,MAAM,CAAC,QAAgB,EAAE,MAAc,EAAE,SAAsC;QAEnF,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAErC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,KAAK,GAAG;;;;;KAKb,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;QAE3F,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,yBAAY,CACpB,mCAAgB,CAAC,gBAAgB,EACjC,uBAAuB,QAAQ,6BAA6B,CAC7D,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACzB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,QAAQ,aAAa,MAAM,EAAE,CAAC,CAAC;QAE9E,OAAO,IAAA,mCAAe,EAAC,uDAA6B,EAAE;YACpD,QAAQ,EAAE,MAAM,CAAC,SAAS;YAC1B,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,MAAM,EAAE,MAAM,CAAC,OAAO;YACtB,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC;YACtC,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC;SACvC,EAAE,EAAE,uBAAuB,EAAE,IAAI,EAAE,CAAC,CAAC;IACxC,CAAC;IAKD,KAAK,CAAC,MAAM,CAAC,QAAgB,EAAE,MAAc;QAE3C,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAErC,MAAM,KAAK,GAAG;;;KAGb,CAAC;QAEF,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;QAEvD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,QAAQ,aAAa,MAAM,EAAE,CAAC,CAAC;IAChF,CAAC;IAKD,KAAK,CAAC,MAAM,CAAC,QAAgB,EAAE,MAAc;QAC3C,MAAM,KAAK,GAAG;;;;KAIb,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;QACtE,OAAO,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IACrC,CAAC;IAKD,KAAK,CAAC,QAAQ,CAAC,MAAc;QAC3B,MAAM,KAAK,GAAG;;;;;;KAMb,CAAC;QAEF,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAC5D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC;QAE1E,OAAO;YACL,YAAY,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC;YAC/C,aAAa,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;SAClD,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,iBAAiB,CACrB,QAAgB,EAChB,MAAc,EACd,QAAmC;QAGnC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEpD,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,IAAI,EAAE,MAAM,GAAG,WAAW,EAAE,aAAa,GAAG,MAAM,EAAE,GAAG,QAAQ,CAAC;QAC9F,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAGlC,IAAI,WAAW,GAAG,wBAAwB,CAAC;QAC3C,MAAM,WAAW,GAAU,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,IAAI,IAAI,EAAE,CAAC;YACT,WAAW,IAAI,kBAAkB,UAAU,EAAE,CAAC;YAC9C,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvB,UAAU,EAAE,CAAC;QACf,CAAC;QAGD,MAAM,wBAAwB,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QACvD,MAAM,UAAU,GAAG,wBAAwB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC;QACpF,MAAM,iBAAiB,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC;QAC3F,MAAM,WAAW,GAAG,cAAc,UAAU,IAAI,iBAAiB,EAAE,CAAC;QAGpE,MAAM,UAAU,GAAG;;;QAGf,WAAW;KACd,CAAC;QAEF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QACzE,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAG7C,MAAM,aAAa,GAAG;;;;;;;;;QASlB,WAAW;QACX,WAAW;eACJ,UAAU,YAAY,UAAU,GAAG,CAAC;KAC9C,CAAC;QAEF,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAChC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QAE/E,MAAM,QAAQ,GAA+B,cAAc,CAAC,GAAG,CAAC,CAAC,OAAY,EAAE,EAAE,CAC/E,IAAA,mCAAe,EAAC,kDAAwB,EAAE;YACxC,SAAS,EAAE,OAAO,CAAC,UAAU;YAC7B,QAAQ,EAAE,OAAO,CAAC,SAAS;YAC3B,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;YACtC,SAAS,EAAE,OAAO,CAAC,UAAU;SAC9B,EAAE,EAAE,uBAAuB,EAAE,IAAI,EAAE,CAAC,CACtC,CAAC;QAEF,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAE5C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,QAAQ,CAAC,MAAM,wBAAwB,QAAQ,UAAU,IAAI,IAAI,UAAU,GAAG,CAAC,CAAC;QAE/G,OAAO;YACL,KAAK,EAAE,QAAQ;YACf,IAAI,EAAE;gBACJ,UAAU,EAAE,KAAK;gBACjB,SAAS,EAAE,QAAQ,CAAC,MAAM;gBAC1B,YAAY,EAAE,KAAK;gBACnB,UAAU;gBACV,WAAW,EAAE,IAAI;gBACjB,QAAQ,EAAE,KAAK,GAAG,CAAC;aACpB;SACF,CAAC;IACJ,CAAC;CACF,CAAA;AAzUY,8DAAyB;AAS9B;IADL,IAAA,qCAAa,GAAE;;qCACQ,qDAA2B;;uDAyClD;oCAlDU,yBAAyB;IADrC,IAAA,mBAAU,GAAE;qCAI8B,oBAAU;GAHxC,yBAAyB,CAyUrC"}