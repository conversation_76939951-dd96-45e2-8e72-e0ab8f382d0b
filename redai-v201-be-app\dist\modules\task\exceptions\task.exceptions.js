"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TASK_ERROR_CODES = void 0;
const common_1 = require("../../../common");
const common_2 = require("@nestjs/common");
exports.TASK_ERROR_CODES = {
    TASK_NOT_FOUND: new common_1.ErrorCode(10000, 'Không tìm thấy nhiệm vụ', common_2.HttpStatus.NOT_FOUND),
    TASK_CREATION_FAILED: new common_1.ErrorCode(10001, 'Tạo nhiệm vụ thất bại', common_2.HttpStatus.INTERNAL_SERVER_ERROR),
    TASK_UPDATE_FAILED: new common_1.ErrorCode(10002, 'Cập nhật nhiệm vụ thất bại', common_2.HttpStatus.INTERNAL_SERVER_ERROR),
    TASK_DELETE_FAILED: new common_1.ErrorCode(10003, 'Xóa nhiệm vụ thất bại', common_2.HttpStatus.INTERNAL_SERVER_ERROR),
    TASK_FETCH_FAILED: new common_1.ErrorCode(10004, 'Lấy thông tin nhiệm vụ thất bại', common_2.HttpStatus.INTERNAL_SERVER_ERROR),
    TASK_UNAUTHORIZED: new common_1.ErrorCode(10010, 'Không có quyền truy cập nhiệm vụ này', common_2.HttpStatus.FORBIDDEN),
    TASK_INVALID_OWNER: new common_1.ErrorCode(10011, 'Bạn không phải là chủ sở hữu của nhiệm vụ này', common_2.HttpStatus.FORBIDDEN),
    TASK_INVALID_STATUS: new common_1.ErrorCode(10020, 'Trạng thái nhiệm vụ không hợp lệ', common_2.HttpStatus.BAD_REQUEST),
    TASK_ALREADY_COMPLETED: new common_1.ErrorCode(10021, 'Nhiệm vụ đã hoàn thành, không thể thay đổi', common_2.HttpStatus.BAD_REQUEST),
    TASK_ALREADY_CANCELLED: new common_1.ErrorCode(10022, 'Nhiệm vụ đã bị hủy, không thể thay đổi', common_2.HttpStatus.BAD_REQUEST),
    TASK_INVALID_DATA: new common_1.ErrorCode(10030, 'Dữ liệu nhiệm vụ không hợp lệ', common_2.HttpStatus.BAD_REQUEST),
    TASK_MISSING_AGENT: new common_1.ErrorCode(10031, 'Thiếu thông tin agent cho nhiệm vụ', common_2.HttpStatus.BAD_REQUEST),
    TASK_AGENT_NOT_FOUND: new common_1.ErrorCode(10032, 'Không tìm thấy agent cho nhiệm vụ', common_2.HttpStatus.NOT_FOUND),
    TASK_NAME_REQUIRED: new common_1.ErrorCode(10033, 'Tên nhiệm vụ là bắt buộc', common_2.HttpStatus.BAD_REQUEST),
    TASK_NAME_TOO_LONG: new common_1.ErrorCode(10034, 'Tên nhiệm vụ quá dài (tối đa 255 ký tự)', common_2.HttpStatus.BAD_REQUEST),
    TASK_LIMIT_EXCEEDED: new common_1.ErrorCode(10040, 'Đã vượt quá giới hạn số lượng nhiệm vụ', common_2.HttpStatus.BAD_REQUEST),
};
//# sourceMappingURL=task.exceptions.js.map