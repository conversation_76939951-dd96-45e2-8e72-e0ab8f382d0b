"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskAdminModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const entities_1 = require("../entities");
const repositories_1 = require("../repositories");
const controllers_1 = require("./controllers");
const services_1 = require("./services");
let TaskAdminModule = class TaskAdminModule {
};
exports.TaskAdminModule = TaskAdminModule;
exports.TaskAdminModule = TaskAdminModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                entities_1.AdminTask,
                entities_1.AdminStep,
                entities_1.AdminTaskExecution,
                entities_1.AdminStepConnection,
            ])
        ],
        controllers: [
            controllers_1.AdminTaskController,
            controllers_1.AdminStepController,
            controllers_1.AdminConnectionController
        ],
        providers: [
            repositories_1.AdminTaskRepository,
            repositories_1.AdminStepRepository,
            repositories_1.AdminTaskExecutionRepository,
            repositories_1.AdminStepConnectionRepository,
            services_1.AdminTaskService,
            services_1.AdminStepService,
            services_1.AdminConnectionService
        ],
        exports: [
            services_1.AdminTaskService,
            services_1.AdminStepService,
            services_1.AdminConnectionService
        ],
    })
], TaskAdminModule);
//# sourceMappingURL=task-admin.module.js.map