"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "CreateUserTable1698765432100", {
    enumerable: true,
    get: function() {
        return CreateUserTable1698765432100;
    }
});
const _typeorm = require("typeorm");
let CreateUserTable1698765432100 = class CreateUserTable1698765432100 {
    async up(queryRunner) {
        await queryRunner.createTable(new _typeorm.Table({
            name: 'users',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'fullName',
                    type: 'varchar',
                    length: '100'
                },
                {
                    name: 'email',
                    type: 'varchar',
                    isUnique: true
                },
                {
                    name: 'password',
                    type: 'varchar'
                },
                {
                    name: 'isActive',
                    type: 'boolean',
                    default: false
                },
                {
                    name: 'avatarUrl',
                    type: 'varchar',
                    isNullable: true
                },
                {
                    name: 'createdAt',
                    type: 'timestamp',
                    default: 'now()'
                },
                {
                    name: 'updatedAt',
                    type: 'timestamp',
                    default: 'now()'
                }
            ]
        }), true);
        // Tạo extension uuid-ossp nếu chưa có
        await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`);
    }
    async down(queryRunner) {
        await queryRunner.dropTable('users');
    }
};

//# sourceMappingURL=1698765432100-CreateUserTable.js.map