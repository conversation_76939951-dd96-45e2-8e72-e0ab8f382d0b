"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get InputModality () {
        return InputModality;
    },
    get ModelFeature () {
        return ModelFeature;
    },
    get ModelProviderEnum () {
        return ModelProviderEnum;
    },
    get OutputModality () {
        return OutputModality;
    },
    get SamplingParameter () {
        return SamplingParameter;
    }
});
var ModelProviderEnum = /*#__PURE__*/ function(ModelProviderEnum) {
    ModelProviderEnum["OPENAI"] = "OPENAI";
    ModelProviderEnum["XAI"] = "XAI";
    ModelProviderEnum["ANTHROPIC"] = "ANTHROPIC";
    ModelProviderEnum["GOOGLE"] = "GOOGLE";
    ModelProviderEnum["DEEPSEEK"] = "DEEPSEEK";
    return ModelProviderEnum;
}({});
var InputModality = /*#__PURE__*/ function(InputModality) {
    InputModality["TEXT"] = "text";
    InputModality["IMAGE"] = "image";
    InputModality["AUDIO"] = "audio";
    InputModality["VIDEO"] = "video";
    return InputModality;
}({});
var OutputModality = /*#__PURE__*/ function(OutputModality) {
    OutputModality["TEXT"] = "text";
    OutputModality["IMAGE"] = "image";
    OutputModality["AUDIO"] = "audio";
    OutputModality["VIDEO"] = "video";
    return OutputModality;
}({});
var SamplingParameter = /*#__PURE__*/ function(SamplingParameter) {
    SamplingParameter["TEMPERATURE"] = "temperature";
    SamplingParameter["TOP_P"] = "top_p";
    SamplingParameter["TOP_K"] = "top_k";
    SamplingParameter["MAX_TOKENS"] = "max_tokens";
    SamplingParameter["MAX_OUTPUT_TOKENS"] = "max_output_tokens";
    return SamplingParameter;
}({});
var ModelFeature = /*#__PURE__*/ function(ModelFeature) {
    ModelFeature["TOOL_CALL"] = "tool_call";
    ModelFeature["PARALLEL_TOOL_CALL"] = "parallel_tool_call";
    return ModelFeature;
}({});

//# sourceMappingURL=system-agent-config.interface.js.map