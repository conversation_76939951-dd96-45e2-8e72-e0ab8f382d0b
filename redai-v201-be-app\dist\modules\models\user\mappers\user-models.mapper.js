"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserModelsMapper = void 0;
class UserModelsMapper {
    static convertPricing(pricing) {
        if (!pricing || typeof pricing !== 'object') {
            return { inputRate: 0, outputRate: 0 };
        }
        return {
            inputRate: pricing.inputRate || pricing.input_rate || 0,
            outputRate: pricing.outputRate || pricing.output_rate || 0,
        };
    }
    static toResponseDto(entity) {
        return {
            id: entity.id,
            modelId: entity.modelId,
            provider: entity.provider,
            inputModalities: Array.isArray(entity.inputModalities) ? entity.inputModalities : [],
            outputModalities: Array.isArray(entity.outputModalities) ? entity.outputModalities : [],
            samplingParameters: Array.isArray(entity.samplingParameters) ? entity.samplingParameters : [],
            features: Array.isArray(entity.features) ? entity.features : [],
            basePricing: this.convertPricing(entity.basePricing),
            fineTunePricing: this.convertPricing(entity.fineTunePricing),
            trainingPricing: entity.trainingPricing || 0,
        };
    }
    static toResponseDtoList(entities) {
        if (!entities || !Array.isArray(entities)) {
            return [];
        }
        return entities
            .map(entity => this.toResponseDto(entity))
            .filter(dto => dto !== null);
    }
}
exports.UserModelsMapper = UserModelsMapper;
//# sourceMappingURL=user-models.mapper.js.map