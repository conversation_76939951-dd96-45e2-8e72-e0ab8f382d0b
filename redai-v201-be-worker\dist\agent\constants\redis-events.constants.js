/**
 * Redis Event Patterns for Pub/Sub Communication (Agent Module)
 * 
 * IMPORTANT: These constants must be kept identical to the ones in redai-v201-be-app
 * to maintain consistency across separate codebases.
 * 
 * These constants define the event patterns used for Redis pub/sub communication
 * between the backend API and worker services.
 */ /**
 * Event patterns for Redis pub/sub communication between backend and worker
 */ "use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "REDIS_EVENTS", {
    enumerable: true,
    get: function() {
        return REDIS_EVENTS;
    }
});
const REDIS_EVENTS = {
    /**
   * Trigger event to start processing a run
   * Published by: Backend API (Chat Module)
   * Consumed by: Worker Service (Agent Module)
   */ RUN_TRIGGER: 'run.trigger',
    /**
   * Cancel event to stop processing a run
   * Published by: Backend API (Chat Module)
   * Consumed by: Worker Service (Agent Module)
   */ RUN_CANCEL: 'run.cancel',
    /**
   * Status update event for run progress
   * Published by: Worker Service (Agent Module)
   * Consumed by: Backend API (optional)
   */ RUN_STATUS_UPDATE: 'run.status.update',
    /**
   * Completion event when run finishes
   * Published by: Worker Service (Agent Module)
   * Consumed by: Backend API (optional)
   */ RUN_COMPLETED: 'run.completed',
    /**
   * Error event when run fails
   * Published by: Worker Service (Agent Module)
   * Consumed by: Backend API (optional)
   */ RUN_ERROR: 'run.error'
};

//# sourceMappingURL=redis-events.constants.js.map