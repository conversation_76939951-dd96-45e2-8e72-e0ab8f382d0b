"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RuleContractResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const rule_contract_entity_1 = require("../../entities/rule-contract.entity");
class RuleContractResponseDto {
    id;
    userId;
    userName;
    userEmail;
    contractCode;
    status;
    type;
    contractUrl;
    createdAt;
    userSignatureAt;
    adminSignatureAt;
}
exports.RuleContractResponseDto = RuleContractResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID hợp đồng',
        example: 1,
        type: Number,
    }),
    __metadata("design:type", Number)
], RuleContractResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID người dùng',
        example: 123,
        type: Number,
    }),
    __metadata("design:type", Number)
], RuleContractResponseDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên người dùng',
        example: 'Nguyễn Văn A',
        type: String,
    }),
    __metadata("design:type", String)
], RuleContractResponseDto.prototype, "userName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email người dùng',
        example: '<EMAIL>',
        type: String,
    }),
    __metadata("design:type", String)
], RuleContractResponseDto.prototype, "userEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mã hợp đồng',
        example: 'HD-123',
        type: String,
    }),
    __metadata("design:type", String)
], RuleContractResponseDto.prototype, "contractCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái hợp đồng',
        enum: rule_contract_entity_1.ContractStatusEnum,
        example: rule_contract_entity_1.ContractStatusEnum.APPROVED,
    }),
    __metadata("design:type", String)
], RuleContractResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại hợp đồng',
        enum: rule_contract_entity_1.ContractTypeEnum,
        example: rule_contract_entity_1.ContractTypeEnum.INDIVIDUAL,
    }),
    __metadata("design:type", String)
], RuleContractResponseDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Đường dẫn file hợp đồng',
        example: 'https://example.com/contract.pdf',
        type: String,
    }),
    __metadata("design:type", String)
], RuleContractResponseDto.prototype, "contractUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo hợp đồng (timestamp)',
        example: 1625097600000,
        type: Number,
    }),
    __metadata("design:type", Number)
], RuleContractResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian người dùng ký hợp đồng (timestamp)',
        example: 1625097600000,
        type: Number,
    }),
    __metadata("design:type", Number)
], RuleContractResponseDto.prototype, "userSignatureAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian admin ký hợp đồng (timestamp)',
        example: 1625097600000,
        type: Number,
    }),
    __metadata("design:type", Number)
], RuleContractResponseDto.prototype, "adminSignatureAt", void 0);
//# sourceMappingURL=rule-contract-response.dto.js.map