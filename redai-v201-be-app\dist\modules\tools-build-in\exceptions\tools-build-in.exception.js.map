{"version": 3, "file": "tools-build-in.exception.js", "sourceRoot": "", "sources": ["../../../../src/modules/tools-build-in/exceptions/tools-build-in.exception.ts"], "names": [], "mappings": ";;;AAAA,2CAA4C;AAC5C,2DAA+C;AAElC,QAAA,0BAA0B,GAAG;IAExC,kBAAkB,EAAE,IAAI,sBAAS,CAC/B,KAAK,EACL,0BAA0B,EAC1B,mBAAU,CAAC,qBAAqB,CACjC;IACD,mBAAmB,EAAE,IAAI,sBAAS,CAChC,KAAK,EACL,uBAAuB,EACvB,mBAAU,CAAC,YAAY,CACxB;IACD,yBAAyB,EAAE,IAAI,sBAAS,CACtC,KAAK,EACL,kCAAkC,EAClC,mBAAU,CAAC,SAAS,CACrB;IAGD,mBAAmB,EAAE,IAAI,sBAAS,CAChC,KAAK,EACL,+BAA+B,EAC/B,mBAAU,CAAC,qBAAqB,CACjC;IACD,sBAAsB,EAAE,IAAI,sBAAS,CACnC,KAAK,EACL,2BAA2B,EAC3B,mBAAU,CAAC,qBAAqB,CACjC;IACD,iBAAiB,EAAE,IAAI,sBAAS,CAC9B,KAAK,EACL,+BAA+B,EAC/B,mBAAU,CAAC,qBAAqB,CACjC;IACD,WAAW,EAAE,IAAI,sBAAS,CACxB,KAAK,EACL,kBAAkB,EAClB,mBAAU,CAAC,WAAW,CACvB;IAGD,mBAAmB,EAAE,IAAI,sBAAS,CAChC,KAAK,EACL,+BAA+B,EAC/B,mBAAU,CAAC,qBAAqB,CACjC;IACD,oBAAoB,EAAE,IAAI,sBAAS,CACjC,KAAK,EACL,0BAA0B,EAC1B,mBAAU,CAAC,qBAAqB,CACjC;IACD,0BAA0B,EAAE,IAAI,sBAAS,CACvC,KAAK,EACL,6BAA6B,EAC7B,mBAAU,CAAC,qBAAqB,CACjC;IAGD,iBAAiB,EAAE,IAAI,sBAAS,CAC9B,KAAK,EACL,yBAAyB,EACzB,mBAAU,CAAC,SAAS,CACrB;IACD,mBAAmB,EAAE,IAAI,sBAAS,CAChC,KAAK,EACL,gCAAgC,EAChC,mBAAU,CAAC,qBAAqB,CACjC;IACD,oBAAoB,EAAE,IAAI,sBAAS,CACjC,KAAK,EACL,2BAA2B,EAC3B,mBAAU,CAAC,qBAAqB,CACjC;IACD,oBAAoB,EAAE,IAAI,sBAAS,CACjC,KAAK,EACL,mCAAmC,EACnC,mBAAU,CAAC,qBAAqB,CACjC;IACD,sBAAsB,EAAE,IAAI,sBAAS,CACnC,KAAK,EACL,0BAA0B,EAC1B,mBAAU,CAAC,qBAAqB,CACjC;IACD,qBAAqB,EAAE,IAAI,sBAAS,CAClC,KAAK,EACL,kCAAkC,EAClC,mBAAU,CAAC,SAAS,CACrB;IACD,cAAc,EAAE,IAAI,sBAAS,CAC3B,KAAK,EACL,qBAAqB,EACrB,mBAAU,CAAC,WAAW,CACvB;IAGD,kBAAkB,EAAE,IAAI,sBAAS,CAC/B,KAAK,EACL,2BAA2B,EAC3B,mBAAU,CAAC,SAAS,CACrB;IACD,oBAAoB,EAAE,IAAI,sBAAS,CACjC,KAAK,EACL,kCAAkC,EAClC,mBAAU,CAAC,qBAAqB,CACjC;IACD,oBAAoB,EAAE,IAAI,sBAAS,CACjC,KAAK,EACL,2BAA2B,EAC3B,mBAAU,CAAC,SAAS,CACrB;IACD,sBAAsB,EAAE,IAAI,sBAAS,CACnC,KAAK,EACL,kCAAkC,EAClC,mBAAU,CAAC,qBAAqB,CACjC;IACD,uBAAuB,EAAE,IAAI,sBAAS,CACpC,KAAK,EACL,6BAA6B,EAC7B,mBAAU,CAAC,qBAAqB,CACjC;IACD,qBAAqB,EAAE,IAAI,sBAAS,CAClC,KAAK,EACL,mCAAmC,EACnC,mBAAU,CAAC,qBAAqB,CACjC;IAGD,gBAAgB,EAAE,IAAI,sBAAS,CAC7B,KAAK,EACL,mBAAmB,EACnB,mBAAU,CAAC,qBAAqB,CACjC;IACD,cAAc,EAAE,IAAI,sBAAS,CAC3B,KAAK,EACL,iBAAiB,EACjB,mBAAU,CAAC,qBAAqB,CACjC;IACD,uBAAuB,EAAE,IAAI,sBAAS,CACpC,KAAK,EACL,2BAA2B,EAC3B,mBAAU,CAAC,qBAAqB,CACjC;IACD,mBAAmB,EAAE,IAAI,sBAAS,CAChC,KAAK,EACL,2BAA2B,EAC3B,mBAAU,CAAC,qBAAqB,CACjC;IACD,iBAAiB,EAAE,IAAI,sBAAS,CAC9B,KAAK,EACL,yBAAyB,EACzB,mBAAU,CAAC,qBAAqB,CACjC;IAGD,eAAe,EAAE,IAAI,sBAAS,CAC5B,KAAK,EACL,sBAAsB,EACtB,mBAAU,CAAC,SAAS,CACrB;IACD,oBAAoB,EAAE,IAAI,sBAAS,CACjC,KAAK,EACL,2BAA2B,EAC3B,mBAAU,CAAC,SAAS,CACrB;IACD,wBAAwB,EAAE,IAAI,sBAAS,CACrC,KAAK,EACL,sCAAsC,EACtC,mBAAU,CAAC,qBAAqB,CACjC;CACF,CAAC"}