"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UserDataFineTuneService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserDataFineTuneService = void 0;
const common_1 = require("../../../../common");
const cdn_service_1 = require("../../../../shared/services/cdn.service");
const s3_service_1 = require("../../../../shared/services/s3.service");
const utils_1 = require("../../../../shared/utils");
const file_jsonl_type_util_1 = require("../../../../shared/utils/file/file-jsonl-type.util");
const response_1 = require("../../../../common/response");
const common_2 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const data_fine_tune_status_enum_1 = require("../../constants/data-fine-tune-status.enum");
const exceptions_1 = require("../../exceptions");
const user_data_fine_tune_repository_1 = require("../../repositories/user-data-fine-tune.repository");
const user_data_fine_tune_mapper_1 = require("../mappers/user-data-fine-tune.mapper");
let UserDataFineTuneService = UserDataFineTuneService_1 = class UserDataFineTuneService {
    userDataFineTuneRepository;
    s3Service;
    cdnService;
    logger = new common_2.Logger(UserDataFineTuneService_1.name);
    constructor(userDataFineTuneRepository, s3Service, cdnService) {
        this.userDataFineTuneRepository = userDataFineTuneRepository;
        this.s3Service = s3Service;
        this.cdnService = cdnService;
    }
    async create(userId, createDto) {
        this.logger.log(`Creating data fine tune for user ${userId}`);
        const existsByName = await this.userDataFineTuneRepository.existsByName(createDto.name, userId);
        if (existsByName) {
            throw new common_1.AppException(exceptions_1.MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_NAME_EXISTS);
        }
        const trainDatasetKey = (0, utils_1.generateS3Key)({
            baseFolder: userId.toString(),
            categoryFolder: utils_1.CategoryFolderEnum.DATA_FINE_TUNE,
            fileName: `train-${userId}.jsonl`,
            useTimeFolder: true,
        });
        const trainUploadUrl = await this.s3Service.createPresignedWithID(trainDatasetKey, utils_1.TimeIntervalEnum.ONE_HOUR, file_jsonl_type_util_1.FileJsonlType.getMimeType(createDto.trainDataset), utils_1.FileSizeEnum.TEN_MB);
        let validDatasetKey = null;
        let validUploadUrl = null;
        if (createDto.validDataset) {
            validDatasetKey = (0, utils_1.generateS3Key)({
                baseFolder: userId.toString(),
                categoryFolder: utils_1.CategoryFolderEnum.DATA_FINE_TUNE,
                fileName: `valid-${userId}.jsonl`,
                useTimeFolder: true,
            });
            validUploadUrl = await this.s3Service.createPresignedWithID(validDatasetKey, utils_1.TimeIntervalEnum.ONE_HOUR, file_jsonl_type_util_1.FileJsonlType.getMimeType(createDto.validDataset), utils_1.FileSizeEnum.TEN_MB);
        }
        const newDataset = this.userDataFineTuneRepository.create({
            name: createDto.name,
            description: createDto.description,
            trainDataset: trainDatasetKey,
            validDataset: validDatasetKey,
            userId,
            provider: createDto.provider,
            status: data_fine_tune_status_enum_1.DataFineTuneStatus.PENDING
        });
        const savedDataset = await this.userDataFineTuneRepository.save(newDataset);
        return response_1.ApiResponseDto.success({ id: savedDataset.id, trainUploadUrl, validUploadUrl });
    }
    async findAll(userId, queryDto) {
        this.logger.log(`Getting data fine tune list for user ${userId}`);
        const result = await this.userDataFineTuneRepository.findWithPagination(queryDto, userId);
        const items = user_data_fine_tune_mapper_1.UserDataFineTuneMapper.toResponseDtoList(result.items, this.cdnService);
        return response_1.ApiResponseDto.paginated({
            items,
            meta: result.meta
        });
    }
    async findOne(userId, id) {
        this.logger.log(`Getting data fine tune detail ${id} for user ${userId}`);
        return response_1.ApiResponseDto.success({});
    }
    async update(userId, id, updateDto) {
        this.logger.log(`Updating data fine tune ${id} for user ${userId}`);
        const existingDataset = await this.userDataFineTuneRepository.findByIdAndUserId(id, userId);
        if (!existingDataset) {
            throw new common_1.AppException(exceptions_1.MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_NOT_FOUND);
        }
        if (updateDto.name && updateDto.name !== existingDataset.name) {
            const nameExists = await this.userDataFineTuneRepository.existsByName(updateDto.name, userId, id);
            if (nameExists) {
                throw new common_1.AppException(exceptions_1.MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_NAME_EXISTS);
            }
        }
        let trainUploadUrl = null;
        let validUploadUrl = null;
        let trainDatasetKey = existingDataset.trainDataset;
        let validDatasetKey = existingDataset.validDataset;
        if (updateDto.trainDataset) {
            trainDatasetKey = (0, utils_1.generateS3Key)({
                baseFolder: userId.toString(),
                categoryFolder: utils_1.CategoryFolderEnum.DATA_FINE_TUNE,
                fileName: `train-${userId}-${Date.now()}.jsonl`,
                useTimeFolder: true,
            });
            trainUploadUrl = await this.s3Service.createPresignedWithID(trainDatasetKey, utils_1.TimeIntervalEnum.ONE_HOUR, file_jsonl_type_util_1.FileJsonlType.getMimeType(updateDto.trainDataset), utils_1.FileSizeEnum.TEN_MB);
        }
        if (updateDto.validDataset) {
            validDatasetKey = (0, utils_1.generateS3Key)({
                baseFolder: userId.toString(),
                categoryFolder: utils_1.CategoryFolderEnum.DATA_FINE_TUNE,
                fileName: `valid-${userId}-${Date.now()}.jsonl`,
                useTimeFolder: true,
            });
            validUploadUrl = await this.s3Service.createPresignedWithID(validDatasetKey, utils_1.TimeIntervalEnum.ONE_HOUR, file_jsonl_type_util_1.FileJsonlType.getMimeType(updateDto.validDataset), utils_1.FileSizeEnum.TEN_MB);
        }
        const updateData = {
            updatedAt: Date.now()
        };
        if (updateDto.name)
            updateData.name = updateDto.name;
        if (updateDto.description !== undefined)
            updateData.description = updateDto.description;
        if (updateDto.trainDataset) {
            updateData.trainDataset = trainDatasetKey;
            updateData.status = data_fine_tune_status_enum_1.DataFineTuneStatus.PENDING;
        }
        if (updateDto.validDataset) {
            updateData.validDataset = validDatasetKey;
            updateData.status = data_fine_tune_status_enum_1.DataFineTuneStatus.PENDING;
        }
        await this.userDataFineTuneRepository.update({ id, userId }, updateData);
        this.logger.log(`Dataset ${id} updated successfully for user ${userId}`);
        return response_1.ApiResponseDto.success({
            id,
            trainUploadUrl,
            validUploadUrl
        });
    }
    async remove(userId, id) {
        this.logger.log(`Removing data fine tune ${id} for user ${userId}`);
        const existingDataset = await this.userDataFineTuneRepository.findByIdAndUserId(id, userId);
        if (!existingDataset) {
            throw new common_1.AppException(exceptions_1.MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_NOT_FOUND);
        }
        const deleted = await this.userDataFineTuneRepository.softDeleteByIdAndUserId(id, userId);
        if (!deleted) {
            throw new common_1.AppException(exceptions_1.MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_NOT_FOUND);
        }
        return response_1.ApiResponseDto.success({ message: 'Xóa dataset fine tune thành công' });
    }
    async updateStatus(userId, id) {
        this.logger.log(`Updating dataset status ${id} for user`);
        const dataset = await this.userDataFineTuneRepository.findOne({
            where: {
                id,
                userId,
                deletedAt: (0, typeorm_1.IsNull)()
            }
        });
        if (!dataset) {
            throw new common_1.AppException(exceptions_1.MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_NOT_FOUND);
        }
        dataset.status = data_fine_tune_status_enum_1.DataFineTuneStatus.APPROVED;
        const trainExists = await this.s3Service.checkObjectExists(dataset.trainDataset);
        if (!trainExists) {
            dataset.status = data_fine_tune_status_enum_1.DataFineTuneStatus.ERROR;
        }
        if (dataset.validDataset) {
            const validExists = await this.s3Service.checkObjectExists(dataset.validDataset);
            if (!validExists) {
                dataset.status = data_fine_tune_status_enum_1.DataFineTuneStatus.ERROR;
            }
        }
        await this.userDataFineTuneRepository.update({ id, userId }, {
            status: dataset.status,
            updatedAt: Date.now()
        });
        this.logger.log(`Dataset ${id} status updated to for user ${userId}`);
        return response_1.ApiResponseDto.success({ id });
    }
    async urlUpload(userId, mime) {
        const key = (0, utils_1.generateS3Key)({
            baseFolder: userId.toString(),
            categoryFolder: utils_1.CategoryFolderEnum.DATA_FINE_TUNE,
            useTimeFolder: true,
        });
        const uploadUrl = await this.s3Service.createPresignedWithID(key, utils_1.TimeIntervalEnum.ONE_HOUR, utils_1.ImageType.getType(mime), utils_1.FileSizeEnum.TEN_MB);
        const viewUrl = this.cdnService.generateUrlView(key, utils_1.TimeIntervalEnum.ONE_HOUR);
        return response_1.ApiResponseDto.success({ uploadUrl, viewUrl });
    }
};
exports.UserDataFineTuneService = UserDataFineTuneService;
exports.UserDataFineTuneService = UserDataFineTuneService = UserDataFineTuneService_1 = __decorate([
    (0, common_2.Injectable)(),
    __metadata("design:paramtypes", [user_data_fine_tune_repository_1.UserDataFineTuneRepository,
        s3_service_1.S3Service,
        cdn_service_1.CdnService])
], UserDataFineTuneService);
//# sourceMappingURL=user-data-fine-tune.service.js.map