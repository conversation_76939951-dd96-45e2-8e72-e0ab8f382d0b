"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryGenericPageTemplateDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const query_dto_1 = require("../../../../common/dto/query.dto");
const generic_page_enum_1 = require("../../constants/generic-page.enum");
class QueryGenericPageTemplateDto extends query_dto_1.QueryDto {
    sortBy = generic_page_enum_1.GenericPageTemplateSortByEnum.CREATED_AT;
    category;
    tags;
}
exports.QueryGenericPageTemplateDto = QueryGenericPageTemplateDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sắp xếp theo trường',
        enum: generic_page_enum_1.GenericPageTemplateSortByEnum,
        default: generic_page_enum_1.GenericPageTemplateSortByEnum.CREATED_AT,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(generic_page_enum_1.GenericPageTemplateSortByEnum, {
        message: `sortBy phải là một trong các giá trị: ${Object.values(generic_page_enum_1.GenericPageTemplateSortByEnum).join(', ')}`,
    }),
    __metadata("design:type", String)
], QueryGenericPageTemplateDto.prototype, "sortBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Lọc theo danh mục',
        example: 'Form',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Danh mục phải là chuỗi' }),
    __metadata("design:type", String)
], QueryGenericPageTemplateDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Lọc theo tag (phân cách bằng dấu phẩy)',
        example: 'form,liên hệ',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Tags phải là chuỗi' }),
    __metadata("design:type", String)
], QueryGenericPageTemplateDto.prototype, "tags", void 0);
//# sourceMappingURL=query-generic-page-template.dto.js.map