"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get GraphState () {
        return GraphState;
    },
    get createReactAgent () {
        return createReactAgent;
    },
    get createReactAgentAnnotation () {
        return createReactAgentAnnotation;
    }
});
const _messages = require("@langchain/core/messages");
const _langgraph = require("@langchain/langgraph");
const _enums = require("../../enums");
const _prebuilt = require("@langchain/langgraph/prebuilt");
const _common = require("@nestjs/common");
const _helpers = require("./helpers");
const _constants = require("./constants");
const _universal = require("langchain/chat_models/universal");
const logger = new _common.Logger('ReactAgentExecutor');
const CHAT_MODEL_WITHOUT_PARALLEL_TOOL_CALL_PARAM = new Set([
    'o1',
    'o1-2024-12-17',
    'o1-preview-2024-09-12',
    'o1-pro',
    'o1-pro-2025-03-19',
    'o3',
    'o3-2025-04-16',
    'o4-mini',
    'o4-mini-2025-04-16',
    'o3-mini',
    'o3-mini-2025-01-31',
    'o1-mini',
    'o1-mini-2024-09-12'
]);
function createReactAgentAnnotation() {
    return _langgraph.Annotation.Root({
        messages: (0, _langgraph.Annotation)({
            reducer: _langgraph.messagesStateReducer,
            default: ()=>[]
        }),
        activeAgent: (0, _langgraph.Annotation)({
            reducer: (x, y)=>{
                logger.log(`transferring from ${x} to ${y}`);
                return y;
            },
            default: ()=>'supervisor'
        }),
        metadata: _langgraph.Annotation
    });
}
const GraphState = createReactAgentAnnotation();
function createReactAgent(params) {
    const { checkpointSaver, store } = params;
    const shouldContinue = (state, config)=>{
        const { messages } = state;
        const lastMessage = messages[messages.length - 1];
        if ((0, _messages.isAIMessage)(lastMessage)) {
            const aiMessage = lastMessage;
            if (!aiMessage.tool_calls || aiMessage.tool_calls.length === 0) {
                return _langgraph.END;
            }
            const alwaysApproveToolCall = config?.configurable?.alwaysApproveToolCall || false;
            return alwaysApproveToolCall ? 'tools' : 'humanReview';
        }
        return _langgraph.END;
    };
    const humanReviewNode = (state, config)=>{
        const { messages } = state;
        const lastMessage = messages[messages.length - 1];
        if (!lastMessage || !(0, _messages.isAIMessage)(lastMessage)) {
            throw new Error('Last message is not an AI message');
        }
        const aiMessage = lastMessage;
        // Check for errored tool calls (missing name or id)
        const erroredToolCalls = aiMessage.tool_calls?.filter((toolCall)=>!toolCall.name || toolCall.name.trim() === '' || !toolCall.id || toolCall.id.trim() === '') || [];
        if (erroredToolCalls.length > 0) {
            throw new Error(`Found ${erroredToolCalls.length} errored tool calls: ${JSON.stringify(erroredToolCalls)}. Tool calls must have both name and id.`);
        }
        // Note: If alwaysApproveToolCall is true, we wouldn't reach this node
        // as shouldContinue would have routed directly to tools
        // Display the tool calls to the user and ask for approval
        const toolCalls = aiMessage.tool_calls?.map((toolCall, index)=>{
            return `Tool ${index + 1}: ${toolCall.name}\nArguments: ${JSON.stringify(toolCall.args, null, 2)}`;
        }).join('\n\n') || '';
        const name = state.activeAgent === config.configurable?.supervisorAgentId ? 'supervisor' : 'worker';
        const { choice } = (0, _langgraph.interrupt)(JSON.stringify({
            role: name,
            prompt: `The AI wants to use the following tools:\n\n${toolCalls}\n\nDo you approve these tool calls? (yes/no/always)`,
            prompter: config?.configurable?.agentConfigMap?.[state.activeAgent]?.name || '',
            prompterId: state.activeAgent
        }));
        // Handle user response
        if (choice === 'always' || choice === 'yes') {
            // When user selects 'always', we just route to tools
            // The frontend should capture this response and set alwaysApproveToolCall=true in the configurable for future calls
            // We can't set configurable in the Command object
            return new _langgraph.Command({
                goto: 'tools'
            });
        } else {
            // User rejected the tool calls, create rejection tool messages
            const rejectionMessages = aiMessage.tool_calls?.map((toolCall)=>{
                return new _messages.ToolMessage({
                    content: 'Tool call was rejected by the user.',
                    tool_call_id: toolCall.id ?? '',
                    name: toolCall.name
                });
            }) || [];
            // Return to the worker with the rejection messages
            return new _langgraph.Command({
                update: {
                    messages: rejectionMessages
                },
                goto: 'agent'
            });
        }
    };
    const callModel = async (state, config)=>{
        // Get the active worker ID from state
        const activeAgentId = state.activeAgent;
        // Get the worker configuration from the config
        const agentConfig = config?.configurable?.agentConfigMap?.[activeAgentId];
        if (!agentConfig) {
            throw new Error(`No configuration found for agent: ${activeAgentId}`);
        }
        // Create a model instance based on the worker config
        const modelConfig = agentConfig.model;
        const dynamicLLM = await (0, _universal.initChatModel)(`${modelConfig.provider.toLowerCase()}:${modelConfig.name}`, {
            configurableFields: modelConfig.samplingParameters,
            ...modelConfig.parameters
        });
        // todo: modify _callTool
        const dynamicTools = await config?.configurable?.multiMcpClients?.getTools(...agentConfig.mcpConfig.map((mcp)=>mcp.serverName)) || [];
        const toolInstances = [
            ...dynamicTools,
            ...params.defaultTools || []
        ];
        const handoffTool = (0, _helpers.getHandoffTool)(config);
        if (handoffTool) {
            toolInstances.push(handoffTool);
        }
        // Bind tools to the model
        // Check if the model is OpenAI to conditionally set parallel_tool_calls
        const condition = modelConfig.provider !== _enums.ModelProviderEnum.OPENAI || CHAT_MODEL_WITHOUT_PARALLEL_TOOL_CALL_PARAM.has(modelConfig.name);
        const modelWithTools = condition ? dynamicLLM.bindTools(toolInstances) : dynamicLLM.bindTools(toolInstances, {
            parallel_tool_calls: false
        });
        // Create a prompt runnable with the worker's system prompt
        const systemMessage = new _messages.SystemMessage(agentConfig.instruction);
        // Filter messages to only include those from the current worker
        const inputMessages = state.messages;
        const input = [
            systemMessage,
            ...inputMessages
        ];
        const tag = agentConfig.id === config?.configurable?.supervisorAgentId ? _constants.SUPERVISOR_TAG : _constants.WORKER_TAG;
        // Invoke the model
        const response = await modelWithTools.invoke(input, {
            ...config,
            tags: [
                tag
            ]
        });
        // Add worker name to the response
        response.response_metadata.invoker = activeAgentId;
        if (response.lc_kwargs) {
            response.lc_kwargs.name = `${activeAgentId}-${agentConfig.name}`;
        }
        return {
            messages: [
                response
            ]
        };
    };
    const wrappedToolNode = async (state, config)=>{
        const { messages: prevMessages, activeAgent } = state;
        const lastMessage = prevMessages.at(-1);
        if (!lastMessage) {
            throw new Error('No messages found');
        }
        if ((0, _messages.isAIMessage)(lastMessage)) {
            const aiMessage = lastMessage;
            if (aiMessage.tool_calls && aiMessage.tool_calls.length > 0) {
                const erroredToolCalls = aiMessage.tool_calls.filter((toolCall)=>!toolCall.name || toolCall.name.trim() === '' || !toolCall.id || toolCall.id.trim() === '');
                if (erroredToolCalls.length > 0) {
                    throw new Error(`Found ${erroredToolCalls.length} errored tool calls: ${JSON.stringify(erroredToolCalls)}. Tool calls must have both name and id.`);
                }
            }
        }
        const agentConfig = config?.configurable?.agentConfigMap?.[activeAgent];
        if (!agentConfig) {
            throw new Error(`No configuration found for agent: ${activeAgent}`);
        }
        const realAgentConfig = agentConfig;
        // todo: modify _callTool
        const dynamicTools = await config?.configurable?.multiMcpClients?.getTools(...agentConfig.mcpConfig.map((mcp)=>mcp.serverName)) || [];
        const toolInstances = [
            ...dynamicTools,
            ...params.defaultTools || []
        ];
        const handoffTool = (0, _helpers.getHandoffTool)(config);
        if (handoffTool) {
            toolInstances.push(handoffTool);
        }
        const tag = realAgentConfig.id === config?.configurable?.supervisorAgentId ? _constants.SUPERVISOR_TOOL_CALL_TAG : _constants.WORKER_TOOL_CALL_TAG;
        // Create a dynamic tool node
        const dynamicToolNode = new _prebuilt.ToolNode(toolInstances, {
            handleToolErrors: true
        });
        const raw = await dynamicToolNode.invoke(state, {
            ...config,
            tags: [
                tag
            ]
        });
        // Flatten raw into a single output array of either Commands or BaseMessage[]
        const output = [];
        if (Array.isArray(raw)) {
            logger.log('raw array');
            output.push(...raw);
        } else if (raw instanceof _langgraph.Command) {
            logger.log('raw command');
            output.push(raw);
        } else if (Array.isArray(raw.messages)) {
            logger.log('raw messages');
            output.push(...raw.messages);
        } else {
            logger.error('bad raw');
            throw new Error('wrappedToolNode: unexpected return shape');
        }
        const hasCommand = output.some((item)=>item instanceof _langgraph.Command);
        if (hasCommand) {
            return output;
        }
        // Tag every BaseMessage with invoker
        const messages = [];
        for (const item of output){
            // item is either a BaseMessage or an object with `.messages`
            if (item instanceof _messages.BaseMessage) {
                item.response_metadata = {
                    ...item.response_metadata,
                    invoker: state.activeAgent
                };
                messages.push(item);
            }
        }
        return {
            messages
        };
    };
    const workflow = new _langgraph.StateGraph(GraphState).addNode('agent', callModel).addNode('humanReview', humanReviewNode, {
        ends: [
            'tools',
            'agent'
        ]
    }).addNode('tools', wrappedToolNode).addEdge(_langgraph.START, 'agent').addConditionalEdges('agent', shouldContinue, {
        humanReview: 'humanReview',
        tools: 'tools',
        [_langgraph.END]: _langgraph.END
    }).addEdge('tools', 'agent');
    return workflow.compile({
        checkpointer: checkpointSaver,
        store
    });
}

//# sourceMappingURL=react-agent-executor.js.map