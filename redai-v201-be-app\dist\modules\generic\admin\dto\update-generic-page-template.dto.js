"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateGenericPageTemplateDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class UpdateGenericPageTemplateDto {
    name;
    description;
    category;
    thumbnail;
    tags;
    config;
}
exports.UpdateGenericPageTemplateDto = UpdateGenericPageTemplateDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên của mẫu trang',
        example: 'Mẫu form liên hệ mới',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Tên mẫu trang phải là chuỗi' }),
    (0, class_validator_1.MinLength)(3, { message: 'Tên mẫu trang phải có ít nhất 3 ký tự' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Tên mẫu trang không được vượt quá 255 ký tự' }),
    __metadata("design:type", String)
], UpdateGenericPageTemplateDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả về mẫu trang',
        example: 'Mẫu form liên hệ cơ bản với các trường thông tin liên hệ mới',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Mô tả phải là chuỗi' }),
    __metadata("design:type", String)
], UpdateGenericPageTemplateDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh mục của mẫu trang',
        example: 'Form mới',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Danh mục phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Danh mục không được vượt quá 100 ký tự' }),
    __metadata("design:type", String)
], UpdateGenericPageTemplateDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'URL hình thu nhỏ minh họa cho mẫu trang',
        example: '/assets/images/templates/contact-form-new.jpg',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'URL hình thu nhỏ phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(255, { message: 'URL hình thu nhỏ không được vượt quá 255 ký tự' }),
    __metadata("design:type", String)
], UpdateGenericPageTemplateDto.prototype, "thumbnail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách tag cho mẫu trang',
        example: ['form', 'liên hệ', 'cơ bản', 'mới'],
        required: false,
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)({ message: 'Tags phải là mảng' }),
    (0, class_validator_1.ArrayMaxSize)(10, { message: 'Tối đa 10 tag' }),
    (0, class_validator_1.IsString)({ each: true, message: 'Mỗi tag phải là chuỗi' }),
    (0, class_validator_1.MaxLength)(50, { each: true, message: 'Mỗi tag không được vượt quá 50 ký tự' }),
    __metadata("design:type", Array)
], UpdateGenericPageTemplateDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cấu hình mẫu trang dạng JSON',
        example: {
            formId: 'contact-form-template',
            title: 'Liên hệ với chúng tôi',
            subtitle: 'Hãy để lại thông tin, chúng tôi sẽ liên hệ lại với bạn',
            groups: [],
        },
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)({ message: 'Cấu hình mẫu trang phải là đối tượng JSON' }),
    (0, class_transformer_1.Type)(() => Object),
    __metadata("design:type", Object)
], UpdateGenericPageTemplateDto.prototype, "config", void 0);
//# sourceMappingURL=update-generic-page-template.dto.js.map