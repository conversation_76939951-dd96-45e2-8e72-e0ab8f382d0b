{"version": 3, "sources": ["../../../src/agent/helper/api-key-encryption.helper.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport * as crypto from 'crypto';\r\nimport { env } from 'src/config';\r\n\r\n/**\r\n * Helper class để mã hóa và giải mã API key\r\n */\r\n@Injectable()\r\nexport class ApiKeyEncryptionHelper {\r\n  private readonly algorithm = 'aes-256-cbc';\r\n  private readonly adminSecretKey: string;\r\n  private readonly userSecretKey: string;\r\n\r\n  constructor() {\r\n    this.adminSecretKey = env.llmSystemEncryptionKey.ADMIN_SECRECT_MODEL;\r\n    this.userSecretKey = env.llmSystemEncryptionKey.USER_SECRECT_MODEL;\r\n  } \r\n\r\n  /**\r\n   * Mã hóa API key cho admin\r\n   * @param apiKey API key cần mã hóa\r\n   * @returns API key đã được mã hóa\r\n   */\r\n  encryptAdminApiKey(apiKey: string): string {\r\n    if (!this.adminSecretKey) {\r\n      throw new Error('ADMIN_SECRECT_MODEL không được cấu hình');\r\n    }\r\n    \r\n    return this.encrypt(apiKey, this.adminSecretKey);\r\n  }\r\n\r\n  /**\r\n   * Giải mã API key cho admin\r\n   * @param encryptedApiKey API key đã mã hóa\r\n   * @returns API key gốc\r\n   */\r\n  decryptAdminApiKey(encryptedApiKey: string): string {\r\n    if (!this.adminSecretKey) {\r\n      throw new Error('ADMIN_SECRECT_MODEL không được cấu hình');\r\n    }\r\n    \r\n    return this.decrypt(encryptedApiKey, this.adminSecretKey);\r\n  }\r\n\r\n  /**\r\n   * Mã hóa API key cho user\r\n   * @param apiKey API key cần mã hóa\r\n   * @param userId ID của user\r\n   * @returns API key đã được mã hóa\r\n   */\r\n  encryptUserApiKey(apiKey: string, userId: number): string {\r\n    if (!this.userSecretKey) {\r\n      throw new Error('USER_SECRECT_MODEL không được cấu hình');\r\n    }\r\n    \r\n    // Kết hợp secretKey với userId để tạo key mã hóa riêng cho mỗi user\r\n    const userSpecificKey = `${this.userSecretKey}_${userId}`;\r\n    return this.encrypt(apiKey, userSpecificKey);\r\n  }\r\n\r\n  /**\r\n   * Giải mã API key cho user\r\n   * @param encryptedApiKey API key đã mã hóa\r\n   * @param userId ID của user\r\n   * @returns API key gốc\r\n   */\r\n  decryptUserApiKey(encryptedApiKey: string, userId: number): string {\r\n    if (!this.userSecretKey) {\r\n      throw new Error('USER_SECRECT_MODEL không được cấu hình');\r\n    }\r\n    \r\n    // Kết hợp secretKey với userId để tạo key mã hóa riêng cho mỗi user\r\n    const userSpecificKey = `${this.userSecretKey}_${userId}`;\r\n    return this.decrypt(encryptedApiKey, userSpecificKey);\r\n  }\r\n\r\n  /**\r\n   * Hàm mã hóa chung\r\n   * @param text Chuỗi cần mã hóa\r\n   * @param secretKey Khóa bí mật\r\n   * @returns Chuỗi đã mã hóa\r\n   */\r\n  private encrypt(text: string, secretKey: string): string {\r\n    // Tạo key từ secretKey bằng cách hash với SHA-256\r\n    const key = crypto.createHash('sha256').update(secretKey).digest('base64').substring(0, 32);\r\n    \r\n    // Tạo IV (Initialization Vector) ngẫu nhiên\r\n    const iv = crypto.randomBytes(16);\r\n    \r\n    // Tạo cipher\r\n    const cipher = crypto.createCipheriv(this.algorithm, key, iv);\r\n    \r\n    // Mã hóa\r\n    let encrypted = cipher.update(text, 'utf8', 'hex');\r\n    encrypted += cipher.final('hex');\r\n    \r\n    // Kết hợp IV và chuỗi đã mã hóa (IV cần được lưu cùng để giải mã)\r\n    return iv.toString('hex') + ':' + encrypted;\r\n  }\r\n\r\n  /**\r\n   * Hàm giải mã chung\r\n   * @param encryptedText Chuỗi đã mã hóa\r\n   * @param secretKey Khóa bí mật\r\n   * @returns Chuỗi gốc\r\n   */\r\n  private decrypt(encryptedText: string, secretKey: string): string {\r\n    // Tạo key từ secretKey bằng cách hash với SHA-256\r\n    const key = crypto.createHash('sha256').update(secretKey).digest('base64').substring(0, 32);\r\n    \r\n    // Tách IV và chuỗi đã mã hóa\r\n    const textParts = encryptedText.split(':');\r\n    const iv = Buffer.from(textParts[0], 'hex');\r\n    const encryptedData = textParts[1];\r\n    \r\n    // Tạo decipher\r\n    const decipher = crypto.createDecipheriv(this.algorithm, key, iv);\r\n    \r\n    // Giải mã\r\n    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');\r\n    decrypted += decipher.final('utf8');\r\n    \r\n    return decrypted;\r\n  }\r\n}\r\n"], "names": ["ApiKeyEncryptionHelper", "encryptAdminApiKey", "<PERSON><PERSON><PERSON><PERSON>", "admin<PERSON><PERSON><PERSON><PERSON>", "Error", "encrypt", "decryptAdminApiKey", "encryptedApiKey", "decrypt", "encryptUserApiKey", "userId", "userSecretKey", "userSpecificKey", "decryptUserApiKey", "text", "secret<PERSON>ey", "key", "crypto", "createHash", "update", "digest", "substring", "iv", "randomBytes", "cipher", "createCipheriv", "algorithm", "encrypted", "final", "toString", "encryptedText", "textParts", "split", "<PERSON><PERSON><PERSON>", "from", "encryptedData", "decipher", "createDecipheriv", "decrypted", "constructor", "env", "llmSystemEncryptionKey", "ADMIN_SECRECT_MODEL", "USER_SECRECT_MODEL"], "mappings": ";;;;+BAQaA;;;eAAAA;;;wBARc;gEACH;wBACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMb,IAAA,AAAMA,yBAAN,MAAMA;IAUX;;;;GAIC,GACDC,mBAAmBC,MAAc,EAAU;QACzC,IAAI,CAAC,IAAI,CAACC,cAAc,EAAE;YACxB,MAAM,IAAIC,MAAM;QAClB;QAEA,OAAO,IAAI,CAACC,OAAO,CAACH,QAAQ,IAAI,CAACC,cAAc;IACjD;IAEA;;;;GAIC,GACDG,mBAAmBC,eAAuB,EAAU;QAClD,IAAI,CAAC,IAAI,CAACJ,cAAc,EAAE;YACxB,MAAM,IAAIC,MAAM;QAClB;QAEA,OAAO,IAAI,CAACI,OAAO,CAACD,iBAAiB,IAAI,CAACJ,cAAc;IAC1D;IAEA;;;;;GAKC,GACDM,kBAAkBP,MAAc,EAAEQ,MAAc,EAAU;QACxD,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE;YACvB,MAAM,IAAIP,MAAM;QAClB;QAEA,oEAAoE;QACpE,MAAMQ,kBAAkB,GAAG,IAAI,CAACD,aAAa,CAAC,CAAC,EAAED,QAAQ;QACzD,OAAO,IAAI,CAACL,OAAO,CAACH,QAAQU;IAC9B;IAEA;;;;;GAKC,GACDC,kBAAkBN,eAAuB,EAAEG,MAAc,EAAU;QACjE,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE;YACvB,MAAM,IAAIP,MAAM;QAClB;QAEA,oEAAoE;QACpE,MAAMQ,kBAAkB,GAAG,IAAI,CAACD,aAAa,CAAC,CAAC,EAAED,QAAQ;QACzD,OAAO,IAAI,CAACF,OAAO,CAACD,iBAAiBK;IACvC;IAEA;;;;;GAKC,GACD,AAAQP,QAAQS,IAAY,EAAEC,SAAiB,EAAU;QACvD,kDAAkD;QAClD,MAAMC,MAAMC,QAAOC,UAAU,CAAC,UAAUC,MAAM,CAACJ,WAAWK,MAAM,CAAC,UAAUC,SAAS,CAAC,GAAG;QAExF,4CAA4C;QAC5C,MAAMC,KAAKL,QAAOM,WAAW,CAAC;QAE9B,aAAa;QACb,MAAMC,SAASP,QAAOQ,cAAc,CAAC,IAAI,CAACC,SAAS,EAAEV,KAAKM;QAE1D,SAAS;QACT,IAAIK,YAAYH,OAAOL,MAAM,CAACL,MAAM,QAAQ;QAC5Ca,aAAaH,OAAOI,KAAK,CAAC;QAE1B,kEAAkE;QAClE,OAAON,GAAGO,QAAQ,CAAC,SAAS,MAAMF;IACpC;IAEA;;;;;GAKC,GACD,AAAQnB,QAAQsB,aAAqB,EAAEf,SAAiB,EAAU;QAChE,kDAAkD;QAClD,MAAMC,MAAMC,QAAOC,UAAU,CAAC,UAAUC,MAAM,CAACJ,WAAWK,MAAM,CAAC,UAAUC,SAAS,CAAC,GAAG;QAExF,6BAA6B;QAC7B,MAAMU,YAAYD,cAAcE,KAAK,CAAC;QACtC,MAAMV,KAAKW,OAAOC,IAAI,CAACH,SAAS,CAAC,EAAE,EAAE;QACrC,MAAMI,gBAAgBJ,SAAS,CAAC,EAAE;QAElC,eAAe;QACf,MAAMK,WAAWnB,QAAOoB,gBAAgB,CAAC,IAAI,CAACX,SAAS,EAAEV,KAAKM;QAE9D,UAAU;QACV,IAAIgB,YAAYF,SAASjB,MAAM,CAACgB,eAAe,OAAO;QACtDG,aAAaF,SAASR,KAAK,CAAC;QAE5B,OAAOU;IACT;IA9GAC,aAAc;aAJGb,YAAY;QAK3B,IAAI,CAACvB,cAAc,GAAGqC,WAAG,CAACC,sBAAsB,CAACC,mBAAmB;QACpE,IAAI,CAAC/B,aAAa,GAAG6B,WAAG,CAACC,sBAAsB,CAACE,kBAAkB;IACpE;AA4GF"}