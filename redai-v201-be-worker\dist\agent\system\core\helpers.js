"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get dropLeadingOrphanAsRemovals () {
        return dropLeadingOrphanAsRemovals;
    },
    get dropTrailingOrphanBlock () {
        return dropTrailingOrphanBlock;
    },
    get getHandoffTool () {
        return getHandoffTool;
    }
});
const _messages = require("@langchain/core/messages");
const _tool = require("@langchain/core/messages/tool");
const _handofftool = require("./handoff-tool");
const _common = require("@nestjs/common");
const logger = new _common.Logger('TrimmerHelper');
function dropLeadingOrphanAsRemovals(messages) {
    if (messages.length === 0) return [];
    const [first, ...rest] = messages;
    // Case A: orphaned AIMessage with N tool_calls
    if (first instanceof _messages.AIMessage && first.tool_calls?.length) {
        const expectedIds = first.tool_calls.map((tc)=>tc.id);
        // grab the next N messages
        const nextN = rest.slice(0, expectedIds.length);
        // are they an exact match, in order?
        const allMatch = nextN.length === expectedIds.length && nextN.every((msg, i)=>msg instanceof _tool.ToolMessage && msg.tool_call_id === expectedIds[i]);
        if (!allMatch) {
            // drop the AI + whatever tools DID match
            const toDelete = [
                first,
                ...nextN.filter((msg)=>msg instanceof _tool.ToolMessage && expectedIds.includes(msg.tool_call_id))
            ];
            return toDelete.filter((m)=>!!m.id).map((m)=>new _messages.RemoveMessage({
                    id: m.id
                }));
        }
        return [];
    }
    // Case B: stray ToolMessages at front
    if (first instanceof _tool.ToolMessage) {
        // collect all leading ToolMessages
        const orphans = messages.filter((m)=>m instanceof _tool.ToolMessage);
        return orphans.filter((m)=>!!m.id).map((m)=>new _messages.RemoveMessage({
                id: m.id
            }));
    }
    // nothing to drop
    return [];
}
function dropTrailingOrphanBlock(msgs) {
    let end = msgs.length;
    // 1) strip trailing ToolMessage(s)
    while(end > 0 && msgs[end - 1] instanceof _tool.ToolMessage){
        end--;
    }
    // 2) if we now end on an AIMessage-with-tool_calls, drop it too
    const last = msgs[end - 1];
    if (last instanceof msgs[0].constructor && Array.isArray(last.tool_calls) && last.tool_calls.length > 0) {
        end--;
    }
    return msgs.slice(0, end);
}
function getHandoffTool(config) {
    if (Object.keys(config?.configurable?.agentConfigMap || {})?.length) {
        return (0, _handofftool.createHandoffTool)(Object.keys(config?.configurable?.agentConfigMap || {}));
    } else {
        return null;
    }
}

//# sourceMappingURL=helpers.js.map