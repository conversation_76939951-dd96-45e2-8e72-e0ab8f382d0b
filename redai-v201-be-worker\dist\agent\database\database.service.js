"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AgentDatabaseService", {
    enumerable: true,
    get: function() {
        return AgentDatabaseService;
    }
});
const _common = require("@nestjs/common");
const _typeorm = require("typeorm");
const _typeorm1 = require("@nestjs/typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let AgentDatabaseService = class AgentDatabaseService {
    async onModuleInit() {
        // Ensure database connection is established
        if (!this.dataSource.isInitialized) {
            await this.dataSource.initialize();
        }
    }
    async onModuleDestroy() {
        // Clean up database connection
        if (this.dataSource.isInitialized) {
            await this.dataSource.destroy();
        }
    }
    /**
   * Get the TypeORM DataSource instance for raw SQL operations
   * @returns DataSource instance
   */ getDataSource() {
        return this.dataSource;
    }
    /**
   * Execute a raw SQL query
   * @param query SQL query string
   * @param parameters Query parameters
   * @returns Promise<any[]> Query results
   */ async query(query, parameters) {
        try {
            return await this.dataSource.query(query, parameters);
        } catch (error) {
            throw new Error(`Database query failed: ${error.message}`);
        }
    }
    /**
   * Execute a raw SQL query and return a single result
   * @param query SQL query string
   * @param parameters Query parameters
   * @returns Promise<any | null> Single result or null
   */ async queryOne(query, parameters) {
        const results = await this.query(query, parameters);
        return results.length > 0 ? results[0] : null;
    }
    constructor(dataSource){
        this.dataSource = dataSource;
    }
};
AgentDatabaseService = _ts_decorate([
    (0, _common.Injectable)(),
    _ts_param(0, (0, _typeorm1.InjectDataSource)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _typeorm.DataSource === "undefined" ? Object : _typeorm.DataSource
    ])
], AgentDatabaseService);

//# sourceMappingURL=database.service.js.map