"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MockUserRepository = exports.MockAgentMediaRepository = exports.MockS3Service = exports.MockMediaRepository = void 0;
const MockMediaRepository = () => ({
    findOne: jest.fn(),
    find: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
});
exports.MockMediaRepository = MockMediaRepository;
const MockS3Service = () => ({
    createPresignedWithID: jest.fn(),
    getDownloadUrl: jest.fn(),
    createPresignedDownloadUrl: jest.fn(),
});
exports.MockS3Service = MockS3Service;
const MockAgentMediaRepository = () => ({
    delete: jest.fn(),
});
exports.MockAgentMediaRepository = MockAgentMediaRepository;
const MockUserRepository = () => ({
    createQueryBuilder: jest.fn(),
});
exports.MockUserRepository = MockUserRepository;
//# sourceMappingURL=mock.services.js.map