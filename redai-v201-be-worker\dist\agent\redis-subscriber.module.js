"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "RedisSubscriberModule", {
    enumerable: true,
    get: function() {
        return RedisSubscriberModule;
    }
});
const _common = require("@nestjs/common");
const _microservices = require("@nestjs/microservices");
const _config = require("../config");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
let RedisSubscriberModule = class RedisSubscriberModule {
};
RedisSubscriberModule = _ts_decorate([
    (0, _common.Module)({
        imports: [
            _microservices.ClientsModule.register([
                {
                    name: 'REDIS_SUBSCRIBER',
                    transport: _microservices.Transport.REDIS,
                    options: {
                        // Parse Redis URL to extract connection details
                        ...parseRedisUrl(_config.env.external.REDIS_URL),
                        retryDelay: 1000,
                        maxRetriesPerRequest: 3
                    }
                }
            ])
        ],
        exports: [
            _microservices.ClientsModule
        ]
    })
], RedisSubscriberModule);
/**
 * Parse Redis URL to extract connection options
 * @param redisUrl Redis URL string
 * @returns Redis connection options
 */ function parseRedisUrl(redisUrl) {
    try {
        const url = new URL(redisUrl);
        return {
            host: url.hostname,
            port: parseInt(url.port) || 6379,
            password: url.password || undefined,
            db: parseInt(url.pathname.slice(1)) || 0
        };
    } catch (error) {
        // Fallback to default values if URL parsing fails
        return {
            host: 'localhost',
            port: 6379,
            db: 0
        };
    }
}

//# sourceMappingURL=redis-subscriber.module.js.map