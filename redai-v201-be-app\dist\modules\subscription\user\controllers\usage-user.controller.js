"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsageUserController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const usage_user_service_1 = require("../services/usage-user.service");
const jwt_user_guard_1 = require("../../../auth/guards/jwt-user.guard");
let UsageUserController = class UsageUserController {
    usageUserService;
    constructor(usageUserService) {
        this.usageUserService = usageUserService;
    }
};
exports.UsageUserController = UsageUserController;
exports.UsageUserController = UsageUserController = __decorate([
    (0, swagger_1.ApiTags)('Subscription - User Usage'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_user_guard_1.JwtUserGuard),
    (0, common_1.Controller)('user/subscription/usage'),
    __metadata("design:paramtypes", [usage_user_service_1.UsageUserService])
], UsageUserController);
//# sourceMappingURL=usage-user.controller.js.map