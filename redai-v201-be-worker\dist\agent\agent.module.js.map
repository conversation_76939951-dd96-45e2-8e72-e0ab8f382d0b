{"version": 3, "sources": ["../../src/agent/agent.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { createMcpUserClient } from './mcp/test-mcp-client';\r\nimport { StreamController } from './worker/stream.controller';\r\nimport { RedisEventController } from './controllers/redis-event.controller';\r\nimport { AgentDatabaseService, UserAgentRunsQueries, UserMessagesQueries } from './database';\r\nimport { RedisSubscriberModule } from './redis-subscriber.module';\r\nimport { ApiKeyEncryptionHelper } from './helper/api-key-encryption.helper';\r\n\r\n/**\r\n * Module for worker-related functionality\r\n */\r\n@Module({\r\n  imports: [RedisSubscriberModule],\r\n  controllers: [StreamController, RedisEventController],\r\n  providers: [\r\n    {\r\n      provide: 'McpClient',\r\n      useFactory: async () => {\r\n        await createMcpUserClient();\r\n      },\r\n    },\r\n    AgentDatabaseService,\r\n    UserAgentRunsQueries,\r\n    UserMessagesQueries,\r\n    ApiKeyEncryptionHelper,\r\n  ],\r\n  exports: [\r\n    AgentDatabaseService,\r\n    UserAgentRunsQueries,\r\n    UserMessagesQueries,\r\n  ],\r\n})\r\nexport class AgentModule {}\r\n"], "names": ["AgentModule", "imports", "RedisSubscriberModule", "controllers", "StreamController", "RedisEventController", "providers", "provide", "useFactory", "createMcpUserClient", "AgentDatabaseService", "UserAgentRunsQueries", "UserMessagesQueries", "ApiKeyEncryptionHelper", "exports"], "mappings": ";;;;+BAgCaA;;;eAAAA;;;wBAhCU;+BACa;kCACH;sCACI;0BAC2C;uCAC1C;wCACC;;;;;;;AA0BhC,IAAA,AAAMA,cAAN,MAAMA;AAAa;;;QApBxBC,SAAS;YAACC,4CAAqB;SAAC;QAChCC,aAAa;YAACC,kCAAgB;YAAEC,0CAAoB;SAAC;QACrDC,WAAW;YACT;gBACEC,SAAS;gBACTC,YAAY;oBACV,MAAMC,IAAAA,kCAAmB;gBAC3B;YACF;YACAC,8BAAoB;YACpBC,8BAAoB;YACpBC,6BAAmB;YACnBC,8CAAsB;SACvB;QACDC,SAAS;YACPJ,8BAAoB;YACpBC,8BAAoB;YACpBC,6BAAmB;SACpB"}