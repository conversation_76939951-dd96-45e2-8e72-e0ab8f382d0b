"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentToolsController = void 0;
const response_1 = require("../../../common/response");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const decorators_1 = require("../decorators");
const guards_1 = require("../guards");
const services_1 = require("../services");
let AgentToolsController = class AgentToolsController {
    agentToolsService;
    constructor(agentToolsService) {
        this.agentToolsService = agentToolsService;
    }
    async getAgentTools(agentId) {
        console.log('Agent ID: ', agentId);
        const result = await this.agentToolsService.getAgentTools(agentId);
        return response_1.ApiResponseDto.success(result, 'Lấy danh sách tool của agent thành công');
    }
};
exports.AgentToolsController = AgentToolsController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách tool của agent' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lấy danh sách tool thành công',
        type: response_1.ApiResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Không tìm thấy agent',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Không có quyền truy cập',
    }),
    __param(0, (0, decorators_1.CurrentAgent)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AgentToolsController.prototype, "getAgentTools", null);
exports.AgentToolsController = AgentToolsController = __decorate([
    (0, swagger_1.ApiTags)('Agent Tools'),
    (0, common_1.Controller)('agent-tools'),
    (0, common_1.UseGuards)(guards_1.ApiKeyAuthGuard),
    (0, decorators_1.ApiKeyAuth)(),
    (0, swagger_1.ApiSecurity)('api-key'),
    __metadata("design:paramtypes", [services_1.AgentToolsService])
], AgentToolsController);
//# sourceMappingURL=agent-tools.controller.js.map