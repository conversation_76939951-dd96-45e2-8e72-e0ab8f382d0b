"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "toolRegistry", {
    enumerable: true,
    get: function() {
        return toolRegistry;
    }
});
const _mathtools = require("./math-tools");
/**
 * Registry for all available tools
 */ let ToolRegistry = class ToolRegistry {
    /**
   * Register a tool with the registry
   * @param id Tool identifier
   * @param tool Tool instance
   */ registerTool(id, tool) {
        this.tools.set(id, tool);
    }
    /**
   * Get a tool by its ID
   * @param id Tool identifier
   * @returns The tool instance
   */ getTool(id) {
        const tool = this.tools.get(id);
        if (!tool) {
            throw new Error(`Tool not found: ${id}`);
        }
        return tool;
    }
    /**
   * Check if a tool exists in the registry
   * @param id Tool identifier
   * @returns True if the tool exists
   */ hasTool(id) {
        return this.tools.has(id);
    }
    /**
   * Get all registered tools
   * @returns Array of all tool instances
   */ getAllTools() {
        return Array.from(this.tools.values());
    }
    /**
   * Create tool instances from tool IDs
   * @param toolIds Array of tool IDs
   * @returns Array of tool instances
   */ createToolsFromConfig(toolIds) {
        return toolIds.filter((id)=>this.hasTool(id)).map((id)=>this.getTool(id));
    }
    constructor(){
        this.tools = new Map();
        // Register math tools
        Object.entries(_mathtools.mathTools).forEach(([id, tool])=>{
            this.registerTool(id, tool);
        });
    }
};
const toolRegistry = new ToolRegistry();

//# sourceMappingURL=tools-registry.js.map