"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserKeyLlmController = void 0;
const swagger_1 = require("../../../../common/swagger");
const decorators_1 = require("../../../auth/decorators");
const guards_1 = require("../../../auth/guards");
const response_1 = require("../../../../common/response");
const common_1 = require("@nestjs/common");
const swagger_2 = require("@nestjs/swagger");
const user_key_llm_1 = require("../dto/user-key-llm");
const services_1 = require("../services");
let UserKeyLlmController = class UserKeyLlmController {
    userKeyLlmService;
    constructor(userKeyLlmService) {
        this.userKeyLlmService = userKeyLlmService;
    }
    create(createDto, userId) {
        return this.userKeyLlmService.create(userId, createDto);
    }
    findAll(userId, queryDto) {
        return this.userKeyLlmService.findAll(userId, queryDto);
    }
    update(id, updateDto, userId) {
        return this.userKeyLlmService.update(userId, id, updateDto);
    }
    reloadModels(keyId, userId) {
        return this.userKeyLlmService.reloadModels(userId, keyId);
    }
    remove(id, userId) {
        return this.userKeyLlmService.remove(userId, id);
    }
};
exports.UserKeyLlmController = UserKeyLlmController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_2.ApiOperation)({
        summary: 'Tạo mới user key LLM với model auto-discovery',
        description: 'Tự động discovery models từ provider sau khi tạo key thành công.'
    }),
    (0, swagger_2.ApiResponse)({
        status: 201,
        description: 'Tạo mới user key LLM thành công với thông tin model discovery',
        type: user_key_llm_1.CreateUserKeyLlmResponseDto
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, decorators_1.CurrentUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_key_llm_1.CreateUserKeyLlmDto, Number]),
    __metadata("design:returntype", Promise)
], UserKeyLlmController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_2.ApiOperation)({
        summary: 'Lấy danh sách user key LLM có phân trang',
        description: 'API này hỗ trợ tìm kiếm theo tên key, phân trang và sắp xếp'
    }),
    (0, swagger_2.ApiResponse)({
        status: 200,
        description: 'Danh sách user key LLM',
        type: response_1.ApiResponseDto
    }),
    __param(0, (0, decorators_1.CurrentUser)('id')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, user_key_llm_1.UserKeyLlmQueryDto]),
    __metadata("design:returntype", void 0)
], UserKeyLlmController.prototype, "findAll", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_2.ApiOperation)({
        summary: 'Cập nhật user key LLM với model auto-discovery',
        description: 'Nếu cập nhật API key, sẽ tự động discovery models mới từ provider.'
    }),
    (0, swagger_2.ApiResponse)({
        status: 200,
        description: 'Cập nhật user key LLM thành công với thông tin model discovery',
        type: user_key_llm_1.UpdateUserKeyLlmResponseDto
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, decorators_1.CurrentUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, user_key_llm_1.UpdateUserKeyLlmDto, Number]),
    __metadata("design:returntype", Promise)
], UserKeyLlmController.prototype, "update", null);
__decorate([
    (0, common_1.Post)(':id/reload-models'),
    (0, swagger_2.ApiOperation)({
        summary: 'Reload models từ user key LLM',
        description: 'Thực hiện lại discovery models từ provider cho key cụ thể.'
    }),
    (0, swagger_2.ApiResponse)({
        status: 200,
        description: 'Reload models thành công',
        type: user_key_llm_1.ReloadModelsResponseDto
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, decorators_1.CurrentUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], UserKeyLlmController.prototype, "reloadModels", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_2.ApiOperation)({ summary: 'Xóa user key LLM và clear model mappings' }),
    (0, swagger_2.ApiResponse)({
        status: 200,
        description: 'Xóa user key LLM thành công',
        type: response_1.ApiResponseDto
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, decorators_1.CurrentUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", void 0)
], UserKeyLlmController.prototype, "remove", null);
exports.UserKeyLlmController = UserKeyLlmController = __decorate([
    (0, swagger_2.ApiTags)(swagger_1.SWAGGER_API_TAGS.USER_API_KEY_MODEL),
    (0, common_1.Controller)('key-llm'),
    (0, common_1.UseGuards)(guards_1.JwtUserGuard),
    (0, swagger_2.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [services_1.UserKeyLlmService])
], UserKeyLlmController);
//# sourceMappingURL=user-key-llm.controller.js.map