"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mockOrderAdminService = exports.mockProductAdminService = exports.mockCartAdminService = void 0;
const cart_mock_1 = require("./cart.mock");
const product_mock_1 = require("./product.mock");
const order_mock_1 = require("./order.mock");
exports.mockCartAdminService = {
    getCarts: jest.fn().mockImplementation((_employeeId, _queryDto) => {
        return Promise.resolve(cart_mock_1.mockPaginatedCartResponseDto);
    }),
    getCartById: jest.fn().mockImplementation((_employeeId, cartId) => {
        if (cartId === 1) {
            return Promise.resolve(cart_mock_1.mockCartResponseDto);
        }
        throw new Error('Cart not found');
    }),
};
exports.mockProductAdminService = {
    getProducts: jest.fn().mockImplementation((_employeeId, _queryDto) => {
        return Promise.resolve(product_mock_1.mockPaginatedProductResponseDto);
    }),
    getProductById: jest.fn().mockImplementation((_employeeId, productId) => {
        if (productId === 1) {
            return Promise.resolve(product_mock_1.mockProductDetailResponseDto);
        }
        throw new Error('Product not found');
    }),
    updateMultipleProductsStatus: jest.fn().mockImplementation((_employeeId, _updateDto) => {
        return Promise.resolve({
            successIds: [1, 2],
            failedIds: [
                {
                    id: 3,
                    reason: 'Sản phẩm không tồn tại'
                }
            ]
        });
    }),
};
exports.mockOrderAdminService = {
    getOrders: jest.fn().mockImplementation((_employeeId, _queryDto) => {
        return Promise.resolve(order_mock_1.mockPaginatedOrderResponseDto);
    }),
    getOrderById: jest.fn().mockImplementation((_employeeId, orderId) => {
        if (orderId === 1) {
            return Promise.resolve(order_mock_1.mockOrderResponseDto);
        }
        throw new Error('Order not found');
    }),
};
//# sourceMappingURL=service.mock.js.map