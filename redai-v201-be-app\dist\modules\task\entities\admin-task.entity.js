"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminTask = void 0;
const typeorm_1 = require("typeorm");
let AdminTask = class AdminTask {
    taskId;
    agentId;
    taskName;
    taskDescription;
    active;
    createdBy;
    updatedBy;
    deletedBy;
    createdAt;
    updatedAt;
    deletedAt;
};
exports.AdminTask = AdminTask;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid', { name: 'task_id' }),
    __metadata("design:type", String)
], AdminTask.prototype, "taskId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'agent_id', type: 'uuid', nullable: false }),
    __metadata("design:type", String)
], AdminTask.prototype, "agentId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'task_name', type: 'varchar', length: 255, nullable: false }),
    __metadata("design:type", String)
], AdminTask.prototype, "taskName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'task_description', type: 'text', nullable: true }),
    __metadata("design:type", String)
], AdminTask.prototype, "taskDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'active', type: 'boolean', default: true }),
    __metadata("design:type", Boolean)
], AdminTask.prototype, "active", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'int', nullable: true }),
    __metadata("design:type", Number)
], AdminTask.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'int', nullable: true }),
    __metadata("design:type", Number)
], AdminTask.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'deleted_by', type: 'int', nullable: true }),
    __metadata("design:type", Number)
], AdminTask.prototype, "deletedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'created_at',
        type: 'bigint',
        default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    }),
    __metadata("design:type", Number)
], AdminTask.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'updated_at',
        type: 'bigint',
        default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    }),
    __metadata("design:type", Number)
], AdminTask.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'deleted_at', type: 'bigint', nullable: true }),
    __metadata("design:type", Number)
], AdminTask.prototype, "deletedAt", void 0);
exports.AdminTask = AdminTask = __decorate([
    (0, typeorm_1.Entity)('admin_tasks')
], AdminTask);
//# sourceMappingURL=admin-task.entity.js.map