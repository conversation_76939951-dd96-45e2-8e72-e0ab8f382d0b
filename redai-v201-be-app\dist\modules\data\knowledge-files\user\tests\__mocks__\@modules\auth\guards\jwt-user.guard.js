"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtUserGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const jwt_util_1 = require("../services/jwt.util");
const redis_service_1 = require("../../../@shared/services/redis.service");
let JwtUserGuard = class JwtUserGuard {
    reflector;
    jwtUtilService;
    redisService;
    constructor(reflector, jwtUtilService, redisService) {
        this.reflector = reflector;
        this.jwtUtilService = jwtUtilService;
        this.redisService = redisService;
    }
    canActivate(context) {
        return true;
    }
};
exports.JwtUserGuard = JwtUserGuard;
exports.JwtUserGuard = JwtUserGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector,
        jwt_util_1.JwtUtilService,
        redis_service_1.RedisService])
], JwtUserGuard);
//# sourceMappingURL=jwt-user.guard.js.map