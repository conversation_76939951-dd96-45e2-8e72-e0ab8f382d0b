"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RuleContractAdminService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RuleContractAdminService = void 0;
const common_1 = require("@nestjs/common");
const exceptions_1 = require("../../../../common/exceptions");
const repositories_1 = require("../../repositories");
const user_repository_1 = require("../../../user/repositories/user.repository");
const dto_1 = require("../dto");
const errors_1 = require("../../errors");
const typeorm_transactional_1 = require("typeorm-transactional");
const cdn_service_1 = require("../../../../shared/services/cdn.service");
const time_interval_util_1 = require("../../../../shared/utils/time/time-interval.util");
const rule_contract_state_service_1 = require("../../state-machine/rule-contract-state.service");
let RuleContractAdminService = RuleContractAdminService_1 = class RuleContractAdminService {
    ruleContractRepository;
    userRepository;
    cdnService;
    ruleContractStateService;
    logger = new common_1.Logger(RuleContractAdminService_1.name);
    constructor(ruleContractRepository, userRepository, cdnService, ruleContractStateService) {
        this.ruleContractRepository = ruleContractRepository;
        this.userRepository = userRepository;
        this.cdnService = cdnService;
        this.ruleContractStateService = ruleContractStateService;
    }
    async getContracts(queryDto) {
        try {
            const { items, meta } = await this.ruleContractRepository.findWithPaginationForAdmin(queryDto);
            const userIds = items.map((item) => item.userId);
            const uniqueUserIds = [...new Set(userIds)].filter(id => id !== null && id !== undefined);
            const users = uniqueUserIds.length > 0 ? await this.userRepository.findByIds(uniqueUserIds) : [];
            const contractDtos = await Promise.all(items.map(async (contract) => {
                const user = users.find((u) => u.id === contract.userId);
                let contractUrl = '';
                if (contract.contractUrlPdf) {
                    const generatedUrl = this.cdnService.generateUrlView(contract.contractUrlPdf, time_interval_util_1.TimeIntervalEnum.ONE_HOUR);
                    if (generatedUrl) {
                        contractUrl = generatedUrl;
                    }
                }
                return {
                    id: contract.id,
                    userId: contract.userId,
                    userName: user?.fullName || 'Unknown',
                    userEmail: user?.email || 'Unknown',
                    contractCode: `HD-${contract.id}`,
                    status: contract.status,
                    type: contract.type,
                    contractUrl,
                    createdAt: contract.createdAt,
                    userSignatureAt: contract.userSignatureAt,
                    adminSignatureAt: contract.adminSignatureAt,
                };
            }));
            return {
                items: contractDtos,
                meta,
            };
        }
        catch (error) {
            this.logger.error(`Error getting contracts: ${error.message}`, error.stack);
            if (error instanceof exceptions_1.AppException) {
                throw error;
            }
            throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.CONTRACT_RETRIEVAL_FAILED, 'Lỗi khi lấy danh sách hợp đồng nguyên tắc');
        }
    }
    async getContractById(id) {
        try {
            const contract = await this.ruleContractRepository.findById(id);
            if (!contract) {
                throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND, `Không tìm thấy hợp đồng nguyên tắc với ID ${id}`);
            }
            const user = contract.userId ? await this.userRepository.findById(contract.userId) : null;
            let contractUrl = '';
            if (contract.contractUrlPdf) {
                const generatedUrl = this.cdnService.generateUrlView(contract.contractUrlPdf, time_interval_util_1.TimeIntervalEnum.ONE_HOUR);
                if (generatedUrl) {
                    contractUrl = generatedUrl;
                }
            }
            return {
                id: contract.id,
                userId: contract.userId,
                userName: user?.fullName || 'Unknown',
                userEmail: user?.email || 'Unknown',
                contractCode: `HD-${contract.id}`,
                status: contract.status,
                type: contract.type,
                contractUrl,
                createdAt: contract.createdAt,
                userSignatureAt: contract.userSignatureAt,
                adminSignatureAt: contract.adminSignatureAt,
            };
        }
        catch (error) {
            this.logger.error(`Error getting contract: ${error.message}`, error.stack);
            if (error instanceof exceptions_1.AppException) {
                throw error;
            }
            throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.CONTRACT_RETRIEVAL_FAILED, 'Lỗi khi lấy thông tin chi tiết hợp đồng nguyên tắc');
        }
    }
};
exports.RuleContractAdminService = RuleContractAdminService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.RuleContractQueryDto]),
    __metadata("design:returntype", Promise)
], RuleContractAdminService.prototype, "getContracts", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RuleContractAdminService.prototype, "getContractById", null);
exports.RuleContractAdminService = RuleContractAdminService = RuleContractAdminService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.RuleContractRepository,
        user_repository_1.UserRepository,
        cdn_service_1.CdnService,
        rule_contract_state_service_1.RuleContractStateService])
], RuleContractAdminService);
//# sourceMappingURL=rule-contract-admin.service.js.map