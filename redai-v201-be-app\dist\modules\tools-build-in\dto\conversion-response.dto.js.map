{"version": 3, "file": "conversion-response.dto.js", "sourceRoot": "", "sources": ["../../../../src/modules/tools-build-in/dto/conversion-response.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAK9C,MAAa,6BAA6B;IAMxC,EAAE,CAAU;IAMZ,IAAI,CAAS;IAWb,KAAK,CAAuB;IAO5B,KAAK,CAAU;IAOf,MAAM,CAAU;IAOhB,QAAQ,CAAU;IAWlB,QAAQ,CAAuB;CAChC;AAxDD,sEAwDC;AAlDC;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,mBAAmB;QAChC,OAAO,EAAE,CAAC;QACV,QAAQ,EAAE,IAAI;KACf,CAAC;;yDACU;AAMZ;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,gBAAgB;QAC7B,OAAO,EAAE,cAAc;KACxB,CAAC;;2DACW;AAWb;IATC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,kBAAkB;QAC/B,OAAO,EAAE;YACP,OAAO,EAAE,mBAAmB;YAC5B,QAAQ,EAAE,IAAI;YACd,UAAU,EAAE,CAAC,oBAAoB,CAAC;SACnC;QACD,QAAQ,EAAE,IAAI;KACf,CAAC;;4DAC0B;AAO5B;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,0BAA0B;QACvC,OAAO,EAAE,YAAY;QACrB,QAAQ,EAAE,IAAI;KACf,CAAC;;4DACa;AAOf;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,cAAc;QAC3B,OAAO,EAAE,gCAAgC;QACzC,QAAQ,EAAE,IAAI;KACf,CAAC;;6DACc;AAOhB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,gBAAgB;QAC7B,OAAO,EAAE,UAAU;QACnB,QAAQ,EAAE,IAAI;KACf,CAAC;;+DACgB;AAWlB;IATC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,oBAAoB;QACjC,OAAO,EAAE;YACP,GAAG,EAAE,EAAE;YACP,SAAS,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;YAClC,eAAe,EAAE,aAAa;SAC/B;QACD,QAAQ,EAAE,IAAI;KACf,CAAC;;+DAC6B;AAMjC,MAAa,qBAAqB;IAMhC,EAAE,CAAU;IAOZ,iBAAiB,CAAU;IAO3B,QAAQ,CAAiC;IAOzC,cAAc,CAAU;IAOxB,MAAM,CAAU;IAOhB,KAAK,CAAU;IAaf,OAAO,CAAuB;IAO9B,SAAS,CAAU;CACpB;AA9DD,sDA8DC;AAxDC;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,2BAA2B;QACxC,OAAO,EAAE,CAAC;QACV,QAAQ,EAAE,IAAI;KACf,CAAC;;iDACU;AAOZ;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,+BAA+B;QAC5C,OAAO,EAAE,CAAC;QACV,QAAQ,EAAE,IAAI;KACf,CAAC;;gEACyB;AAO3B;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,sCAAsC;QACnD,IAAI,EAAE,6BAA6B;QACnC,QAAQ,EAAE,IAAI;KACf,CAAC;8BACS,6BAA6B;uDAAC;AAOzC;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,iBAAiB;QAC9B,OAAO,EAAE,QAAQ;QACjB,QAAQ,EAAE,IAAI;KACf,CAAC;;6DACsB;AAOxB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,sBAAsB;QACnC,OAAO,EAAE,UAAU;QACnB,QAAQ,EAAE,IAAI;KACf,CAAC;;qDACc;AAOhB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,SAAS;QACtB,OAAO,EAAE,oCAAoC;QAC7C,QAAQ,EAAE,IAAI;KACf,CAAC;;oDACa;AAaf;IAXC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,kBAAkB;QAC/B,OAAO,EAAE;YACP,kBAAkB,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAC7B,mBAAmB,EAAE;gBACnB,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,UAAU,EAAE;gBAC5C,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,8BAA8B,EAAE;aACjE;SACF;QACD,QAAQ,EAAE,IAAI;KACf,CAAC;;sDAC4B;AAO9B;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,eAAe;QAC5B,OAAO,EAAE,aAAa;QACtB,QAAQ,EAAE,IAAI;KACf,CAAC;;wDACiB;AAMrB,MAAa,2BAA2B;IAKtC,WAAW,CAA0B;IAMrC,KAAK,CAAS;IAWd,OAAO,CAAuB;CAC/B;AAvBD,kEAuBC;AAlBC;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,+BAA+B;QAC5C,IAAI,EAAE,CAAC,qBAAqB,CAAC;KAC9B,CAAC;;gEACmC;AAMrC;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,iBAAiB;QAC9B,OAAO,EAAE,EAAE;KACZ,CAAC;;0DACY;AAWd;IATC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,mBAAmB;QAChC,OAAO,EAAE;YACP,cAAc,EAAE,QAAQ;YACxB,MAAM,EAAE,UAAU;YAClB,SAAS,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,EAAE,aAAa,EAAE;SACxD;QACD,QAAQ,EAAE,IAAI;KACf,CAAC;;4DAC4B"}