"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserModelFineTuneResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class UserModelFineTuneResponseDto {
    id;
    modelId;
    provider;
    inputModalities;
    outputModalities;
    samplingParameters;
    features;
    basePricing;
    fineTunePricing;
    trainingPricing;
}
exports.UserModelFineTuneResponseDto = UserModelFineTuneResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của user model fine-tune',
        example: '123e4567-e89b-12d3-a456-426614174000'
    }),
    __metadata("design:type", String)
], UserModelFineTuneResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID định danh của model đã fine-tune',
        example: 'ft:gpt-3.5-turbo-1106:personal::8N8VjQJl'
    }),
    __metadata("design:type", String)
], UserModelFineTuneResponseDto.prototype, "modelId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Nhà cung cấp model',
        example: 'OPENAI'
    }),
    __metadata("design:type", String)
], UserModelFineTuneResponseDto.prototype, "provider", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Các phương thức input được hỗ trợ',
        example: ['text'],
        type: [String]
    }),
    __metadata("design:type", Array)
], UserModelFineTuneResponseDto.prototype, "inputModalities", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Các phương thức output được hỗ trợ',
        example: ['text'],
        type: [String]
    }),
    __metadata("design:type", Array)
], UserModelFineTuneResponseDto.prototype, "outputModalities", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Các tham số sampling được hỗ trợ',
        example: ['temperature', 'top_p', 'max_tokens'],
        type: [String]
    }),
    __metadata("design:type", Array)
], UserModelFineTuneResponseDto.prototype, "samplingParameters", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Các tính năng được hỗ trợ',
        example: ['function_calling'],
        type: [String]
    }),
    __metadata("design:type", Array)
], UserModelFineTuneResponseDto.prototype, "features", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Giá cơ bản cho model (input/output rate)',
        example: { inputRate: 0.01, outputRate: 0.03 }
    }),
    __metadata("design:type", Object)
], UserModelFineTuneResponseDto.prototype, "basePricing", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Giá fine-tune cho model (input/output rate)',
        example: { inputRate: 0.02, outputRate: 0.06 }
    }),
    __metadata("design:type", Object)
], UserModelFineTuneResponseDto.prototype, "fineTunePricing", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Giá training cho model',
        example: 100
    }),
    __metadata("design:type", Number)
], UserModelFineTuneResponseDto.prototype, "trainingPricing", void 0);
//# sourceMappingURL=user-model-fine-tune-response.dto.js.map