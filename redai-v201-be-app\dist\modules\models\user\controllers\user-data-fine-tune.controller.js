"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserDataFineTuneController = void 0;
const swagger_1 = require("../../../../common/swagger");
const decorators_1 = require("../../../auth/decorators");
const guards_1 = require("../../../auth/guards");
const response_1 = require("../../../../common/response");
const common_1 = require("@nestjs/common");
const swagger_2 = require("@nestjs/swagger");
const create_user_data_fine_tune_dto_1 = require("../dto/user-data-fine-tune/create-user-data-fine-tune.dto");
const user_data_fine_tune_query_dto_1 = require("../dto/user-data-fine-tune/user-data-fine-tune-query.dto");
const user_data_fine_tune_service_1 = require("../services/user-data-fine-tune.service");
let UserDataFineTuneController = class UserDataFineTuneController {
    userDataFineTuneService;
    constructor(userDataFineTuneService) {
        this.userDataFineTuneService = userDataFineTuneService;
    }
    create(createDto, userId) {
        return this.userDataFineTuneService.create(userId, createDto);
    }
    updateStatus(id, userId) {
        return this.userDataFineTuneService.updateStatus(userId, id);
    }
    findAll(userId, queryDto) {
        return this.userDataFineTuneService.findAll(userId, queryDto);
    }
    urlUpload(mime, userId) {
        return this.userDataFineTuneService.urlUpload(userId, mime);
    }
    findOne(id, userId) {
        return this.userDataFineTuneService.findOne(userId, id);
    }
    update(id, updateDto, userId) {
        return this.userDataFineTuneService.update(userId, id, updateDto);
    }
    remove(id, userId) {
        return this.userDataFineTuneService.remove(userId, id);
    }
};
exports.UserDataFineTuneController = UserDataFineTuneController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_2.ApiOperation)({ summary: 'Tạo mới dataset fine tune' }),
    (0, swagger_2.ApiResponse)({
        status: 201,
        description: 'Tạo mới dataset fine tune thành công',
        type: response_1.ApiResponseDto
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, decorators_1.CurrentUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_user_data_fine_tune_dto_1.CreateUserDataFineTuneDto, Number]),
    __metadata("design:returntype", Promise)
], UserDataFineTuneController.prototype, "create", null);
__decorate([
    (0, common_1.Patch)(':id/upload-url-success'),
    (0, swagger_2.ApiOperation)({ summary: 'Cập nhật trạng thái upload dataset' }),
    (0, swagger_2.ApiResponse)({
        status: 200,
        description: 'Cập nhật trạng thái dataset thành công',
        type: response_1.ApiResponseDto
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, decorators_1.CurrentUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], UserDataFineTuneController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_2.ApiOperation)({
        summary: 'Lấy danh sách dataset fine tune của user có phân trang',
        description: 'API này hỗ trợ tìm kiếm theo tên dataset, phân trang và sắp xếp'
    }),
    (0, swagger_2.ApiResponse)({
        status: 200,
        description: 'Danh sách dataset fine tune',
        type: response_1.ApiResponseDto
    }),
    __param(0, (0, decorators_1.CurrentUser)('id')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, user_data_fine_tune_query_dto_1.UserDataFineTuneQueryDto]),
    __metadata("design:returntype", Promise)
], UserDataFineTuneController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('upload-url'),
    (0, swagger_2.ApiOperation)({ summary: 'Lấy URL upload dataset' }),
    (0, swagger_2.ApiResponse)({
        status: 200,
        description: 'URL upload dataset',
        type: response_1.ApiResponseDto
    }),
    __param(0, (0, common_1.Query)('mime')),
    __param(1, (0, decorators_1.CurrentUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], UserDataFineTuneController.prototype, "urlUpload", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_2.ApiOperation)({ summary: 'Lấy chi tiết dataset fine tune' }),
    (0, swagger_2.ApiResponse)({
        status: 200,
        description: 'Chi tiết dataset fine tune',
        type: response_1.ApiResponseDto
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, decorators_1.CurrentUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", void 0)
], UserDataFineTuneController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_2.ApiOperation)({ summary: 'Cập nhật dataset fine tune' }),
    (0, swagger_2.ApiResponse)({
        status: 200,
        description: 'Cập nhật dataset fine tune thành công',
        type: response_1.ApiResponseDto
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, decorators_1.CurrentUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Number]),
    __metadata("design:returntype", void 0)
], UserDataFineTuneController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_2.ApiOperation)({ summary: 'Xóa dataset fine tune' }),
    (0, swagger_2.ApiResponse)({
        status: 200,
        description: 'Xóa dataset fine tune thành công',
        type: response_1.ApiResponseDto
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, decorators_1.CurrentUser)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", void 0)
], UserDataFineTuneController.prototype, "remove", null);
exports.UserDataFineTuneController = UserDataFineTuneController = __decorate([
    (0, swagger_2.ApiTags)(swagger_1.SWAGGER_API_TAGS.USER_FINETUNING_DATA),
    (0, common_1.Controller)('user/data-fine-tune'),
    (0, common_1.UseGuards)(guards_1.JwtUserGuard),
    (0, swagger_2.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [user_data_fine_tune_service_1.UserDataFineTuneService])
], UserDataFineTuneController);
//# sourceMappingURL=user-data-fine-tune.controller.js.map