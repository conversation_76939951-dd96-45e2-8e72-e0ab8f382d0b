"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _langgraph = require("@langchain/langgraph");
const _common = require("@nestjs/common");
const logger = new _common.Logger('Test');
const graphState = _langgraph.Annotation.Root({
    messages: (0, _langgraph.Annotation)({
        reducer: _langgraph.messagesStateReducer,
        default: ()=>[]
    })
});
function sleep(ms) {
    return new Promise((resolve)=>setTimeout(resolve, ms));
}
const stateGraph = new _langgraph.StateGraph(graphState).addNode('a', async ()=>{
    logger.log('a');
    await sleep(1000);
    return {
        messages: [
            {
                role: 'user',
                content: 'a'
            }
        ]
    };
}).addNode('b', async ()=>{
    logger.log('b');
    await sleep(1000);
    return {
        messages: [
            {
                role: 'user',
                content: 'b'
            }
        ]
    };
}).addNode('c', async ()=>{
    logger.log('c');
    await sleep(1000);
    return {
        messages: [
            {
                role: 'user',
                content: 'c'
            }
        ]
    };
}).addNode('d', async ()=>{
    logger.log('d');
    await sleep(1000);
    return {
        messages: [
            {
                role: 'user',
                content: 'd'
            }
        ]
    };
}).addNode('e', async ()=>{
    logger.log('e');
    await sleep(1000);
    return {
        messages: [
            {
                role: 'user',
                content: 'e'
            }
        ]
    };
}).addNode('f', async ()=>{
    logger.log('f');
    await sleep(1000);
    return {
        messages: [
            {
                role: 'user',
                content: 'f'
            }
        ]
    };
}).addNode('g', async ()=>{
    logger.log('g');
    await sleep(1000);
    return {
        messages: [
            {
                role: 'user',
                content: 'g'
            }
        ]
    };
}).addNode('h', async ()=>{
    logger.log('h');
    (0, _langgraph.interrupt)('funny businessfunny businessfunny businessfunny businessfunny businessfunny businessfunny businessfunny businessfunny businessfunny business');
    await sleep(1000);
    return {
        messages: [
            {
                role: 'user',
                content: 'h'
            }
        ]
    };
}).addNode('i', async ()=>{
    logger.log('i');
    await sleep(1000);
    return {
        messages: [
            {
                role: 'user',
                content: 'i'
            }
        ]
    };
}).addNode('j', async ()=>{
    logger.log('j');
    await sleep(1000);
    return {
        messages: [
            {
                role: 'user',
                content: 'j'
            }
        ]
    };
}).addEdge(_langgraph.START, 'a').addEdge('a', 'b').addEdge('b', 'c').addEdge('c', 'd').addEdge('d', 'e').addEdge('e', 'f').addEdge('f', 'g').addEdge('g', 'h').addEdge('h', 'i').addEdge('i', 'j').compile({
    checkpointer: new _langgraph.MemorySaver()
});
async function main() {
    const abortController = new AbortController();
    const threadId = '123';
    let count = 0;
    try {
        const stream = stateGraph.streamEvents({
            messages: []
        }, {
            configurable: {
                thread_id: threadId
            },
            version: 'v2',
            signal: abortController.signal
        });
        for await (const evt of stream){
            ++count;
        }
    } catch (e) {
        logger.debug('aborted');
    }
    const state = await stateGraph.getState({
        configurable: {
            thread_id: threadId
        }
    });
    const checkpointId = state?.config?.configurable?.['checkpoint_id'];
    const resumeStream = stateGraph.streamEvents(null, {
        configurable: {
            thread_id: threadId,
            checkpoint_id: checkpointId
        },
        version: 'v2'
    });
    for await (const evt of resumeStream){
        const state = await stateGraph.getState({
            configurable: {
                thread_id: threadId
            }
        });
    // logger.debug(state);
    }
}

//# sourceMappingURL=test.js.map