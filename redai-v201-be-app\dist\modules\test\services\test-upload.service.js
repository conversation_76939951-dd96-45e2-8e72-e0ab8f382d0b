"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TestUploadService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestUploadService = void 0;
const common_1 = require("@nestjs/common");
const s3_service_1 = require("../../../shared/services/s3.service");
const utils_1 = require("../../../shared/utils");
const common_2 = require("../../../common");
let TestUploadService = TestUploadService_1 = class TestUploadService {
    s3Service;
    logger = new common_1.Logger(TestUploadService_1.name);
    constructor(s3Service) {
        this.s3Service = s3Service;
    }
    async createMediaUploadUrl(dto) {
        try {
            let mediaType;
            let categoryFolder;
            if (dto.mediaType.startsWith('image/')) {
                mediaType = dto.mediaType;
                categoryFolder = utils_1.CategoryFolderEnum.IMAGE;
            }
            else if (dto.mediaType.startsWith('video/')) {
                mediaType = dto.mediaType;
                categoryFolder = utils_1.CategoryFolderEnum.VIDEO;
            }
            else if (dto.mediaType.startsWith('application/') || dto.mediaType.startsWith('text/')) {
                mediaType = dto.mediaType;
                categoryFolder = utils_1.CategoryFolderEnum.DOCUMENT;
            }
            else {
                throw new common_2.AppException(common_2.ErrorCode.FILE_TYPE_NOT_FOUND, `Loại media không được hỗ trợ: ${dto.mediaType}`);
            }
            const fileName = dto.fileName || `file-${Date.now()}`;
            const key = (0, utils_1.generateS3Key)({
                baseFolder: 'test',
                categoryFolder: categoryFolder,
                fileName: fileName,
                useTimeFolder: true,
            });
            const expirationTime = utils_1.TimeIntervalEnum.FIFTEEN_MINUTES;
            const uploadUrl = await this.s3Service.createPresignedWithID(key, expirationTime, mediaType, dto.fileSize);
            const expiresAt = Date.now() + expirationTime * 1000;
            return {
                uploadUrl,
                key,
                expiresAt,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo URL upload: ${error.message}`, error.stack);
            if (error instanceof common_2.AppException) {
                throw error;
            }
            throw new common_2.AppException(common_2.ErrorCode.INTERNAL_SERVER_ERROR, `Không thể tạo URL upload: ${error.message}`);
        }
    }
};
exports.TestUploadService = TestUploadService;
exports.TestUploadService = TestUploadService = TestUploadService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [s3_service_1.S3Service])
], TestUploadService);
//# sourceMappingURL=test-upload.service.js.map