{"version": 3, "sources": ["../../../src/agent/database/user-messages.queries.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\nimport { AgentDatabaseService } from './database.service';\n\n/**\n * Interface for user message data\n */\nexport interface UserMessageData {\n  message_id?: string;\n  thread_id: string;\n  role: 'user' | 'assistant';\n  content: any; // JSONB content\n  timestamp?: number;\n  created_by: number;\n}\n\n/**\n * Interface for creating user messages\n */\nexport interface CreateUserMessageData {\n  thread_id: string;\n  role: 'user' | 'assistant';\n  content: any;\n  created_by: number;\n}\n\n/**\n * User Messages Queries Service (Worker)\n * \n * Handles database operations for the user_messages table using raw SQL queries.\n * This service is used in the worker to persist AI assistant responses.\n */\n@Injectable()\nexport class UserMessagesQueries {\n  private readonly logger = new Logger(UserMessagesQueries.name);\n\n  constructor(private readonly databaseService: AgentDatabaseService) {}\n\n  /**\n   * Create a new user message\n   * @param messageData Message data to create\n   * @returns Promise<string> Created message ID\n   */\n  async createMessage(messageData: CreateUserMessageData): Promise<string> {\n    const query = `\n      INSERT INTO user_messages (thread_id, role, content, timestamp, created_by)\n      VALUES ($1, $2, $3, $4, $5)\n      RETURNING message_id\n    `;\n\n    const timestamp = Date.now();\n    const values = [\n      messageData.thread_id,\n      messageData.role,\n      JSON.stringify(messageData.content),\n      timestamp,\n      messageData.created_by\n    ];\n\n    try {\n      this.logger.debug(`Creating message for thread ${messageData.thread_id}, role: ${messageData.role}`);\n      \n      const result = await this.databaseService.query(query, values);\n      const messageId = result[0]?.message_id;\n\n      if (!messageId) {\n        throw new Error('Failed to create message - no ID returned');\n      }\n\n      this.logger.log(`Created message ${messageId} for thread ${messageData.thread_id}`);\n      return messageId;\n\n    } catch (error) {\n      this.logger.error(`Failed to create message for thread ${messageData.thread_id}:`, error);\n      throw new Error(`Message creation failed: ${error.message}`);\n    }\n  }\n\n  /**\n   * Get messages by thread ID\n   * @param threadId Thread ID to get messages for\n   * @param limit Optional limit on number of messages\n   * @returns Promise<UserMessageData[]> Array of messages\n   */\n  async getMessagesByThreadId(threadId: string, limit?: number): Promise<UserMessageData[]> {\n    let query = `\n      SELECT message_id, thread_id, role, content, timestamp, created_by\n      FROM user_messages\n      WHERE thread_id = $1\n      ORDER BY timestamp ASC\n    `;\n\n    const values: any[] = [threadId];\n\n    if (limit) {\n      query += ` LIMIT $2`;\n      values.push(limit);\n    }\n\n    try {\n      this.logger.debug(`Getting messages for thread ${threadId}${limit ? ` (limit: ${limit})` : ''}`);\n      \n      const result = await this.databaseService.query(query, values);\n      \n      const messages: UserMessageData[] = result.map(row => ({\n        message_id: row.message_id,\n        thread_id: row.thread_id,\n        role: row.role,\n        content: row.content, // Already parsed by PostgreSQL\n        timestamp: row.timestamp,\n        created_by: row.created_by\n      }));\n\n      this.logger.debug(`Found ${messages.length} messages for thread ${threadId}`);\n      return messages;\n\n    } catch (error) {\n      this.logger.error(`Failed to get messages for thread ${threadId}:`, error);\n      throw new Error(`Message retrieval failed: ${error.message}`);\n    }\n  }\n\n  /**\n   * Update message content by ID\n   * @param messageId Message ID to update\n   * @param content New content for the message\n   * @returns Promise<boolean> True if update was successful\n   */\n  async updateMessageContent(messageId: string, content: any): Promise<boolean> {\n    const query = `\n      UPDATE user_messages\n      SET content = $1, timestamp = $2\n      WHERE message_id = $3\n    `;\n\n    const timestamp = Date.now();\n    const values = [JSON.stringify(content), timestamp, messageId];\n\n    try {\n      this.logger.debug(`Updating message ${messageId}`);\n      \n      const result = await this.databaseService.query(query, values);\n      \n      this.logger.log(`Updated message ${messageId}`);\n      return true;\n\n    } catch (error) {\n      this.logger.error(`Failed to update message ${messageId}:`, error);\n      throw new Error(`Message update failed: ${error.message}`);\n    }\n  }\n\n  /**\n   * Delete message by ID\n   * @param messageId Message ID to delete\n   * @returns Promise<boolean> True if deletion was successful\n   */\n  async deleteMessage(messageId: string): Promise<boolean> {\n    const query = `\n      DELETE FROM user_messages\n      WHERE message_id = $1\n    `;\n\n    try {\n      this.logger.debug(`Deleting message ${messageId}`);\n      \n      await this.databaseService.query(query, [messageId]);\n      \n      this.logger.log(`Deleted message ${messageId}`);\n      return true;\n\n    } catch (error) {\n      this.logger.error(`Failed to delete message ${messageId}:`, error);\n      throw new Error(`Message deletion failed: ${error.message}`);\n    }\n  }\n\n  /**\n   * Delete messages by thread ID\n   * @param threadId Thread ID to delete messages for\n   * @returns Promise<boolean> True if deletion was successful\n   */\n  async deleteMessagesByThreadId(threadId: string): Promise<boolean> {\n    const query = `\n      DELETE FROM user_messages\n      WHERE thread_id = $1\n    `;\n\n    try {\n      this.logger.debug(`Deleting messages for thread ${threadId}`);\n\n      await this.databaseService.query(query, [threadId]);\n\n      this.logger.log(`Deleted messages for thread ${threadId}`);\n      return true;\n\n    } catch (error) {\n      this.logger.error(`Failed to delete messages for thread ${threadId}:`, error);\n      throw new Error(`Message deletion failed: ${error.message}`);\n    }\n  }\n}\n"], "names": ["UserMessagesQueries", "createMessage", "messageData", "query", "timestamp", "Date", "now", "values", "thread_id", "role", "JSON", "stringify", "content", "created_by", "logger", "debug", "result", "databaseService", "messageId", "message_id", "Error", "log", "error", "message", "getMessagesByThreadId", "threadId", "limit", "push", "messages", "map", "row", "length", "updateMessageContent", "deleteMessage", "deleteMessagesByThreadId", "constructor", "<PERSON><PERSON>", "name"], "mappings": ";;;;+BAgCaA;;;eAAAA;;;wBAhCsB;iCACE;;;;;;;;;;AA+B9B,IAAA,AAAMA,sBAAN,MAAMA;IAKX;;;;GAIC,GACD,MAAMC,cAAcC,WAAkC,EAAmB;QACvE,MAAMC,QAAQ,CAAC;;;;IAIf,CAAC;QAED,MAAMC,YAAYC,KAAKC,GAAG;QAC1B,MAAMC,SAAS;YACbL,YAAYM,SAAS;YACrBN,YAAYO,IAAI;YAChBC,KAAKC,SAAS,CAACT,YAAYU,OAAO;YAClCR;YACAF,YAAYW,UAAU;SACvB;QAED,IAAI;YACF,IAAI,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,4BAA4B,EAAEb,YAAYM,SAAS,CAAC,QAAQ,EAAEN,YAAYO,IAAI,EAAE;YAEnG,MAAMO,SAAS,MAAM,IAAI,CAACC,eAAe,CAACd,KAAK,CAACA,OAAOI;YACvD,MAAMW,YAAYF,MAAM,CAAC,EAAE,EAAEG;YAE7B,IAAI,CAACD,WAAW;gBACd,MAAM,IAAIE,MAAM;YAClB;YAEA,IAAI,CAACN,MAAM,CAACO,GAAG,CAAC,CAAC,gBAAgB,EAAEH,UAAU,YAAY,EAAEhB,YAAYM,SAAS,EAAE;YAClF,OAAOU;QAET,EAAE,OAAOI,OAAO;YACd,IAAI,CAACR,MAAM,CAACQ,KAAK,CAAC,CAAC,oCAAoC,EAAEpB,YAAYM,SAAS,CAAC,CAAC,CAAC,EAAEc;YACnF,MAAM,IAAIF,MAAM,CAAC,yBAAyB,EAAEE,MAAMC,OAAO,EAAE;QAC7D;IACF;IAEA;;;;;GAKC,GACD,MAAMC,sBAAsBC,QAAgB,EAAEC,KAAc,EAA8B;QACxF,IAAIvB,QAAQ,CAAC;;;;;IAKb,CAAC;QAED,MAAMI,SAAgB;YAACkB;SAAS;QAEhC,IAAIC,OAAO;YACTvB,SAAS,CAAC,SAAS,CAAC;YACpBI,OAAOoB,IAAI,CAACD;QACd;QAEA,IAAI;YACF,IAAI,CAACZ,MAAM,CAACC,KAAK,CAAC,CAAC,4BAA4B,EAAEU,WAAWC,QAAQ,CAAC,SAAS,EAAEA,MAAM,CAAC,CAAC,GAAG,IAAI;YAE/F,MAAMV,SAAS,MAAM,IAAI,CAACC,eAAe,CAACd,KAAK,CAACA,OAAOI;YAEvD,MAAMqB,WAA8BZ,OAAOa,GAAG,CAACC,CAAAA,MAAQ,CAAA;oBACrDX,YAAYW,IAAIX,UAAU;oBAC1BX,WAAWsB,IAAItB,SAAS;oBACxBC,MAAMqB,IAAIrB,IAAI;oBACdG,SAASkB,IAAIlB,OAAO;oBACpBR,WAAW0B,IAAI1B,SAAS;oBACxBS,YAAYiB,IAAIjB,UAAU;gBAC5B,CAAA;YAEA,IAAI,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,MAAM,EAAEa,SAASG,MAAM,CAAC,qBAAqB,EAAEN,UAAU;YAC5E,OAAOG;QAET,EAAE,OAAON,OAAO;YACd,IAAI,CAACR,MAAM,CAACQ,KAAK,CAAC,CAAC,kCAAkC,EAAEG,SAAS,CAAC,CAAC,EAAEH;YACpE,MAAM,IAAIF,MAAM,CAAC,0BAA0B,EAAEE,MAAMC,OAAO,EAAE;QAC9D;IACF;IAEA;;;;;GAKC,GACD,MAAMS,qBAAqBd,SAAiB,EAAEN,OAAY,EAAoB;QAC5E,MAAMT,QAAQ,CAAC;;;;IAIf,CAAC;QAED,MAAMC,YAAYC,KAAKC,GAAG;QAC1B,MAAMC,SAAS;YAACG,KAAKC,SAAS,CAACC;YAAUR;YAAWc;SAAU;QAE9D,IAAI;YACF,IAAI,CAACJ,MAAM,CAACC,KAAK,CAAC,CAAC,iBAAiB,EAAEG,WAAW;YAEjD,MAAMF,SAAS,MAAM,IAAI,CAACC,eAAe,CAACd,KAAK,CAACA,OAAOI;YAEvD,IAAI,CAACO,MAAM,CAACO,GAAG,CAAC,CAAC,gBAAgB,EAAEH,WAAW;YAC9C,OAAO;QAET,EAAE,OAAOI,OAAO;YACd,IAAI,CAACR,MAAM,CAACQ,KAAK,CAAC,CAAC,yBAAyB,EAAEJ,UAAU,CAAC,CAAC,EAAEI;YAC5D,MAAM,IAAIF,MAAM,CAAC,uBAAuB,EAAEE,MAAMC,OAAO,EAAE;QAC3D;IACF;IAEA;;;;GAIC,GACD,MAAMU,cAAcf,SAAiB,EAAoB;QACvD,MAAMf,QAAQ,CAAC;;;IAGf,CAAC;QAED,IAAI;YACF,IAAI,CAACW,MAAM,CAACC,KAAK,CAAC,CAAC,iBAAiB,EAAEG,WAAW;YAEjD,MAAM,IAAI,CAACD,eAAe,CAACd,KAAK,CAACA,OAAO;gBAACe;aAAU;YAEnD,IAAI,CAACJ,MAAM,CAACO,GAAG,CAAC,CAAC,gBAAgB,EAAEH,WAAW;YAC9C,OAAO;QAET,EAAE,OAAOI,OAAO;YACd,IAAI,CAACR,MAAM,CAACQ,KAAK,CAAC,CAAC,yBAAyB,EAAEJ,UAAU,CAAC,CAAC,EAAEI;YAC5D,MAAM,IAAIF,MAAM,CAAC,yBAAyB,EAAEE,MAAMC,OAAO,EAAE;QAC7D;IACF;IAEA;;;;GAIC,GACD,MAAMW,yBAAyBT,QAAgB,EAAoB;QACjE,MAAMtB,QAAQ,CAAC;;;IAGf,CAAC;QAED,IAAI;YACF,IAAI,CAACW,MAAM,CAACC,KAAK,CAAC,CAAC,6BAA6B,EAAEU,UAAU;YAE5D,MAAM,IAAI,CAACR,eAAe,CAACd,KAAK,CAACA,OAAO;gBAACsB;aAAS;YAElD,IAAI,CAACX,MAAM,CAACO,GAAG,CAAC,CAAC,4BAA4B,EAAEI,UAAU;YACzD,OAAO;QAET,EAAE,OAAOH,OAAO;YACd,IAAI,CAACR,MAAM,CAACQ,KAAK,CAAC,CAAC,qCAAqC,EAAEG,SAAS,CAAC,CAAC,EAAEH;YACvE,MAAM,IAAIF,MAAM,CAAC,yBAAyB,EAAEE,MAAMC,OAAO,EAAE;QAC7D;IACF;IApKAY,YAAY,AAAiBlB,eAAqC,CAAE;aAAvCA,kBAAAA;aAFZH,SAAS,IAAIsB,cAAM,CAACpC,oBAAoBqC,IAAI;IAEQ;AAqKvE"}