"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RuleContractActionsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RuleContractActionsService = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../repositories");
const user_repository_1 = require("../../user/repositories/user.repository");
const rule_contract_entity_1 = require("../entities/rule-contract.entity");
const enums_1 = require("../../user/enums");
const exceptions_1 = require("../../../common/exceptions");
const errors_1 = require("../errors");
const contract_helper_service_1 = require("../user/services/contract-helper.service");
const cdn_service_1 = require("../../../shared/services/cdn.service");
const time_interval_util_1 = require("../../../shared/utils/time/time-interval.util");
const services_1 = require("../../email/services");
let RuleContractActionsService = RuleContractActionsService_1 = class RuleContractActionsService {
    ruleContractRepository;
    emailService;
    userRepository;
    contractHelperService;
    cdnService;
    logger = new common_1.Logger(RuleContractActionsService_1.name);
    constructor(ruleContractRepository, emailService, userRepository, contractHelperService, cdnService) {
        this.ruleContractRepository = ruleContractRepository;
        this.emailService = emailService;
        this.userRepository = userRepository;
        this.contractHelperService = contractHelperService;
        this.cdnService = cdnService;
    }
    async saveContract(context, event) {
        try {
            const { userId, contractId, contractType, contractStatus, contractKey } = context;
            const { data } = event;
            if (contractId) {
                const contract = await this.ruleContractRepository.findById(contractId);
                if (contract) {
                    contract.type = data?.contractType || contractType;
                    contract.contractUrlPdf = data?.contractKey || contractKey;
                    contract.updatedAt = Date.now();
                    await this.ruleContractRepository.save(contract);
                    return {
                        contractType: contract.type,
                        contractKey: contract.contractUrlPdf,
                        updatedAt: contract.updatedAt,
                    };
                }
            }
            const newContract = this.ruleContractRepository.create();
            newContract.userId = userId || 0;
            newContract.type = data?.contractType || contractType;
            newContract.status = contractStatus;
            newContract.contractUrlPdf = data?.contractKey || contractKey;
            newContract.createdAt = Date.now();
            newContract.updatedAt = Date.now();
            const savedContract = await this.ruleContractRepository.save(newContract);
            return {
                contractId: savedContract.id,
                contractType: savedContract.type,
                contractStatus: savedContract.status,
                contractKey: savedContract.contractUrlPdf,
                createdAt: savedContract.createdAt,
                updatedAt: savedContract.updatedAt,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lưu hợp đồng: ${error.message}`, error.stack);
            throw error;
        }
    }
    async saveUserSignature(context, event) {
        try {
            const { contractId } = context;
            const { data } = event;
            if (!contractId) {
                throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND, 'Không tìm thấy ID hợp đồng trong ngữ cảnh');
            }
            const contract = await this.ruleContractRepository.findById(contractId);
            if (!contract) {
                throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND, `Không tìm thấy hợp đồng với ID ${contractId}`);
            }
            const now = Date.now();
            contract.status = rule_contract_entity_1.ContractStatusEnum.PENDING_APPROVAL;
            contract.userSignatureAt = now;
            contract.updatedAt = now;
            await this.ruleContractRepository.save(contract);
            return {
                contractStatus: contract.status,
                userSignatureAt: contract.userSignatureAt,
                signatureData: data?.signatureData,
                updatedAt: contract.updatedAt,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lưu chữ ký người dùng: ${error.message}`, error.stack);
            throw error;
        }
    }
    async saveAdminApproval(context, event) {
        try {
            const { contractId } = context;
            const { data } = event;
            if (!contractId) {
                throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND, 'Không tìm thấy ID hợp đồng trong ngữ cảnh');
            }
            const contract = await this.ruleContractRepository.findById(contractId);
            if (!contract) {
                throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND, `Không tìm thấy hợp đồng với ID ${contractId}`);
            }
            const now = Date.now();
            contract.status = rule_contract_entity_1.ContractStatusEnum.APPROVED;
            contract.adminSignatureAt = now;
            contract.updatedAt = now;
            await this.ruleContractRepository.save(contract);
            return {
                contractStatus: contract.status,
                adminId: data?.adminId,
                adminSignatureAt: contract.adminSignatureAt,
                updatedAt: contract.updatedAt,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lưu phê duyệt của admin: ${error.message}`, error.stack);
            throw error;
        }
    }
    async saveRejection(context, event) {
        try {
            const { contractId } = context;
            const { data } = event;
            if (!contractId) {
                throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND, 'Không tìm thấy ID hợp đồng trong ngữ cảnh');
            }
            const contract = await this.ruleContractRepository.findById(contractId);
            if (!contract) {
                throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND, `Không tìm thấy hợp đồng với ID ${contractId}`);
            }
            const now = Date.now();
            contract.status = rule_contract_entity_1.ContractStatusEnum.REJECTED;
            contract.rejectReason = data?.rejectionReason;
            contract.updatedAt = now;
            await this.ruleContractRepository.save(contract);
            return {
                contractStatus: contract.status,
                adminId: data?.adminId,
                rejectionReason: data?.rejectionReason,
                updatedAt: contract.updatedAt,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi lưu từ chối của admin: ${error.message}`, error.stack);
            throw error;
        }
    }
    async clearRejectionReason(context, _event) {
        try {
            const { contractId } = context;
            if (!contractId) {
                throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND, 'Không tìm thấy ID hợp đồng trong ngữ cảnh');
            }
            const contract = await this.ruleContractRepository.findById(contractId);
            if (!contract) {
                throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND, `Không tìm thấy hợp đồng với ID ${contractId}`);
            }
            const now = Date.now();
            contract.status = rule_contract_entity_1.ContractStatusEnum.DRAFT;
            contract.rejectReason = '';
            contract.updatedAt = now;
            await this.ruleContractRepository.save(contract);
            return {
                contractStatus: contract.status,
                rejectionReason: null,
                updatedAt: contract.updatedAt,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi xóa lý do từ chối: ${error.message}`, error.stack);
            throw error;
        }
    }
    async upgradeContractType(context, event) {
        try {
            const { contractId, userId } = context;
            const { data } = event;
            if (contractId) {
                const contract = await this.ruleContractRepository.findById(contractId);
                if (contract) {
                    const now = Date.now();
                    contract.type = rule_contract_entity_1.ContractTypeEnum.BUSINESS;
                    contract.contractUrlPdf = data?.newContractKey || contract.contractUrlPdf;
                    contract.updatedAt = now;
                    await this.ruleContractRepository.save(contract);
                    if (userId) {
                        const user = await this.userRepository.findById(userId);
                        if (user) {
                            user.type = enums_1.UserTypeEnum.BUSINESS;
                            user.updatedAt = Date.now();
                            await this.userRepository['repository'].save(user);
                        }
                    }
                    return {
                        contractType: contract.type,
                        contractKey: contract.contractUrlPdf,
                        updatedAt: contract.updatedAt,
                    };
                }
            }
            throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND, 'Không tìm thấy hợp đồng để nâng cấp');
        }
        catch (error) {
            this.logger.error(`Lỗi khi nâng cấp loại hợp đồng: ${error.message}`, error.stack);
            throw error;
        }
    }
    async notifyAdmin(context, _event) {
        try {
            const { contractId, userId } = context;
            if (!userId) {
                this.logger.warn(`ID người dùng không hợp lệ`);
                return;
            }
            const user = await this.userRepository.findById(userId);
            if (!user) {
                this.logger.warn(`Không tìm thấy người dùng với ID ${userId}`);
                return;
            }
            await this.emailService.sendRuleContractProcessing({
                EMAIL: process.env.ADMIN_EMAIL || '<EMAIL>',
                RULE_CONTRACT_ID: String(contractId || 0),
                NAME: user.fullName,
                USER_ID: String(userId)
            });
        }
        catch (error) {
            this.logger.error(`Lỗi khi gửi thông báo cho admin: ${error.message}`, error.stack);
        }
    }
    async notifyUser(context, _event) {
        try {
            const { contractId, userId, contractStatus, rejectionReason } = context;
            if (!userId) {
                this.logger.warn(`ID người dùng không hợp lệ`);
                return;
            }
            const user = await this.userRepository.findById(userId);
            if (!user) {
                this.logger.warn(`Không tìm thấy người dùng với ID ${userId}`);
                return;
            }
            if (contractStatus === rule_contract_entity_1.ContractStatusEnum.APPROVED) {
                await this.emailService.sendRuleContractSigned({
                    EMAIL: user.email,
                    RULE_CONTRACT_ID: String(contractId || 0),
                    NAME: user.fullName,
                    RULE_CONTRACT_SIGNED_DATE: new Date().toISOString()
                });
            }
            else if (contractStatus === rule_contract_entity_1.ContractStatusEnum.REJECTED) {
                await this.emailService.sendRuleContractSignFailed({
                    EMAIL: user.email,
                    RULE_CONTRACT_ID: String(contractId || 0),
                    NAME: user.fullName,
                    RULE_REASON: rejectionReason || 'Không đáp ứng yêu cầu'
                });
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi gửi thông báo cho người dùng: ${error.message}`, error.stack);
        }
    }
    async finalizeContract(context, _event) {
        try {
            const { userId, contractType } = context;
            if (contractType === rule_contract_entity_1.ContractTypeEnum.BUSINESS && userId) {
                const user = await this.userRepository.findById(userId);
                if (user) {
                    user.type = enums_1.UserTypeEnum.BUSINESS;
                    user.updatedAt = Date.now();
                    await this.userRepository['repository'].save(user);
                }
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi hoàn tất hợp đồng: ${error.message}`, error.stack);
        }
    }
    isValidSignature(_context, event) {
        return !!event.data?.signatureData;
    }
    isAdmin(_context, event) {
        return !!event.data?.adminId;
    }
    canUpgradeToBusinessType(context, _event) {
        return context.contractType === rule_contract_entity_1.ContractTypeEnum.INDIVIDUAL;
    }
    async saveIndividualContract(context, event) {
        try {
            const { userId } = context;
            const { data } = event;
            const individualData = data?.individualContractData;
            if (!individualData) {
                throw new exceptions_1.AppException(errors_1.RULE_CONTRACT_ERROR_CODES.INVALID_DATA, 'Dữ liệu hợp đồng cá nhân không hợp lệ');
            }
            const positions = this.createIndividualContractPositions(individualData);
            const { contractKey, contractBase64 } = await this.contractHelperService.createIndividualRuleContract(userId || 0, positions);
            const newContract = this.ruleContractRepository.create({
                userId: userId || 0,
                type: rule_contract_entity_1.ContractTypeEnum.INDIVIDUAL,
                status: rule_contract_entity_1.ContractStatusEnum.DRAFT,
                contractUrlPdf: contractKey,
                createdAt: Date.now(),
            });
            const savedContract = await this.ruleContractRepository.save(newContract);
            const contractUrl = this.cdnService.generateUrlView(contractKey, time_interval_util_1.TimeIntervalEnum.ONE_HOUR);
            return {
                contractId: savedContract.id,
                contractKey,
                contractBase64,
                contractUrl: contractUrl || '',
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo và lưu hợp đồng cá nhân: ${error.message}`, error.stack);
            throw error;
        }
    }
    createIndividualContractPositions(data) {
        const formatDate = (dateStr) => {
            const date = new Date(dateStr);
            return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
        };
        const formattedDateOfBirth = formatDate(data.dateOfBirth);
        const formattedIssueDate = formatDate(data.issueDate);
        const positions = [
            { pageIndex: 0, text: data.name, xMm: 50, yMm: 50, size: 12 },
            { pageIndex: 0, text: formattedDateOfBirth, xMm: 50, yMm: 60, size: 12 },
            { pageIndex: 0, text: data.cccd, xMm: 50, yMm: 70, size: 12 },
            { pageIndex: 0, text: formattedIssueDate, xMm: 50, yMm: 80, size: 12 },
            { pageIndex: 0, text: data.issuePlace, xMm: 50, yMm: 90, size: 12 },
            { pageIndex: 0, text: data.phone, xMm: 50, yMm: 100, size: 12 },
            { pageIndex: 0, text: data.address, xMm: 50, yMm: 110, size: 12 },
        ];
        if (data.taxCode) {
            positions.push({ pageIndex: 0, text: data.taxCode, xMm: 50, yMm: 120, size: 12 });
        }
        positions.push({
            pageIndex: 2,
            text: data.name.toUpperCase(),
            xMm: 150,
            yMm: 200,
            size: 14,
        });
        return positions;
    }
};
exports.RuleContractActionsService = RuleContractActionsService;
exports.RuleContractActionsService = RuleContractActionsService = RuleContractActionsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.RuleContractRepository,
        services_1.EmailPlaceholderService,
        user_repository_1.UserRepository,
        contract_helper_service_1.ContractHelperService,
        cdn_service_1.CdnService])
], RuleContractActionsService);
//# sourceMappingURL=rule-contract-actions.service.js.map