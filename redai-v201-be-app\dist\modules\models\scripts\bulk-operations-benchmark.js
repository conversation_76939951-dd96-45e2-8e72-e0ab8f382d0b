"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runBulkOperationsBenchmark = runBulkOperationsBenchmark;
const core_1 = require("@nestjs/core");
const app_module_1 = require("../../../app.module");
const bulk_model_operations_service_1 = require("../services/bulk-model-operations.service");
const system_models_repository_1 = require("../repositories/system-models.repository");
const user_models_repository_1 = require("../repositories/user-models.repository");
const system_model_key_llm_repository_1 = require("../repositories/system-model-key-llm.repository");
const user_model_key_llm_repository_1 = require("../repositories/user-model-key-llm.repository");
const performance_monitor_service_1 = require("../services/performance-monitor.service");
async function runBulkOperationsBenchmark() {
    console.log('🚀 Starting Bulk Operations Performance Benchmark...\n');
    try {
        const app = await core_1.NestFactory.createApplicationContext(app_module_1.AppModule);
        const bulkOperations = app.get(bulk_model_operations_service_1.BulkModelOperationsService);
        const systemModelsRepo = app.get(system_models_repository_1.SystemModelsRepository);
        const userModelsRepo = app.get(user_models_repository_1.UserModelsRepository);
        const systemMappingRepo = app.get(system_model_key_llm_repository_1.SystemModelKeyLlmRepository);
        const userMappingRepo = app.get(user_model_key_llm_repository_1.UserModelKeyLlmRepository);
        const performanceMonitor = app.get(performance_monitor_service_1.PerformanceMonitorService);
        console.log('✅ Application context initialized\n');
        const testScenarios = [
            { recordCount: 10, description: 'Small batch' },
            { recordCount: 50, description: 'Medium batch' },
            { recordCount: 100, description: 'Large batch' },
            { recordCount: 500, description: 'Very large batch' }
        ];
        const results = [];
        for (const scenario of testScenarios) {
            console.log(`📊 Testing ${scenario.description}: ${scenario.recordCount} records`);
            const testData = generateTestModelData(scenario.recordCount);
            const testKeyId = 'test-key-id-' + Date.now();
            const individualResult = await testIndividualOperations(testData, testKeyId, systemModelsRepo, systemMappingRepo, performanceMonitor, scenario.description);
            results.push(individualResult);
            await cleanupTestData(testData, testKeyId, systemModelsRepo, systemMappingRepo);
            const bulkResult = await testBulkOperations(testData, testKeyId, bulkOperations, performanceMonitor, scenario.description);
            results.push(bulkResult);
            await cleanupTestData(testData, testKeyId, systemModelsRepo, systemMappingRepo);
            compareResults(individualResult, bulkResult, scenario);
            console.log('---\n');
        }
        generateSummaryReport(results);
        console.log('✅ Bulk operations benchmark completed successfully!');
    }
    catch (error) {
        console.error('❌ Benchmark failed:', error.message);
        console.error(error.stack);
    }
}
async function testIndividualOperations(testData, keyId, systemModelsRepo, systemMappingRepo, monitor, scenario) {
    const operationId = `individual_operations_${scenario}_${Date.now()}`;
    monitor.startMonitoring(operationId, {
        testType: 'individual_operations',
        scenario,
        recordCount: testData.length
    });
    const startMemory = process.memoryUsage().heapUsed;
    let queryCount = 0;
    for (const data of testData) {
        try {
            const existingModel = await systemModelsRepo.findByModelId(data.modelId);
            queryCount++;
            let modelInternalId;
            if (!existingModel) {
                const newModel = systemModelsRepo.create({
                    modelId: data.modelId,
                    modelRegistryId: data.modelRegistryId,
                    active: true
                });
                const savedModel = await systemModelsRepo.save(newModel);
                modelInternalId = savedModel.id;
                queryCount++;
            }
            else {
                modelInternalId = existingModel.id;
            }
            const existingMapping = await systemMappingRepo.findByModelAndKey(modelInternalId, keyId);
            queryCount++;
            if (!existingMapping) {
                const mapping = systemMappingRepo.create({
                    modelId: modelInternalId,
                    llmKeyId: keyId
                });
                await systemMappingRepo.save(mapping);
                queryCount++;
            }
        }
        catch (error) {
            console.error(`Individual operation failed for ${data.modelId}:`, error.message);
        }
    }
    const metrics = monitor.stopMonitoring(operationId);
    const endMemory = process.memoryUsage().heapUsed;
    return {
        operation: 'Model Upsert + Mapping',
        approach: 'individual',
        recordCount: testData.length,
        duration: metrics?.duration || 0,
        throughput: testData.length / ((metrics?.duration || 1) / 1000),
        memoryUsage: endMemory - startMemory,
        databaseQueries: queryCount
    };
}
async function testBulkOperations(testData, keyId, bulkOperations, monitor, scenario) {
    const operationId = `bulk_operations_${scenario}_${Date.now()}`;
    monitor.startMonitoring(operationId, {
        testType: 'bulk_operations',
        scenario,
        recordCount: testData.length
    });
    const startMemory = process.memoryUsage().heapUsed;
    const result = await bulkOperations.bulkUpsertSystemModels(testData, keyId);
    const metrics = monitor.stopMonitoring(operationId);
    const endMemory = process.memoryUsage().heapUsed;
    const estimatedQueries = 4;
    return {
        operation: 'Model Upsert + Mapping',
        approach: 'bulk',
        recordCount: testData.length,
        duration: metrics?.duration || 0,
        throughput: testData.length / ((metrics?.duration || 1) / 1000),
        memoryUsage: endMemory - startMemory,
        databaseQueries: estimatedQueries
    };
}
function compareResults(individualResult, bulkResult, scenario) {
    const speedup = individualResult.duration / bulkResult.duration;
    const queryReduction = (individualResult.databaseQueries - bulkResult.databaseQueries) / individualResult.databaseQueries * 100;
    const throughputImprovement = (bulkResult.throughput - individualResult.throughput) / individualResult.throughput * 100;
    console.log(`📈 Results for ${scenario.description} (${scenario.recordCount} records):`);
    console.log(`   Individual Operations (O(N)):`);
    console.log(`     Duration: ${individualResult.duration}ms`);
    console.log(`     Throughput: ${individualResult.throughput.toFixed(2)} records/sec`);
    console.log(`     Database Queries: ${individualResult.databaseQueries}`);
    console.log(`     Memory Usage: ${(individualResult.memoryUsage / 1024 / 1024).toFixed(2)} MB`);
    console.log(`   Bulk Operations (O(1)):`);
    console.log(`     Duration: ${bulkResult.duration}ms`);
    console.log(`     Throughput: ${bulkResult.throughput.toFixed(2)} records/sec`);
    console.log(`     Database Queries: ${bulkResult.databaseQueries}`);
    console.log(`     Memory Usage: ${(bulkResult.memoryUsage / 1024 / 1024).toFixed(2)} MB`);
    console.log(`   🚀 Performance Improvement:`);
    console.log(`     Speedup: ${speedup.toFixed(2)}x faster`);
    console.log(`     Query Reduction: ${queryReduction.toFixed(1)}%`);
    console.log(`     Throughput Improvement: ${throughputImprovement.toFixed(1)}%`);
    console.log(`     Time Saved: ${(individualResult.duration - bulkResult.duration)}ms`);
}
function generateTestModelData(count) {
    const data = [];
    const providers = ['openai', 'anthropic', 'google', 'meta', 'mistral'];
    const modelTypes = ['gpt', 'claude', 'gemini', 'llama', 'mistral'];
    for (let i = 0; i < count; i++) {
        const provider = providers[i % providers.length];
        const modelType = modelTypes[i % modelTypes.length];
        data.push({
            modelId: `${modelType}-${i}-test`,
            modelRegistryId: `registry-${provider}-${i}`,
            metadata: {
                testData: true,
                index: i,
                provider
            }
        });
    }
    return data;
}
async function cleanupTestData(testData, keyId, systemModelsRepo, systemMappingRepo) {
    try {
        await systemMappingRepo
            .createQueryBuilder()
            .delete()
            .where('llmKeyId = :keyId', { keyId })
            .execute();
        const modelIds = testData.map(d => d.modelId);
        await systemModelsRepo
            .createQueryBuilder()
            .delete()
            .where('modelId IN (:...modelIds)', { modelIds })
            .execute();
    }
    catch (error) {
        console.warn('Cleanup failed:', error.message);
    }
}
function generateSummaryReport(results) {
    console.log('\n📊 SUMMARY REPORT\n');
    console.log('Performance Improvements by Batch Size:');
    console.log('----------------------------------------');
    const individualResults = results.filter(r => r.approach === 'individual');
    const bulkResults = results.filter(r => r.approach === 'bulk');
    for (let i = 0; i < individualResults.length; i++) {
        const individual = individualResults[i];
        const bulk = bulkResults[i];
        const speedup = individual.duration / bulk.duration;
        const queryReduction = (individual.databaseQueries - bulk.databaseQueries) / individual.databaseQueries * 100;
        console.log(`${individual.recordCount} records: ${speedup.toFixed(2)}x speedup, ${queryReduction.toFixed(1)}% fewer queries`);
    }
    console.log('\nKey Benefits of Bulk Operations:');
    console.log('- Reduced database round trips from O(N) to O(1)');
    console.log('- Better transaction management');
    console.log('- Improved memory efficiency');
    console.log('- Linear scalability with data size');
}
if (require.main === module) {
    runBulkOperationsBenchmark()
        .then(() => {
        console.log('🎉 Bulk operations benchmark completed!');
        process.exit(0);
    })
        .catch((error) => {
        console.error('💥 Benchmark failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=bulk-operations-benchmark.js.map