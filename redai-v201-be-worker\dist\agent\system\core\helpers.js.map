{"version": 3, "sources": ["../../../../src/agent/system/core/helpers.ts"], "sourcesContent": ["import {\r\n  AIMessage,\r\n  BaseMessage,\r\n  RemoveMessage,\r\n} from '@langchain/core/messages';\r\nimport { ToolMessage } from '@langchain/core/messages/tool';\r\nimport { createHandoffTool } from './handoff-tool';\r\nimport { CustomRunnableConfig } from './react-agent-executor';\r\nimport { Logger } from '@nestjs/common';\r\n\r\nconst logger = new Logger('TrimmerHelper');\r\n\r\n/**\r\n * Scan only the first block and return RemoveMessage(...) for any orphaned\r\n * AI→Tool sequence or stray ToolMessages. Otherwise return [].\r\n */\r\nexport function dropLeadingOrphanAsRemovals(\r\n  messages: BaseMessage[],\r\n): RemoveMessage[] {\r\n  if (messages.length === 0) return [];\r\n\r\n  const [first, ...rest] = messages;\r\n\r\n  // Case A: orphaned AIMessage with N tool_calls\r\n  if (first instanceof AIMessage && first.tool_calls?.length) {\r\n    const expectedIds = first.tool_calls.map((tc) => tc.id);\r\n    // grab the next N messages\r\n    const nextN = rest.slice(0, expectedIds.length);\r\n\r\n    // are they an exact match, in order?\r\n    const allMatch =\r\n      nextN.length === expectedIds.length &&\r\n      nextN.every(\r\n        (msg, i) =>\r\n          msg instanceof ToolMessage && msg.tool_call_id === expectedIds[i],\r\n      );\r\n\r\n    if (!allMatch) {\r\n      // drop the AI + whatever tools DID match\r\n      const toDelete = [\r\n        first,\r\n        ...nextN.filter(\r\n          (msg): msg is ToolMessage =>\r\n            msg instanceof ToolMessage &&\r\n            expectedIds.includes(msg.tool_call_id),\r\n        ),\r\n      ];\r\n      return toDelete\r\n        .filter((m) => !!m.id)\r\n        .map((m) => new RemoveMessage({ id: m.id! }));\r\n    }\r\n    return [];\r\n  }\r\n\r\n  // Case B: stray ToolMessages at front\r\n  if (first instanceof ToolMessage) {\r\n    // collect all leading ToolMessages\r\n    const orphans = messages.filter((m) => m instanceof ToolMessage);\r\n    return orphans\r\n      .filter((m) => !!m.id)\r\n      .map((m) => new RemoveMessage({ id: m.id! }));\r\n  }\r\n\r\n  // nothing to drop\r\n  return [];\r\n}\r\n\r\n/** your existing helper for tail‐end orphan block removal */\r\nexport function dropTrailingOrphanBlock(msgs: BaseMessage[]): BaseMessage[] {\r\n  let end = msgs.length;\r\n\r\n  // 1) strip trailing ToolMessage(s)\r\n  while (end > 0 && msgs[end - 1] instanceof ToolMessage) {\r\n    end--;\r\n  }\r\n\r\n  // 2) if we now end on an AIMessage-with-tool_calls, drop it too\r\n  const last = msgs[end - 1];\r\n  if (\r\n    last instanceof (msgs[0].constructor as any) /* AIMessage */ &&\r\n    Array.isArray((last as any).tool_calls) &&\r\n    (last as any).tool_calls.length > 0\r\n  ) {\r\n    end--;\r\n  }\r\n\r\n  return msgs.slice(0, end);\r\n}\r\n\r\nexport function getHandoffTool(config: CustomRunnableConfig) {\r\n  if (Object.keys(config?.configurable?.agentConfigMap || {})?.length) {\r\n    return createHandoffTool(\r\n      Object.keys(config?.configurable?.agentConfigMap || {}),\r\n    );\r\n  } else {\r\n    return null;\r\n  }\r\n}\r\n"], "names": ["dropLeadingOrphanAsRemovals", "dropTrailingOrphanBlock", "getHandoffTool", "logger", "<PERSON><PERSON>", "messages", "length", "first", "rest", "AIMessage", "tool_calls", "expectedIds", "map", "tc", "id", "nextN", "slice", "allMatch", "every", "msg", "i", "ToolMessage", "tool_call_id", "toDelete", "filter", "includes", "m", "RemoveMessage", "orphans", "msgs", "end", "last", "constructor", "Array", "isArray", "config", "Object", "keys", "configurable", "agentConfigMap", "createHandoffTool"], "mappings": ";;;;;;;;;;;QAgBgBA;eAAAA;;QAoDAC;eAAAA;;QAqBAC;eAAAA;;;0BArFT;sBACqB;6BACM;wBAEX;AAEvB,MAAMC,SAAS,IAAIC,cAAM,CAAC;AAMnB,SAASJ,4BACdK,QAAuB;IAEvB,IAAIA,SAASC,MAAM,KAAK,GAAG,OAAO,EAAE;IAEpC,MAAM,CAACC,OAAO,GAAGC,KAAK,GAAGH;IAEzB,+CAA+C;IAC/C,IAAIE,iBAAiBE,mBAAS,IAAIF,MAAMG,UAAU,EAAEJ,QAAQ;QAC1D,MAAMK,cAAcJ,MAAMG,UAAU,CAACE,GAAG,CAAC,CAACC,KAAOA,GAAGC,EAAE;QACtD,2BAA2B;QAC3B,MAAMC,QAAQP,KAAKQ,KAAK,CAAC,GAAGL,YAAYL,MAAM;QAE9C,qCAAqC;QACrC,MAAMW,WACJF,MAAMT,MAAM,KAAKK,YAAYL,MAAM,IACnCS,MAAMG,KAAK,CACT,CAACC,KAAKC,IACJD,eAAeE,iBAAW,IAAIF,IAAIG,YAAY,KAAKX,WAAW,CAACS,EAAE;QAGvE,IAAI,CAACH,UAAU;YACb,yCAAyC;YACzC,MAAMM,WAAW;gBACfhB;mBACGQ,MAAMS,MAAM,CACb,CAACL,MACCA,eAAeE,iBAAW,IAC1BV,YAAYc,QAAQ,CAACN,IAAIG,YAAY;aAE1C;YACD,OAAOC,SACJC,MAAM,CAAC,CAACE,IAAM,CAAC,CAACA,EAAEZ,EAAE,EACpBF,GAAG,CAAC,CAACc,IAAM,IAAIC,uBAAa,CAAC;oBAAEb,IAAIY,EAAEZ,EAAE;gBAAE;QAC9C;QACA,OAAO,EAAE;IACX;IAEA,sCAAsC;IACtC,IAAIP,iBAAiBc,iBAAW,EAAE;QAChC,mCAAmC;QACnC,MAAMO,UAAUvB,SAASmB,MAAM,CAAC,CAACE,IAAMA,aAAaL,iBAAW;QAC/D,OAAOO,QACJJ,MAAM,CAAC,CAACE,IAAM,CAAC,CAACA,EAAEZ,EAAE,EACpBF,GAAG,CAAC,CAACc,IAAM,IAAIC,uBAAa,CAAC;gBAAEb,IAAIY,EAAEZ,EAAE;YAAE;IAC9C;IAEA,kBAAkB;IAClB,OAAO,EAAE;AACX;AAGO,SAASb,wBAAwB4B,IAAmB;IACzD,IAAIC,MAAMD,KAAKvB,MAAM;IAErB,mCAAmC;IACnC,MAAOwB,MAAM,KAAKD,IAAI,CAACC,MAAM,EAAE,YAAYT,iBAAW,CAAE;QACtDS;IACF;IAEA,gEAAgE;IAChE,MAAMC,OAAOF,IAAI,CAACC,MAAM,EAAE;IAC1B,IACEC,gBAAiBF,IAAI,CAAC,EAAE,CAACG,WAAW,IACpCC,MAAMC,OAAO,CAAC,AAACH,KAAarB,UAAU,KACtC,AAACqB,KAAarB,UAAU,CAACJ,MAAM,GAAG,GAClC;QACAwB;IACF;IAEA,OAAOD,KAAKb,KAAK,CAAC,GAAGc;AACvB;AAEO,SAAS5B,eAAeiC,MAA4B;IACzD,IAAIC,OAAOC,IAAI,CAACF,QAAQG,cAAcC,kBAAkB,CAAC,IAAIjC,QAAQ;QACnE,OAAOkC,IAAAA,8BAAiB,EACtBJ,OAAOC,IAAI,CAACF,QAAQG,cAAcC,kBAAkB,CAAC;IAEzD,OAAO;QACL,OAAO;IACT;AACF"}