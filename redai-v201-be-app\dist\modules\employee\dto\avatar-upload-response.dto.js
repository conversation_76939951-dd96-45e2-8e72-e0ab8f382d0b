"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AvatarUploadResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class AvatarUploadResponseDto {
    uploadUrl;
    avatarKey;
    expiresIn;
    expiresAt;
}
exports.AvatarUploadResponseDto = AvatarUploadResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'URL tạm thời để tải lên avatar',
        example: 'https://storage.example.com/upload/signed-url?token=abc123'
    }),
    __metadata("design:type", String)
], AvatarUploadResponseDto.prototype, "uploadUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Khóa S3 cho avatar',
        example: 'employee-avatars/images/avatar-1-1682506092000-uuid'
    }),
    __metadata("design:type", String)
], AvatarUploadResponseDto.prototype, "avatarKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian hết hạn của URL tạm thời (giây)',
        example: 300,
        required: false
    }),
    __metadata("design:type", Number)
], AvatarUploadResponseDto.prototype, "expiresIn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời điểm hết hạn của URL tạm thời (timestamp)',
        example: 1746968772000
    }),
    __metadata("design:type", Number)
], AvatarUploadResponseDto.prototype, "expiresAt", void 0);
//# sourceMappingURL=avatar-upload-response.dto.js.map