"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NumberTypePreservingInterceptor = void 0;
const common_1 = require("@nestjs/common");
const operators_1 = require("rxjs/operators");
let NumberTypePreservingInterceptor = class NumberTypePreservingInterceptor {
    numericFields = ['size', 'createdAt', 'updatedAt', 'point', 'viewCount', 'like'];
    processData(data) {
        if (data === null || data === undefined) {
            return data;
        }
        if (Array.isArray(data)) {
            return data.map(item => this.processData(item));
        }
        if (typeof data === 'object') {
            const result = { ...data };
            for (const key of Object.keys(result)) {
                if (this.numericFields.includes(key) && typeof result[key] === 'string' && !isNaN(Number(result[key]))) {
                    result[key] = Number(result[key]);
                }
                else if (typeof result[key] === 'object' && result[key] !== null) {
                    result[key] = this.processData(result[key]);
                }
            }
            return result;
        }
        return data;
    }
    intercept(context, next) {
        return next.handle().pipe((0, operators_1.map)(data => {
            return this.processData(data);
        }));
    }
};
exports.NumberTypePreservingInterceptor = NumberTypePreservingInterceptor;
exports.NumberTypePreservingInterceptor = NumberTypePreservingInterceptor = __decorate([
    (0, common_1.Injectable)()
], NumberTypePreservingInterceptor);
//# sourceMappingURL=number-type-preserving.interceptor.js.map