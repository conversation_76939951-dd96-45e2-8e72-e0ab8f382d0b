{"version": 3, "sources": ["../../../../src/agent/system/core/multi-agent.ts"], "sourcesContent": ["import { END, START, StateGraph } from '@langchain/langgraph';\nimport {\n  AgentState,\n  createReactAgent,\n  CustomRunnableConfig,\n  GraphState,\n} from './react-agent-executor';\nimport {\n  HumanMessage,\n  isAIMessage,\n  isToolMessage,\n  RemoveMessage,\n} from '@langchain/core/messages';\nimport {\n  DEFAULT_TRIMMING_STRATEGY,\n  DEFAULT_TRIMMING_THRESHOLD,\n  SUPERVISOR_TAG,\n  WORKER_TAG,\n} from './constants';\nimport { trimMessagesWithStrategy } from './message-trimmer';\nimport { saver } from './checkpoint-saver';\nimport { Logger } from '@nestjs/common';\nimport { initChatModel } from 'langchain/chat_models/universal';\n\nconst logger = new Logger('MultiAgent');\nconst supervisorGraph = createReactAgent({\n  checkpointSaver: saver,\n}).withConfig({\n  tags: [SUPERVISOR_TAG],\n});\n\nconst workerGraph = createReactAgent({\n  checkpointSaver: saver,\n}).withConfig({\n  tags: [WORKER_TAG],\n});\n\nfunction makeCallWorker(worker: typeof workerGraph) {\n  return async (\n    state: (typeof GraphState)['State'],\n    config: CustomRunnableConfig,\n  ) => {\n    const activeAgentId = state.activeAgent;\n    const agentConfig = config?.configurable?.agentConfigMap?.[activeAgentId];\n    if (!agentConfig) {\n      throw new Error(`No configuration found for agent: ${activeAgentId}`);\n    }\n    const input = state.messages.filter(\n      (msg) =>\n        (isAIMessage(msg) || isToolMessage(msg)) &&\n        msg.response_metadata?.invoker === agentConfig.id,\n    );\n    const workerState: (typeof GraphState)['State'] = {\n      messages: input,\n      activeAgent: activeAgentId,\n      metadata: state.metadata,\n    };\n    const output = await worker.invoke(workerState, config);\n\n    return {\n      messages: [\n        output.messages.at(-1),\n        new HumanMessage(\n          `Worker finishes, decide whether to continue working with the workers or response to the user`,\n        ),\n      ],\n      activeAgent: config.configurable?.supervisorAgentId,\n      isSupervisor: true,\n    };\n  };\n}\n\nconst messageTrimmerNode = async (\n  state: AgentState,\n  config?: CustomRunnableConfig,\n) => {\n  logger.debug('reached mesage trimmer node');\n  logger.debug(`before trimming: ${state.messages.length}`);\n  const { messages } = state;\n  logger.warn('extract messages state');\n  const activeAgentId = state.activeAgent;\n  const agentConfig = config?.configurable?.agentConfigMap?.[activeAgentId];\n  if (!agentConfig) {\n    logger.debug('no worker config, skip');\n    return { messages: [] }; // Return empty messages array instead of empty object\n  }\n  if (agentConfig.id !== config?.configurable?.supervisorAgentId) {\n    logger.debug('not supervisor, skip');\n    return { messages: [] }; // Return empty messages array instead of empty object\n  }\n\n  const {\n    type = DEFAULT_TRIMMING_STRATEGY,\n    threshold = DEFAULT_TRIMMING_THRESHOLD,\n  } = agentConfig.trimmingConfig;\n  const model = await initChatModel(\n    `${agentConfig.model.provider.toLowerCase()}:${agentConfig.model.name}`,\n    {},\n  );\n  const strategy = await trimMessagesWithStrategy(\n    messages,\n    type,\n    threshold,\n    model,\n  );\n\n  // Ensure strategy has a valid messages array\n  if (!strategy || !strategy.messages) {\n    logger.warn('Trimming strategy returned invalid result, using empty messages array');\n    return { messages: [] };\n  }\n\n  logger.debug(\n    `removed ${\n      strategy.messages\n        ? strategy.messages.filter((x: any) => x instanceof RemoveMessage).length\n        : 0\n    } messages`,\n  );\n\n  // Final safety check: ensure we're returning a valid object with messages array\n  if (!strategy || typeof strategy !== 'object' || !Array.isArray(strategy.messages)) {\n    logger.error('Message trimmer returned invalid format, using empty messages array');\n    return { messages: [] };\n  }\n\n  return strategy;\n};\n\nconst workflow = new StateGraph(GraphState)\n  .addNode('supervisor', supervisorGraph, {\n    ends: ['worker', 'messageTrimmer'],\n  })\n  .addNode('worker', makeCallWorker(workerGraph), {\n    subgraphs: [workerGraph],\n  })\n  .addNode('messageTrimmer', messageTrimmerNode)\n  .addEdge(START, 'supervisor')\n  .addEdge('worker', 'supervisor')\n  .addEdge('supervisor', 'messageTrimmer')\n  .addEdge('messageTrimmer', END)\n  .compile({ checkpointer: saver });\n\nexport { workflow };\n"], "names": ["workflow", "logger", "<PERSON><PERSON>", "supervisorGraph", "createReactAgent", "checkpointSaver", "saver", "withConfig", "tags", "SUPERVISOR_TAG", "workerGraph", "WORKER_TAG", "makeCallWorker", "worker", "state", "config", "activeAgentId", "activeAgent", "agentConfig", "configurable", "agentConfigMap", "Error", "input", "messages", "filter", "msg", "isAIMessage", "isToolMessage", "response_metadata", "invoker", "id", "workerState", "metadata", "output", "invoke", "at", "HumanMessage", "supervisorAgentId", "isSupervisor", "messageTrimmerNode", "debug", "length", "warn", "type", "DEFAULT_TRIMMING_STRATEGY", "threshold", "DEFAULT_TRIMMING_THRESHOLD", "trimmingConfig", "model", "initChatModel", "provider", "toLowerCase", "name", "strategy", "trimMessagesWithStrategy", "x", "RemoveMessage", "Array", "isArray", "error", "StateGraph", "GraphState", "addNode", "ends", "subgraphs", "addEdge", "START", "END", "compile", "checkpointer"], "mappings": ";;;;+BA+ISA;;;eAAAA;;;2BA/I8B;oCAMhC;0BAMA;2BAMA;gCACkC;iCACnB;wBACC;2BACO;AAE9B,MAAMC,SAAS,IAAIC,cAAM,CAAC;AAC1B,MAAMC,kBAAkBC,IAAAA,oCAAgB,EAAC;IACvCC,iBAAiBC,sBAAK;AACxB,GAAGC,UAAU,CAAC;IACZC,MAAM;QAACC,yBAAc;KAAC;AACxB;AAEA,MAAMC,cAAcN,IAAAA,oCAAgB,EAAC;IACnCC,iBAAiBC,sBAAK;AACxB,GAAGC,UAAU,CAAC;IACZC,MAAM;QAACG,qBAAU;KAAC;AACpB;AAEA,SAASC,eAAeC,MAA0B;IAChD,OAAO,OACLC,OACAC;QAEA,MAAMC,gBAAgBF,MAAMG,WAAW;QACvC,MAAMC,cAAcH,QAAQI,cAAcC,gBAAgB,CAACJ,cAAc;QACzE,IAAI,CAACE,aAAa;YAChB,MAAM,IAAIG,MAAM,CAAC,kCAAkC,EAAEL,eAAe;QACtE;QACA,MAAMM,QAAQR,MAAMS,QAAQ,CAACC,MAAM,CACjC,CAACC,MACC,AAACC,CAAAA,IAAAA,qBAAW,EAACD,QAAQE,IAAAA,uBAAa,EAACF,IAAG,KACtCA,IAAIG,iBAAiB,EAAEC,YAAYX,YAAYY,EAAE;QAErD,MAAMC,cAA4C;YAChDR,UAAUD;YACVL,aAAaD;YACbgB,UAAUlB,MAAMkB,QAAQ;QAC1B;QACA,MAAMC,SAAS,MAAMpB,OAAOqB,MAAM,CAACH,aAAahB;QAEhD,OAAO;YACLQ,UAAU;gBACRU,OAAOV,QAAQ,CAACY,EAAE,CAAC,CAAC;gBACpB,IAAIC,sBAAY,CACd,CAAC,4FAA4F,CAAC;aAEjG;YACDnB,aAAaF,OAAOI,YAAY,EAAEkB;YAClCC,cAAc;QAChB;IACF;AACF;AAEA,MAAMC,qBAAqB,OACzBzB,OACAC;IAEAd,OAAOuC,KAAK,CAAC;IACbvC,OAAOuC,KAAK,CAAC,CAAC,iBAAiB,EAAE1B,MAAMS,QAAQ,CAACkB,MAAM,EAAE;IACxD,MAAM,EAAElB,QAAQ,EAAE,GAAGT;IACrBb,OAAOyC,IAAI,CAAC;IACZ,MAAM1B,gBAAgBF,MAAMG,WAAW;IACvC,MAAMC,cAAcH,QAAQI,cAAcC,gBAAgB,CAACJ,cAAc;IACzE,IAAI,CAACE,aAAa;QAChBjB,OAAOuC,KAAK,CAAC;QACb,OAAO;YAAEjB,UAAU,EAAE;QAAC,GAAG,sDAAsD;IACjF;IACA,IAAIL,YAAYY,EAAE,KAAKf,QAAQI,cAAckB,mBAAmB;QAC9DpC,OAAOuC,KAAK,CAAC;QACb,OAAO;YAAEjB,UAAU,EAAE;QAAC,GAAG,sDAAsD;IACjF;IAEA,MAAM,EACJoB,OAAOC,oCAAyB,EAChCC,YAAYC,qCAA0B,EACvC,GAAG5B,YAAY6B,cAAc;IAC9B,MAAMC,QAAQ,MAAMC,IAAAA,wBAAa,EAC/B,GAAG/B,YAAY8B,KAAK,CAACE,QAAQ,CAACC,WAAW,GAAG,CAAC,EAAEjC,YAAY8B,KAAK,CAACI,IAAI,EAAE,EACvE,CAAC;IAEH,MAAMC,WAAW,MAAMC,IAAAA,wCAAwB,EAC7C/B,UACAoB,MACAE,WACAG;IAGF,6CAA6C;IAC7C,IAAI,CAACK,YAAY,CAACA,SAAS9B,QAAQ,EAAE;QACnCtB,OAAOyC,IAAI,CAAC;QACZ,OAAO;YAAEnB,UAAU,EAAE;QAAC;IACxB;IAEAtB,OAAOuC,KAAK,CACV,CAAC,QAAQ,EACPa,SAAS9B,QAAQ,GACb8B,SAAS9B,QAAQ,CAACC,MAAM,CAAC,CAAC+B,IAAWA,aAAaC,uBAAa,EAAEf,MAAM,GACvE,EACL,SAAS,CAAC;IAGb,gFAAgF;IAChF,IAAI,CAACY,YAAY,OAAOA,aAAa,YAAY,CAACI,MAAMC,OAAO,CAACL,SAAS9B,QAAQ,GAAG;QAClFtB,OAAO0D,KAAK,CAAC;QACb,OAAO;YAAEpC,UAAU,EAAE;QAAC;IACxB;IAEA,OAAO8B;AACT;AAEA,MAAMrD,WAAW,IAAI4D,qBAAU,CAACC,8BAAU,EACvCC,OAAO,CAAC,cAAc3D,iBAAiB;IACtC4D,MAAM;QAAC;QAAU;KAAiB;AACpC,GACCD,OAAO,CAAC,UAAUlD,eAAeF,cAAc;IAC9CsD,WAAW;QAACtD;KAAY;AAC1B,GACCoD,OAAO,CAAC,kBAAkBvB,oBAC1B0B,OAAO,CAACC,gBAAK,EAAE,cACfD,OAAO,CAAC,UAAU,cAClBA,OAAO,CAAC,cAAc,kBACtBA,OAAO,CAAC,kBAAkBE,cAAG,EAC7BC,OAAO,CAAC;IAAEC,cAAc/D,sBAAK;AAAC"}