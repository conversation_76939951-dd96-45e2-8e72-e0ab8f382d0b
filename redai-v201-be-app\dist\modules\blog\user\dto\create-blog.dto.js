"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateBlogMediaDto = exports.CreateBlogDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const enums_1 = require("../../enums");
class CreateBlogDto {
    title;
    description;
    content_media_type;
    thumbnail_media_type;
    point;
    tags;
    status;
}
exports.CreateBlogDto = CreateBlogDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tiêu đề bài viết',
        example: 'Tiêu đề bài viết',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateBlogDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả ngắn về bài viết',
        example: 'Mô tả ngắn về bài viết',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateBlogDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại nội dung',
        example: 'text/html',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateBlogDto.prototype, "content_media_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại thumbnail',
        example: 'image/jpeg',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateBlogDto.prototype, "thumbnail_media_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số point',
        example: 100,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateBlogDto.prototype, "point", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tags',
        example: ['tag1', 'tag2'],
        type: [String],
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateBlogDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái',
        example: enums_1.BlogStatusEnum.DRAFT,
        enum: enums_1.BlogStatusEnum,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(enums_1.BlogStatusEnum),
    __metadata("design:type", String)
], CreateBlogDto.prototype, "status", void 0);
class UpdateBlogMediaDto {
    media_type;
    media_content_type;
}
exports.UpdateBlogMediaDto = UpdateBlogMediaDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại media (content hoặc thumbnail)',
        example: 'content',
        enum: ['content', 'thumbnail'],
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateBlogMediaDto.prototype, "media_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại nội dung media',
        example: 'text/html',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateBlogMediaDto.prototype, "media_content_type", void 0);
//# sourceMappingURL=create-blog.dto.js.map