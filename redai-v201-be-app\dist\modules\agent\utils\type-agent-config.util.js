"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TypeAgentConfigUtil = void 0;
class TypeAgentConfigUtil {
    static createDefaultConfig() {
        return {
            enableAgentProfileCustomization: false,
            enableOutputToMessenger: false,
            enableOutputToWebsiteLiveChat: false,
            enableTaskConversionTracking: false,
            enableResourceUsage: false,
            enableDynamicStrategyExecution: false,
            enableMultiAgentCollaboration: false,
        };
    }
    static createBasicConfig() {
        return {
            enableAgentProfileCustomization: false,
            enableOutputToMessenger: false,
            enableOutputToWebsiteLiveChat: false,
            enableTaskConversionTracking: false,
            enableResourceUsage: false,
            enableDynamicStrategyExecution: false,
            enableMultiAgentCollaboration: false,
        };
    }
    static createFullConfig() {
        return {
            enableAgentProfileCustomization: false,
            enableOutputToMessenger: false,
            enableOutputToWebsiteLiveChat: false,
            enableTaskConversionTracking: false,
            enableResourceUsage: false,
            enableDynamicStrategyExecution: false,
            enableMultiAgentCollaboration: false,
        };
    }
    static validateConfig(config) {
        if (!config || typeof config !== 'object') {
            return false;
        }
        const requiredFields = [
            'enableAgentProfileCustomization',
            'enableOutputToMessenger',
            'enableOutputToWebsiteLiveChat',
            'enableTaskConversionTracking',
            'enableResourceUsage',
            'enableDynamicStrategyExecution',
            'enableMultiAgentCollaboration',
        ];
        return requiredFields.every(field => field in config && typeof config[field] === 'boolean');
    }
    static mergeWithDefault(config) {
        const defaultConfig = this.createDefaultConfig();
        return {
            ...defaultConfig,
            ...config,
        };
    }
    static getConfigFields() {
        return [
            'enableAgentProfileCustomization',
            'enableOutputToMessenger',
            'enableOutputToWebsiteLiveChat',
            'enableTaskConversionTracking',
            'enableResourceUsage',
            'enableDynamicStrategyExecution',
            'enableMultiAgentCollaboration',
        ];
    }
    static getFieldDescriptions() {
        return {
            enableAgentProfileCustomization: 'Có hồ sơ không - Cho phép agent có thông tin hồ sơ cá nhân',
            enableOutputToMessenger: 'Có đầu ra không - Cho phép agent tạo ra các output/kết quả',
            enableOutputToWebsiteLiveChat: 'Có đầu ra không - Cho phép agent tạo ra các output/kết quả',
            enableTaskConversionTracking: 'Có chuyển đổi không - Cho phép agent thực hiện các conversion/chuyển đổi dữ liệu',
            enableResourceUsage: 'Có tài nguyên không - Cho phép agent sử dụng các resources/tài nguyên',
            enableDynamicStrategyExecution: 'Có chiến lược không - Cho phép agent sử dụng các strategy/chiến lược',
            enableMultiAgentCollaboration: 'Có đa agent không - Cho phép agent làm việc với nhiều agent khác',
        };
    }
}
exports.TypeAgentConfigUtil = TypeAgentConfigUtil;
//# sourceMappingURL=type-agent-config.util.js.map