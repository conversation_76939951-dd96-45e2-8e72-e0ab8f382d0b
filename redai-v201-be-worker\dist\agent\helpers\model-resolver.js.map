{"version": 3, "sources": ["../../../src/agent/helpers/model-resolver.ts"], "sourcesContent": ["// src/modules/worker/helpers/model-resolver.ts\r\n\r\nimport { ChatOpenAI } from '@langchain/openai';\r\nimport { ChatAnthropic } from '@langchain/anthropic';\r\nimport { ChatGoogleGenerativeAI } from '@langchain/google-genai';\r\nimport { ChatXAI } from '@langchain/xai';\r\nimport { ChatDeepSeek } from '@langchain/deepseek';\r\nimport { ModelFeature, ModelProviderEnum } from '../enums';\r\nimport { SystemAgentConfig } from '../interfaces';\r\n\r\nexport type ModelConfig = SystemAgentConfig['model'];\r\n\r\n/**\r\n * Strip out any parameters for which the matching MODIFY_* capability is absent.\r\n * Always leaves `parameters` defined (possibly as an empty object).\r\n */\r\nexport function cleanModelConfig(config: ModelConfig): ModelConfig {\r\n  // clone so we don’t mutate the original\r\n  const cleaned: ModelConfig = {\r\n    ...config,\r\n    parameters: config.parameters ? { ...config.parameters } : {},\r\n  };\r\n\r\n  // Always leave parameters as an object (may now be empty)\r\n  return cleaned;\r\n}\r\n\r\nexport const resolveModels: {\r\n  [key in ModelProviderEnum]: (\r\n    cfg: ModelConfig,\r\n  ) =>\r\n    | ChatOpenAI\r\n    | ChatAnthropic\r\n    | ChatGoogleGenerativeAI\r\n    | ChatXAI\r\n    | ChatDeepSeek;\r\n} = {\r\n  [ModelProviderEnum.OPENAI]: (config) => {\r\n    const cfg = cleanModelConfig(config);\r\n    return new ChatOpenAI({\r\n      model: cfg.name,\r\n      ...cfg.parameters,\r\n    });\r\n  },\r\n\r\n  [ModelProviderEnum.ANTHROPIC]: (config) => {\r\n    const cfg = cleanModelConfig(config);\r\n    return new ChatAnthropic({\r\n      model: cfg.name,\r\n      streaming: true,\r\n      streamUsage: true,\r\n      ...cfg.parameters,\r\n    });\r\n  },\r\n\r\n  [ModelProviderEnum.GOOGLE]: (config) => {\r\n    const cfg = cleanModelConfig(config);\r\n    if (cfg.parameters?.maxTokens) {\r\n      delete cfg.parameters.maxTokens;\r\n      cfg.parameters['maxOutputTokens'] = cfg.parameters.maxTokens;\r\n    }\r\n    return new ChatGoogleGenerativeAI({\r\n      model: cfg.name,\r\n      streaming: true,\r\n      streamUsage: true,\r\n      ...cfg.parameters,\r\n    });\r\n  },\r\n\r\n  [ModelProviderEnum.XAI]: (config) => {\r\n    const cfg = cleanModelConfig(config);\r\n    return new ChatXAI({\r\n      model: cfg.name,\r\n      streaming: true,\r\n      ...cfg.parameters,\r\n    });\r\n  },\r\n\r\n  [ModelProviderEnum.DEEPSEEK]: (config) => {\r\n    const cfg = cleanModelConfig(config);\r\n    return new ChatDeepSeek({\r\n      model: cfg.name,\r\n      streaming: true,\r\n      streamUsage: true,\r\n      ...cfg.parameters,\r\n    });\r\n  },\r\n};\r\n"], "names": ["cleanModelConfig", "resolveModels", "config", "cleaned", "parameters", "ModelProviderEnum", "OPENAI", "cfg", "ChatOpenAI", "model", "name", "ANTHROPIC", "ChatAnthropic", "streaming", "streamUsage", "GOOGLE", "maxTokens", "ChatGoogleGenerativeAI", "XAI", "ChatXAI", "DEEPSEEK", "ChatDeepSeek"], "mappings": "AAAA,+CAA+C;;;;;;;;;;;;QAgB/BA;eAAAA;;QAWHC;eAAAA;;;wBAzBc;2BACG;6BACS;qBACf;0BACK;uBACmB;AASzC,SAASD,iBAAiBE,MAAmB;IAClD,wCAAwC;IACxC,MAAMC,UAAuB;QAC3B,GAAGD,MAAM;QACTE,YAAYF,OAAOE,UAAU,GAAG;YAAE,GAAGF,OAAOE,UAAU;QAAC,IAAI,CAAC;IAC9D;IAEA,0DAA0D;IAC1D,OAAOD;AACT;AAEO,MAAMF,gBAST;IACF,CAACI,wBAAiB,CAACC,MAAM,CAAC,EAAE,CAACJ;QAC3B,MAAMK,MAAMP,iBAAiBE;QAC7B,OAAO,IAAIM,kBAAU,CAAC;YACpBC,OAAOF,IAAIG,IAAI;YACf,GAAGH,IAAIH,UAAU;QACnB;IACF;IAEA,CAACC,wBAAiB,CAACM,SAAS,CAAC,EAAE,CAACT;QAC9B,MAAMK,MAAMP,iBAAiBE;QAC7B,OAAO,IAAIU,wBAAa,CAAC;YACvBH,OAAOF,IAAIG,IAAI;YACfG,WAAW;YACXC,aAAa;YACb,GAAGP,IAAIH,UAAU;QACnB;IACF;IAEA,CAACC,wBAAiB,CAACU,MAAM,CAAC,EAAE,CAACb;QAC3B,MAAMK,MAAMP,iBAAiBE;QAC7B,IAAIK,IAAIH,UAAU,EAAEY,WAAW;YAC7B,OAAOT,IAAIH,UAAU,CAACY,SAAS;YAC/BT,IAAIH,UAAU,CAAC,kBAAkB,GAAGG,IAAIH,UAAU,CAACY,SAAS;QAC9D;QACA,OAAO,IAAIC,mCAAsB,CAAC;YAChCR,OAAOF,IAAIG,IAAI;YACfG,WAAW;YACXC,aAAa;YACb,GAAGP,IAAIH,UAAU;QACnB;IACF;IAEA,CAACC,wBAAiB,CAACa,GAAG,CAAC,EAAE,CAAChB;QACxB,MAAMK,MAAMP,iBAAiBE;QAC7B,OAAO,IAAIiB,YAAO,CAAC;YACjBV,OAAOF,IAAIG,IAAI;YACfG,WAAW;YACX,GAAGN,IAAIH,UAAU;QACnB;IACF;IAEA,CAACC,wBAAiB,CAACe,QAAQ,CAAC,EAAE,CAAClB;QAC7B,MAAMK,MAAMP,iBAAiBE;QAC7B,OAAO,IAAImB,sBAAY,CAAC;YACtBZ,OAAOF,IAAIG,IAAI;YACfG,WAAW;YACXC,aAAa;YACb,GAAGP,IAAIH,UAAU;QACnB;IACF;AACF"}