"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.STEP_ERROR_CODES = void 0;
const common_1 = require("../../../common");
const common_2 = require("@nestjs/common");
exports.STEP_ERROR_CODES = {
    STEP_NOT_FOUND: new common_1.ErrorCode(10100, 'Không tìm thấy bước', common_2.HttpStatus.NOT_FOUND),
    STEP_CREATION_FAILED: new common_1.ErrorCode(10101, 'Tạo bước thất bại', common_2.HttpStatus.INTERNAL_SERVER_ERROR),
    STEP_UPDATE_FAILED: new common_1.ErrorCode(10102, 'Cập nhật bước thất bại', common_2.HttpStatus.INTERNAL_SERVER_ERROR),
    STEP_DELETE_FAILED: new common_1.ErrorCode(10103, 'X<PERSON>a bước thất bại', common_2.HttpStatus.INTERNAL_SERVER_ERROR),
    STEP_FETCH_FAILED: new common_1.ErrorCode(10104, 'Lấy thông tin bước thất bại', common_2.HttpStatus.INTERNAL_SERVER_ERROR),
    STEP_UNAUTHORIZED: new common_1.ErrorCode(10110, 'Không có quyền truy cập bước này', common_2.HttpStatus.FORBIDDEN),
    STEP_INVALID_TYPE: new common_1.ErrorCode(10120, 'Loại bước không hợp lệ', common_2.HttpStatus.BAD_REQUEST),
    STEP_TASK_COMPLETED: new common_1.ErrorCode(10121, 'Không thể thay đổi bước khi nhiệm vụ đã hoàn thành', common_2.HttpStatus.BAD_REQUEST),
    STEP_TASK_CANCELLED: new common_1.ErrorCode(10122, 'Không thể thay đổi bước khi nhiệm vụ đã bị hủy', common_2.HttpStatus.BAD_REQUEST),
    STEP_INVALID_DATA: new common_1.ErrorCode(10130, 'Dữ liệu bước không hợp lệ', common_2.HttpStatus.BAD_REQUEST),
    STEP_NAME_REQUIRED: new common_1.ErrorCode(10131, 'Tên bước là bắt buộc', common_2.HttpStatus.BAD_REQUEST),
    STEP_NAME_TOO_LONG: new common_1.ErrorCode(10132, 'Tên bước quá dài (tối đa 255 ký tự)', common_2.HttpStatus.BAD_REQUEST),
    STEP_CONFIG_INVALID: new common_1.ErrorCode(10133, 'Cấu hình bước không hợp lệ', common_2.HttpStatus.BAD_REQUEST),
    STEP_ORDER_INVALID: new common_1.ErrorCode(10134, 'Thứ tự bước không hợp lệ', common_2.HttpStatus.BAD_REQUEST),
    STEP_ORDER_DUPLICATE: new common_1.ErrorCode(10135, 'Thứ tự bước đã tồn tại', common_2.HttpStatus.BAD_REQUEST),
    STEP_LIMIT_EXCEEDED: new common_1.ErrorCode(10140, 'Đã vượt quá giới hạn số lượng bước trong nhiệm vụ', common_2.HttpStatus.BAD_REQUEST),
    STEP_PROMPT_INVALID: new common_1.ErrorCode(10150, 'Cấu hình bước nhập liệu không hợp lệ', common_2.HttpStatus.BAD_REQUEST),
    STEP_TRIGGER_INVALID: new common_1.ErrorCode(10151, 'Cấu hình bước kích hoạt không hợp lệ', common_2.HttpStatus.BAD_REQUEST),
    STEP_ACTION_INVALID: new common_1.ErrorCode(10152, 'Cấu hình bước hành động không hợp lệ', common_2.HttpStatus.BAD_REQUEST),
    STEP_MEDIA_INVALID: new common_1.ErrorCode(10153, 'Cấu hình bước xử lý media không hợp lệ', common_2.HttpStatus.BAD_REQUEST),
    STEP_GOOGLE_AUTH_REQUIRED: new common_1.ErrorCode(10160, 'Cần xác thực Google để thực hiện bước này', common_2.HttpStatus.BAD_REQUEST),
    STEP_FACEBOOK_AUTH_REQUIRED: new common_1.ErrorCode(10161, 'Cần xác thực Facebook để thực hiện bước này', common_2.HttpStatus.BAD_REQUEST),
};
//# sourceMappingURL=step.exceptions.js.map