"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlogPurchaseListResponseSchema = exports.BlogPurchaseSchema = void 0;
const swagger_1 = require("@nestjs/swagger");
class BlogPurchaseSchema {
    id;
    userId;
    blogId;
    point;
    purchasedAt;
    platformFeePercent;
    sellerReceivePrice;
    constructor(partial) {
        Object.assign(this, partial);
    }
}
exports.BlogPurchaseSchema = BlogPurchaseSchema;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của giao dịch mua bài viết',
        example: 1,
    }),
    __metadata("design:type", Number)
], BlogPurchaseSchema.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của người mua',
        example: 1,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogPurchaseSchema.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của bài viết',
        example: 1,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogPurchaseSchema.prototype, "blogId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số point thời điểm mua',
        example: 100,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogPurchaseSchema.prototype, "point", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian mua hàng (Unix timestamp)',
        example: 1625097600000,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogPurchaseSchema.prototype, "purchasedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Phần trăm phí sàn',
        example: 10.5,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogPurchaseSchema.prototype, "platformFeePercent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Giá người bán nhận được sau khi trừ phí sàn',
        example: 90,
        nullable: true,
    }),
    __metadata("design:type", Number)
], BlogPurchaseSchema.prototype, "sellerReceivePrice", void 0);
class BlogPurchaseListResponseSchema {
    items;
    meta;
}
exports.BlogPurchaseListResponseSchema = BlogPurchaseListResponseSchema;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách giao dịch mua bài viết',
        type: [BlogPurchaseSchema],
    }),
    __metadata("design:type", Array)
], BlogPurchaseListResponseSchema.prototype, "items", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin phân trang',
        type: 'object',
        properties: {
            totalItems: {
                type: 'number',
                example: 100,
                description: 'Tổng số giao dịch',
            },
            itemCount: {
                type: 'number',
                example: 10,
                description: 'Số giao dịch trên trang hiện tại',
            },
            itemsPerPage: {
                type: 'number',
                example: 10,
                description: 'Số giao dịch trên mỗi trang',
            },
            totalPages: {
                type: 'number',
                example: 10,
                description: 'Tổng số trang',
            },
            currentPage: {
                type: 'number',
                example: 1,
                description: 'Trang hiện tại',
            },
        },
    }),
    __metadata("design:type", Object)
], BlogPurchaseListResponseSchema.prototype, "meta", void 0);
//# sourceMappingURL=blog-purchase.schema.js.map