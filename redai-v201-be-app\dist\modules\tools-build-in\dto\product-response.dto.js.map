{"version": 3, "file": "product-response.dto.js", "sourceRoot": "", "sources": ["../../../../src/modules/tools-build-in/dto/product-response.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAC9C,4CAAoE;AAKpE,MAAa,kBAAkB;IAM7B,EAAE,CAAU;IAMZ,IAAI,CAAS;IAOb,WAAW,CAAU;IAWrB,KAAK,CAAsB;IAO3B,SAAS,CAAgB;IAUzB,MAAM,CAAuB;IAU7B,IAAI,CAAuB;IAY3B,cAAc,CAKZ;IAQF,MAAM,CAAyB;IAO/B,SAAS,CAAU;IAOnB,SAAS,CAAU;CACpB;AAjGD,gDAiGC;AA3FC;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,iBAAiB;QAC9B,OAAO,EAAE,CAAC;QACV,QAAQ,EAAE,IAAI;KACf,CAAC;;8CACU;AAMZ;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,cAAc;QAC3B,OAAO,EAAE,aAAa;KACvB,CAAC;;gDACW;AAOb;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,gBAAgB;QAC7B,OAAO,EAAE,mCAAmC;QAC5C,QAAQ,EAAE,IAAI;KACf,CAAC;;uDACmB;AAWrB;IATC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,cAAc;QAC3B,OAAO,EAAE;YACP,MAAM,EAAE,MAAM;YACd,QAAQ,EAAE,KAAK;YACf,QAAQ,EAAE,EAAE;YACZ,WAAW,EAAE,MAAM;SACpB;KACF,CAAC;;iDACyB;AAO3B;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,UAAU;QACvB,IAAI,EAAE,yBAAa;QACnB,OAAO,EAAE,yBAAa,CAAC,SAAS;KACjC,CAAC;;qDACuB;AAUzB;IARC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,mBAAmB;QAChC,OAAO,EAAE;YACP,IAAI,EAAE,gCAAgC;YACtC,OAAO,EAAE,CAAC,gCAAgC,EAAE,gCAAgC,CAAC;SAC9E;QACD,QAAQ,EAAE,IAAI;KACf,CAAC;;kDAC2B;AAU7B;IARC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,sBAAsB;QACnC,OAAO,EAAE;YACP,UAAU,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC;YACjC,MAAM,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;SACjC;QACD,QAAQ,EAAE,IAAI;KACf,CAAC;;gDACyB;AAY3B;IAVC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,qBAAqB;QAClC,OAAO,EAAE;YACP,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,CAAC;YACX,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE,GAAG;SAChB;QACD,QAAQ,EAAE,IAAI;KACf,CAAC;;0DAMA;AAQF;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,qBAAqB;QAClC,IAAI,EAAE,iCAAqB;QAC3B,OAAO,EAAE,iCAAqB,CAAC,SAAS;QACxC,QAAQ,EAAE,IAAI;KACf,CAAC;;kDAC6B;AAO/B;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,eAAe;QAC5B,OAAO,EAAE,aAAa;QACtB,QAAQ,EAAE,IAAI;KACf,CAAC;;qDACiB;AAOnB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,oBAAoB;QACjC,OAAO,EAAE,aAAa;QACtB,QAAQ,EAAE,IAAI;KACf,CAAC;;qDACiB;AAMrB,MAAa,0BAA0B;IAMrC,EAAE,CAAU;IAMZ,IAAI,CAAS;IAOb,WAAW,CAAU;IAOrB,QAAQ,CAAU;IAOlB,YAAY,CAAU;CACvB;AAlCD,gEAkCC;AA5BC;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,iBAAiB;QAC9B,OAAO,EAAE,CAAC;QACV,QAAQ,EAAE,IAAI;KACf,CAAC;;sDACU;AAMZ;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,cAAc;QAC3B,OAAO,EAAE,gBAAgB;KAC1B,CAAC;;wDACW;AAOb;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,gBAAgB;QAC7B,OAAO,EAAE,2CAA2C;QACpD,QAAQ,EAAE,IAAI;KACf,CAAC;;+DACmB;AAOrB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,cAAc;QAC3B,OAAO,EAAE,CAAC;QACV,QAAQ,EAAE,IAAI;KACf,CAAC;;4DACgB;AAOlB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,iBAAiB;QAC9B,OAAO,EAAE,CAAC;QACV,QAAQ,EAAE,IAAI;KACf,CAAC;;gEACoB;AAMxB,MAAa,wBAAwB;IAKnC,QAAQ,CAAuB;IAM/B,KAAK,CAAS;IAOd,OAAO,CAAU;IAUjB,OAAO,CAAuB;CAC/B;AA7BD,4DA6BC;AAxBC;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,6BAA6B;QAC1C,IAAI,EAAE,CAAC,kBAAkB,CAAC;KAC3B,CAAC;;0DAC6B;AAM/B;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,iBAAiB;QAC9B,OAAO,EAAE,EAAE;KACZ,CAAC;;uDACY;AAOd;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,kBAAkB;QAC/B,OAAO,EAAE,SAAS;QAClB,QAAQ,EAAE,IAAI;KACf,CAAC;;yDACe;AAUjB;IARC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,mBAAmB;QAChC,OAAO,EAAE;YACP,UAAU,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE;YACxC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;SACtB;QACD,QAAQ,EAAE,IAAI;KACf,CAAC;;yDAC4B"}