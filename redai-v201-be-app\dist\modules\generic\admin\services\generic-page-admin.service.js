"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var GenericPageAdminService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericPageAdminService = void 0;
const common_1 = require("@nestjs/common");
const common_2 = require("../../../../common");
const generic_page_error_code_1 = require("../../exceptions/generic-page-error.code");
const generic_page_repository_1 = require("../../repositories/generic-page.repository");
const generic_page_entity_1 = require("../../entities/generic-page.entity");
const dto_1 = require("../dto");
const generic_page_enum_1 = require("../../constants/generic-page.enum");
let GenericPageAdminService = GenericPageAdminService_1 = class GenericPageAdminService {
    genericPageRepository;
    logger = new common_1.Logger(GenericPageAdminService_1.name);
    constructor(genericPageRepository) {
        this.genericPageRepository = genericPageRepository;
    }
    async createGenericPage(createGenericPageDto, employeeId) {
        try {
            const isPathExists = await this.genericPageRepository.isPathExists(createGenericPageDto.path);
            if (isPathExists) {
                throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_PATH_ALREADY_EXISTS, `Đường dẫn ${createGenericPageDto.path} đã tồn tại`);
            }
            const genericPage = new generic_page_entity_1.GenericPage();
            genericPage.name = createGenericPageDto.name;
            genericPage.description = createGenericPageDto.description || '';
            genericPage.path = createGenericPageDto.path;
            genericPage.config = createGenericPageDto.config;
            genericPage.status = generic_page_enum_1.GenericPageStatusEnum.DRAFT;
            genericPage.createdAt = Date.now();
            genericPage.updatedAt = Date.now();
            genericPage.publishedAt = null;
            genericPage.createdBy = employeeId;
            genericPage.updatedBy = employeeId;
            const savedGenericPage = await this.genericPageRepository.save(genericPage);
            return this.mapToResponseDto(savedGenericPage);
        }
        catch (error) {
            if (error instanceof common_2.AppException) {
                throw error;
            }
            this.logger.error(`Error creating generic page: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_CREATE_ERROR, 'Lỗi khi tạo trang');
        }
    }
    async updateGenericPage(id, updateGenericPageDto, employeeId) {
        try {
            const genericPage = await this.genericPageRepository.findById(id);
            if (updateGenericPageDto.path && updateGenericPageDto.path !== genericPage.path) {
                const isPathExists = await this.genericPageRepository.isPathExists(updateGenericPageDto.path, id);
                if (isPathExists) {
                    throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_PATH_ALREADY_EXISTS, `Đường dẫn ${updateGenericPageDto.path} đã tồn tại`);
                }
            }
            if (updateGenericPageDto.name) {
                genericPage.name = updateGenericPageDto.name;
            }
            if (updateGenericPageDto.description !== undefined) {
                genericPage.description = updateGenericPageDto.description;
            }
            if (updateGenericPageDto.path) {
                genericPage.path = updateGenericPageDto.path;
            }
            if (updateGenericPageDto.config) {
                genericPage.config = updateGenericPageDto.config;
            }
            genericPage.updatedAt = Date.now();
            genericPage.updatedBy = employeeId;
            const savedGenericPage = await this.genericPageRepository.save(genericPage);
            return this.mapToResponseDto(savedGenericPage);
        }
        catch (error) {
            if (error instanceof common_2.AppException) {
                throw error;
            }
            this.logger.error(`Error updating generic page: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_UPDATE_ERROR, `Lỗi khi cập nhật trang với ID ${id}`);
        }
    }
    async getGenericPageById(id) {
        try {
            const genericPage = await this.genericPageRepository.findById(id);
            return this.mapToResponseDto(genericPage);
        }
        catch (error) {
            if (error instanceof common_2.AppException) {
                throw error;
            }
            this.logger.error(`Error getting generic page by ID: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_FOUND, `Lỗi khi lấy thông tin trang với ID ${id}`);
        }
    }
    async getGenericPageByPath(path) {
        try {
            const genericPage = await this.genericPageRepository.findByPath(path);
            return this.mapToResponseDto(genericPage);
        }
        catch (error) {
            if (error instanceof common_2.AppException) {
                throw error;
            }
            this.logger.error(`Error getting generic page by path: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_FOUND, `Lỗi khi lấy thông tin trang với đường dẫn ${path}`);
        }
    }
    async publishGenericPage(id, employeeId) {
        try {
            const genericPage = await this.genericPageRepository.findById(id);
            if (genericPage.status === generic_page_enum_1.GenericPageStatusEnum.PUBLISHED) {
                throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_ALREADY_PUBLISHED, 'Trang đã được xuất bản');
            }
            genericPage.status = generic_page_enum_1.GenericPageStatusEnum.PUBLISHED;
            genericPage.publishedAt = Date.now();
            genericPage.updatedAt = Date.now();
            genericPage.updatedBy = employeeId;
            const savedGenericPage = await this.genericPageRepository.save(genericPage);
            return this.mapToResponseDto(savedGenericPage);
        }
        catch (error) {
            if (error instanceof common_2.AppException) {
                throw error;
            }
            this.logger.error(`Error publishing generic page: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_UPDATE_ERROR, `Lỗi khi xuất bản trang với ID ${id}`);
        }
    }
    async unpublishGenericPage(id, employeeId) {
        try {
            const genericPage = await this.genericPageRepository.findById(id);
            if (genericPage.status !== generic_page_enum_1.GenericPageStatusEnum.PUBLISHED) {
                throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_PUBLISHED, 'Trang chưa được xuất bản');
            }
            genericPage.status = generic_page_enum_1.GenericPageStatusEnum.DRAFT;
            genericPage.updatedAt = Date.now();
            genericPage.updatedBy = employeeId;
            const savedGenericPage = await this.genericPageRepository.save(genericPage);
            return this.mapToResponseDto(savedGenericPage);
        }
        catch (error) {
            if (error instanceof common_2.AppException) {
                throw error;
            }
            this.logger.error(`Error unpublishing generic page: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_UPDATE_ERROR, `Lỗi khi hủy xuất bản trang với ID ${id}`);
        }
    }
    async deleteGenericPage(id) {
        try {
            const genericPage = await this.genericPageRepository.findById(id);
            await this.genericPageRepository.remove(genericPage);
        }
        catch (error) {
            if (error instanceof common_2.AppException) {
                throw error;
            }
            this.logger.error(`Error deleting generic page: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_DELETE_ERROR, `Lỗi khi xóa trang với ID ${id}`);
        }
    }
    mapToResponseDto(genericPage) {
        const responseDto = new dto_1.GenericPageResponseDto();
        responseDto.id = genericPage.id;
        responseDto.name = genericPage.name;
        responseDto.description = genericPage.description;
        responseDto.path = genericPage.path;
        responseDto.config = genericPage.config;
        responseDto.status = genericPage.status;
        responseDto.createdAt = genericPage.createdAt;
        responseDto.updatedAt = genericPage.updatedAt;
        responseDto.publishedAt = genericPage.publishedAt;
        responseDto.createdBy = genericPage.createdBy;
        responseDto.updatedBy = genericPage.updatedBy;
        return responseDto;
    }
};
exports.GenericPageAdminService = GenericPageAdminService;
exports.GenericPageAdminService = GenericPageAdminService = GenericPageAdminService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [generic_page_repository_1.GenericPageRepository])
], GenericPageAdminService);
//# sourceMappingURL=generic-page-admin.service.js.map