"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorCode = exports.AppException = void 0;
const common_1 = require("@nestjs/common");
class AppException extends common_1.HttpException {
    additionalData;
    errorCode;
    detail;
    constructor(errorCode, message, detail) {
        super({
            code: errorCode.code,
            message: message || errorCode.message,
            detail: detail,
        }, errorCode.status);
        this.errorCode = errorCode;
        this.detail = detail;
    }
    withData(data) {
        this.additionalData = data;
        return this;
    }
    getAdditionalData() {
        return this.additionalData;
    }
    getErrorCode() {
        return this.errorCode;
    }
}
exports.AppException = AppException;
class ErrorCode {
    code;
    message;
    status;
    constructor(code, message, status) {
        this.code = code;
        this.message = message;
        this.status = status;
    }
    static OPENAI_API_ERROR = new ErrorCode(10006, 'Lỗi khi gọi OpenAI API', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
}
exports.ErrorCode = ErrorCode;
//# sourceMappingURL=app.exception.js.map