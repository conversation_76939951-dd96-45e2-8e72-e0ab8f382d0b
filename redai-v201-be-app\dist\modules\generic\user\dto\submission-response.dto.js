"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubmissionResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const generic_page_enum_1 = require("../../constants/generic-page.enum");
class SubmissionResponseDto {
    id;
    pageId;
    status;
    createdAt;
    message;
}
exports.SubmissionResponseDto = SubmissionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của dữ liệu gửi',
        example: 'c7d8e9f0-a1b2-4c3d-9e0f-1a2b3c4d5e6f',
    }),
    __metadata("design:type", String)
], SubmissionResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của trang',
        example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    }),
    __metadata("design:type", String)
], SubmissionResponseDto.prototype, "pageId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái của dữ liệu gửi',
        enum: generic_page_enum_1.GenericPageSubmissionStatusEnum,
        example: generic_page_enum_1.GenericPageSubmissionStatusEnum.PENDING,
    }),
    __metadata("design:type", String)
], SubmissionResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời điểm gửi dữ liệu (Unix timestamp)',
        example: 1678439700000,
    }),
    __metadata("design:type", Number)
], SubmissionResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thông báo',
        example: 'Dữ liệu đã được gửi thành công',
    }),
    __metadata("design:type", String)
], SubmissionResponseDto.prototype, "message", void 0);
//# sourceMappingURL=submission-response.dto.js.map