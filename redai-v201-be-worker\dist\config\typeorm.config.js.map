{"version": 3, "sources": ["../../src/config/typeorm.config.ts"], "sourcesContent": ["import { DataSource, DataSourceOptions } from 'typeorm';\r\nimport { env } from './env';\r\n\r\n// <PERSON><PERSON><PERSON> hình DataSource cho TypeORM CLI\r\nexport const dataSourceOptions: DataSourceOptions = {\r\n  type: 'postgres',\r\n  host: env.database.DB_HOST,\r\n  port: Number(env.database.DB_PORT),\r\n  username: env.database.DB_USERNAME,\r\n  password: env.database.DB_PASSWORD,\r\n  database: env.database.DB_DATABASE,\r\n  entities: [__dirname + '/../**/*.entity{.ts,.js}'],\r\n  migrations: [__dirname + '/../modules/database/migrations/*{.ts,.js}'],\r\n  synchronize: false, // Không bật synchronize khi chạy migrations\r\n  logging: true,\r\n};\r\n\r\nconst dataSource = new DataSource(dataSourceOptions);\r\nexport default dataSource;\r\n"], "names": ["dataSourceOptions", "type", "host", "env", "database", "DB_HOST", "port", "Number", "DB_PORT", "username", "DB_USERNAME", "password", "DB_PASSWORD", "DB_DATABASE", "entities", "__dirname", "migrations", "synchronize", "logging", "dataSource", "DataSource"], "mappings": ";;;;;;;;;;;QAIaA;eAAAA;;QAcb;eAAA;;;yBAlB8C;qBAC1B;AAGb,MAAMA,oBAAuC;IAClDC,MAAM;IACNC,MAAMC,QAAG,CAACC,QAAQ,CAACC,OAAO;IAC1BC,MAAMC,OAAOJ,QAAG,CAACC,QAAQ,CAACI,OAAO;IACjCC,UAAUN,QAAG,CAACC,QAAQ,CAACM,WAAW;IAClCC,UAAUR,QAAG,CAACC,QAAQ,CAACQ,WAAW;IAClCR,UAAUD,QAAG,CAACC,QAAQ,CAACS,WAAW;IAClCC,UAAU;QAACC,YAAY;KAA2B;IAClDC,YAAY;QAACD,YAAY;KAA6C;IACtEE,aAAa;IACbC,SAAS;AACX;AAEA,MAAMC,aAAa,IAAIC,mBAAU,CAACpB;MAClC,WAAemB"}