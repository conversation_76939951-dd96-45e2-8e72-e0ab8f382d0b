{"version": 3, "sources": ["../../../src/agent/tools/tools-registry.ts"], "sourcesContent": ["import {\r\n  DynamicTool,\r\n  StructuredTool,\r\n  StructuredToolInterface,\r\n} from '@langchain/core/tools';\r\nimport { Runnable } from '@langchain/core/runnables';\r\nimport { mathTools } from './math-tools';\r\n\r\n/**\r\n * Registry for all available tools\r\n */\r\nclass ToolRegistry {\r\n  private tools: Map<string, StructuredToolInterface> = new Map();\r\n\r\n  constructor() {\r\n    // Register math tools\r\n    Object.entries(mathTools).forEach(([id, tool]) => {\r\n      this.registerTool(id, tool);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Register a tool with the registry\r\n   * @param id Tool identifier\r\n   * @param tool Tool instance\r\n   */\r\n  registerTool(id: string, tool: StructuredToolInterface): void {\r\n    this.tools.set(id, tool);\r\n  }\r\n\r\n  /**\r\n   * Get a tool by its ID\r\n   * @param id Tool identifier\r\n   * @returns The tool instance\r\n   */\r\n  getTool(id: string): StructuredToolInterface {\r\n    const tool = this.tools.get(id);\r\n    if (!tool) {\r\n      throw new Error(`Tool not found: ${id}`);\r\n    }\r\n    return tool;\r\n  }\r\n\r\n  /**\r\n   * Check if a tool exists in the registry\r\n   * @param id Tool identifier\r\n   * @returns True if the tool exists\r\n   */\r\n  hasTool(id: string): boolean {\r\n    return this.tools.has(id);\r\n  }\r\n\r\n  /**\r\n   * Get all registered tools\r\n   * @returns Array of all tool instances\r\n   */\r\n  getAllTools(): StructuredToolInterface[] {\r\n    return Array.from(this.tools.values());\r\n  }\r\n\r\n  /**\r\n   * Create tool instances from tool IDs\r\n   * @param toolIds Array of tool IDs\r\n   * @returns Array of tool instances\r\n   */\r\n  createToolsFromConfig(toolIds: string[]): StructuredToolInterface[] {\r\n    return toolIds\r\n      .filter((id) => this.hasTool(id))\r\n      .map((id) => this.getTool(id));\r\n  }\r\n}\r\n\r\n// Create and export a singleton instance\r\nexport const toolRegistry = new ToolRegistry();\r\n"], "names": ["toolRegistry", "ToolRegistry", "registerTool", "id", "tool", "tools", "set", "getTool", "get", "Error", "hasTool", "has", "getAllTools", "Array", "from", "values", "createToolsFromConfig", "toolIds", "filter", "map", "constructor", "Map", "Object", "entries", "mathTools", "for<PERSON>ach"], "mappings": ";;;;+BAyEaA;;;eAAAA;;;2BAnEa;AAE1B;;CAEC,GACD,IAAA,AAAMC,eAAN,MAAMA;IAUJ;;;;GAIC,GACDC,aAAaC,EAAU,EAAEC,IAA6B,EAAQ;QAC5D,IAAI,CAACC,KAAK,CAACC,GAAG,CAACH,IAAIC;IACrB;IAEA;;;;GAIC,GACDG,QAAQJ,EAAU,EAA2B;QAC3C,MAAMC,OAAO,IAAI,CAACC,KAAK,CAACG,GAAG,CAACL;QAC5B,IAAI,CAACC,MAAM;YACT,MAAM,IAAIK,MAAM,CAAC,gBAAgB,EAAEN,IAAI;QACzC;QACA,OAAOC;IACT;IAEA;;;;GAIC,GACDM,QAAQP,EAAU,EAAW;QAC3B,OAAO,IAAI,CAACE,KAAK,CAACM,GAAG,CAACR;IACxB;IAEA;;;GAGC,GACDS,cAAyC;QACvC,OAAOC,MAAMC,IAAI,CAAC,IAAI,CAACT,KAAK,CAACU,MAAM;IACrC;IAEA;;;;GAIC,GACDC,sBAAsBC,OAAiB,EAA6B;QAClE,OAAOA,QACJC,MAAM,CAAC,CAACf,KAAO,IAAI,CAACO,OAAO,CAACP,KAC5BgB,GAAG,CAAC,CAAChB,KAAO,IAAI,CAACI,OAAO,CAACJ;IAC9B;IAvDAiB,aAAc;aAFNf,QAA8C,IAAIgB;QAGxD,sBAAsB;QACtBC,OAAOC,OAAO,CAACC,oBAAS,EAAEC,OAAO,CAAC,CAAC,CAACtB,IAAIC,KAAK;YAC3C,IAAI,CAACF,YAAY,CAACC,IAAIC;QACxB;IACF;AAmDF;AAGO,MAAMJ,eAAe,IAAIC"}