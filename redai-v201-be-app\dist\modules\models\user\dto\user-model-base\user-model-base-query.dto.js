"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserModelBaseQueryDto = exports.UserModelBaseSortBy = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const dto_1 = require("../../../../../common/dto");
const type_provider_util_1 = require("../../../../../shared/services/ai/utils/type-provider.util");
var UserModelBaseSortBy;
(function (UserModelBaseSortBy) {
    UserModelBaseSortBy["NAME"] = "name";
    UserModelBaseSortBy["PROVIDER"] = "provider";
    UserModelBaseSortBy["STATUS"] = "status";
    UserModelBaseSortBy["CREATED_AT"] = "createdAt";
    UserModelBaseSortBy["UPDATED_AT"] = "updatedAt";
    UserModelBaseSortBy["INPUT_COST"] = "inputCostPer1kTokens";
    UserModelBaseSortBy["OUTPUT_COST"] = "outputCostPer1kTokens";
    UserModelBaseSortBy["CONTEXT_LENGTH"] = "contextLength";
})(UserModelBaseSortBy || (exports.UserModelBaseSortBy = UserModelBaseSortBy = {}));
class UserModelBaseQueryDto extends dto_1.QueryDto {
    name;
    modelId;
    provider;
    isUserAccessible;
    isFineTunable;
    source;
    keyId;
    capability;
    sortBy = UserModelBaseSortBy.NAME;
    sortDirection = dto_1.SortDirection.ASC;
}
exports.UserModelBaseQueryDto = UserModelBaseQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tìm kiếm theo tên model',
        example: 'gpt-4',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserModelBaseQueryDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tìm kiếm theo model ID',
        example: 'gpt-4-turbo',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserModelBaseQueryDto.prototype, "modelId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Lọc theo nhà cung cấp',
        enum: type_provider_util_1.ProviderEnumq,
        example: type_provider_util_1.ProviderEnumq.OPENAI,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(type_provider_util_1.ProviderEnumq),
    __metadata("design:type", String)
], UserModelBaseQueryDto.prototype, "provider", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Lọc theo khả năng user access',
        example: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UserModelBaseQueryDto.prototype, "isUserAccessible", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Lọc theo khả năng fine-tune',
        example: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UserModelBaseQueryDto.prototype, "isFineTunable", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Lọc theo source (admin hoặc user-key)',
        enum: ['admin', 'user-key'],
        example: 'admin',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserModelBaseQueryDto.prototype, "source", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID của user key (nếu lọc theo user-key)',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserModelBaseQueryDto.prototype, "keyId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Lọc theo capabilities (text-generation, image-generation, etc.)',
        example: 'text-generation',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserModelBaseQueryDto.prototype, "capability", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Sắp xếp theo trường',
        enum: UserModelBaseSortBy,
        example: UserModelBaseSortBy.NAME,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(UserModelBaseSortBy),
    __metadata("design:type", String)
], UserModelBaseQueryDto.prototype, "sortBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Hướng sắp xếp',
        enum: dto_1.SortDirection,
        example: dto_1.SortDirection.ASC,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(dto_1.SortDirection),
    __metadata("design:type", String)
], UserModelBaseQueryDto.prototype, "sortDirection", void 0);
//# sourceMappingURL=user-model-base-query.dto.js.map