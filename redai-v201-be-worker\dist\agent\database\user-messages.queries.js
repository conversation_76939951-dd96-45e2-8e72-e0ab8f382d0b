"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "UserMessagesQueries", {
    enumerable: true,
    get: function() {
        return UserMessagesQueries;
    }
});
const _common = require("@nestjs/common");
const _databaseservice = require("./database.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let UserMessagesQueries = class UserMessagesQueries {
    /**
   * Create a new user message
   * @param messageData Message data to create
   * @returns Promise<string> Created message ID
   */ async createMessage(messageData) {
        const query = `
      INSERT INTO user_messages (thread_id, role, content, timestamp, created_by)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING message_id
    `;
        const timestamp = Date.now();
        const values = [
            messageData.thread_id,
            messageData.role,
            JSON.stringify(messageData.content),
            timestamp,
            messageData.created_by
        ];
        try {
            this.logger.debug(`Creating message for thread ${messageData.thread_id}, role: ${messageData.role}`);
            const result = await this.databaseService.query(query, values);
            const messageId = result[0]?.message_id;
            if (!messageId) {
                throw new Error('Failed to create message - no ID returned');
            }
            this.logger.log(`Created message ${messageId} for thread ${messageData.thread_id}`);
            return messageId;
        } catch (error) {
            this.logger.error(`Failed to create message for thread ${messageData.thread_id}:`, error);
            throw new Error(`Message creation failed: ${error.message}`);
        }
    }
    /**
   * Get messages by thread ID
   * @param threadId Thread ID to get messages for
   * @param limit Optional limit on number of messages
   * @returns Promise<UserMessageData[]> Array of messages
   */ async getMessagesByThreadId(threadId, limit) {
        let query = `
      SELECT message_id, thread_id, role, content, timestamp, created_by
      FROM user_messages
      WHERE thread_id = $1
      ORDER BY timestamp ASC
    `;
        const values = [
            threadId
        ];
        if (limit) {
            query += ` LIMIT $2`;
            values.push(limit);
        }
        try {
            this.logger.debug(`Getting messages for thread ${threadId}${limit ? ` (limit: ${limit})` : ''}`);
            const result = await this.databaseService.query(query, values);
            const messages = result.map((row)=>({
                    message_id: row.message_id,
                    thread_id: row.thread_id,
                    role: row.role,
                    content: row.content,
                    timestamp: row.timestamp,
                    created_by: row.created_by
                }));
            this.logger.debug(`Found ${messages.length} messages for thread ${threadId}`);
            return messages;
        } catch (error) {
            this.logger.error(`Failed to get messages for thread ${threadId}:`, error);
            throw new Error(`Message retrieval failed: ${error.message}`);
        }
    }
    /**
   * Update message content by ID
   * @param messageId Message ID to update
   * @param content New content for the message
   * @returns Promise<boolean> True if update was successful
   */ async updateMessageContent(messageId, content) {
        const query = `
      UPDATE user_messages
      SET content = $1, timestamp = $2
      WHERE message_id = $3
    `;
        const timestamp = Date.now();
        const values = [
            JSON.stringify(content),
            timestamp,
            messageId
        ];
        try {
            this.logger.debug(`Updating message ${messageId}`);
            const result = await this.databaseService.query(query, values);
            this.logger.log(`Updated message ${messageId}`);
            return true;
        } catch (error) {
            this.logger.error(`Failed to update message ${messageId}:`, error);
            throw new Error(`Message update failed: ${error.message}`);
        }
    }
    /**
   * Delete message by ID
   * @param messageId Message ID to delete
   * @returns Promise<boolean> True if deletion was successful
   */ async deleteMessage(messageId) {
        const query = `
      DELETE FROM user_messages
      WHERE message_id = $1
    `;
        try {
            this.logger.debug(`Deleting message ${messageId}`);
            await this.databaseService.query(query, [
                messageId
            ]);
            this.logger.log(`Deleted message ${messageId}`);
            return true;
        } catch (error) {
            this.logger.error(`Failed to delete message ${messageId}:`, error);
            throw new Error(`Message deletion failed: ${error.message}`);
        }
    }
    /**
   * Delete messages by thread ID
   * @param threadId Thread ID to delete messages for
   * @returns Promise<boolean> True if deletion was successful
   */ async deleteMessagesByThreadId(threadId) {
        const query = `
      DELETE FROM user_messages
      WHERE thread_id = $1
    `;
        try {
            this.logger.debug(`Deleting messages for thread ${threadId}`);
            await this.databaseService.query(query, [
                threadId
            ]);
            this.logger.log(`Deleted messages for thread ${threadId}`);
            return true;
        } catch (error) {
            this.logger.error(`Failed to delete messages for thread ${threadId}:`, error);
            throw new Error(`Message deletion failed: ${error.message}`);
        }
    }
    constructor(databaseService){
        this.databaseService = databaseService;
        this.logger = new _common.Logger(UserMessagesQueries.name);
    }
};
UserMessagesQueries = _ts_decorate([
    (0, _common.Injectable)(),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _databaseservice.AgentDatabaseService === "undefined" ? Object : _databaseservice.AgentDatabaseService
    ])
], UserMessagesQueries);

//# sourceMappingURL=user-messages.queries.js.map