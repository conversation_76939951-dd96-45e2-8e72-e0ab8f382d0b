"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryGenericPageDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const query_dto_1 = require("../../../../common/dto/query.dto");
const generic_page_enum_1 = require("../../constants/generic-page.enum");
class QueryGenericPageDto extends query_dto_1.QueryDto {
    sortBy = generic_page_enum_1.GenericPageSortByEnum.CREATED_AT;
    status;
}
exports.QueryGenericPageDto = QueryGenericPageDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sắp xếp theo trường',
        enum: generic_page_enum_1.GenericPageSortByEnum,
        default: generic_page_enum_1.GenericPageSortByEnum.CREATED_AT,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(generic_page_enum_1.GenericPageSortByEnum, {
        message: `sortBy phải là một trong các giá trị: ${Object.values(generic_page_enum_1.GenericPageSortByEnum).join(', ')}`,
    }),
    __metadata("design:type", String)
], QueryGenericPageDto.prototype, "sortBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Lọc theo trạng thái',
        enum: generic_page_enum_1.GenericPageStatusEnum,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(generic_page_enum_1.GenericPageStatusEnum, {
        message: `status phải là một trong các giá trị: ${Object.values(generic_page_enum_1.GenericPageStatusEnum).join(', ')}`,
    }),
    __metadata("design:type", String)
], QueryGenericPageDto.prototype, "status", void 0);
//# sourceMappingURL=query-generic-page.dto.js.map