{"version": 3, "file": "product.mock.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/marketplace/admin/test/__mocks__/product.mock.ts"], "names": [], "mappings": ";;;AAAA,0CAA4E;AAO/D,QAAA,WAAW,GAAQ;IAC9B,EAAE,EAAE,CAAC;IACL,IAAI,EAAE,eAAe;IACrB,WAAW,EAAE,qBAAqB;IAClC,WAAW,EAAE,IAAI;IACjB,eAAe,EAAE,GAAG;IACpB,QAAQ,EAAE,uBAAe,CAAC,KAAK;IAC/B,MAAM,EAAE,qBAAa,CAAC,QAAQ;IAC9B,MAAM,EAAE,CAAC;IACT,UAAU,EAAE,IAAI;IAChB,SAAS,EAAE,CAAC;IACZ,gBAAgB,EAAE,YAAY;IAC9B,YAAY,EAAE,kBAAkB;IAChC,QAAQ,EAAE,EAAS;IACnB,MAAM,EAAE;QACN,EAAE,GAAG,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC,EAAE;QAClC,EAAE,GAAG,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC,EAAE;KACnC;IACD,UAAU,EAAE,YAAY;IACxB,MAAM,EAAE,YAAY;IACpB,SAAS,EAAE,aAAa;IACxB,SAAS,EAAE,aAAa;IAExB,QAAQ,EAAE,YAAY;CACvB,CAAC;AAKW,QAAA,gBAAgB,GAAQ;IACnC,EAAE,EAAE,CAAC;IACL,IAAI,EAAE,gBAAgB;IACtB,WAAW,EAAE,sBAAsB;IACnC,WAAW,EAAE,IAAI;IACjB,eAAe,EAAE,IAAI;IACrB,QAAQ,EAAE,uBAAe,CAAC,QAAQ;IAClC,MAAM,EAAE,qBAAa,CAAC,QAAQ;IAC9B,MAAM,EAAE,IAAI;IACZ,UAAU,EAAE,CAAC;IACb,SAAS,EAAE,IAAI;IACf,aAAa,EAAE,CAAC;IAChB,oBAAoB,EAAE,OAAO;IAC7B,gBAAgB,EAAE,mBAAmB;IACrC,MAAM,EAAE;QACN,EAAE,GAAG,EAAE,kBAAkB,EAAE,QAAQ,EAAE,CAAC,EAAE;KACzC;IACD,UAAU,EAAE,kBAAkB;IAC9B,MAAM,EAAE,kBAAkB;IAC1B,SAAS,EAAE,aAAa;IACxB,SAAS,EAAE,aAAa;IAExB,QAAQ,EAAE,kBAAkB;CAC7B,CAAC;AAKW,QAAA,YAAY,GAAU;IACjC,mBAAW;IACX,wBAAgB;CACjB,CAAC;AAKW,QAAA,sBAAsB,GAAuB;IACxD,EAAE,EAAE,CAAC;IACL,IAAI,EAAE,eAAe;IACrB,WAAW,EAAE,qBAAqB;IAClC,WAAW,EAAE,IAAI;IACjB,eAAe,EAAE,GAAG;IACpB,QAAQ,EAAE,uBAAe,CAAC,KAAK;IAC/B,MAAM,EAAE,qBAAa,CAAC,QAAQ;IAC9B,MAAM,EAAE;QACN,EAAE,EAAE,CAAC;QACL,IAAI,EAAE,YAAY;QAClB,MAAM,EAAE,IAAI;QACZ,IAAI,EAAE,MAAM;KACb;IACD,MAAM,EAAE,CAAC,gCAAgC,CAAC;IAC1C,SAAS,EAAE,aAAa;IACxB,SAAS,EAAE,aAAa;IACxB,SAAS,EAAE,EAAE;IACb,WAAW,EAAE,IAAI;CAClB,CAAC;AAKW,QAAA,4BAA4B,GAA6B;IACpE,EAAE,EAAE,CAAC;IACL,IAAI,EAAE,eAAe;IACrB,WAAW,EAAE,qBAAqB;IAClC,WAAW,EAAE,IAAI;IACjB,eAAe,EAAE,GAAG;IACpB,QAAQ,EAAE,uBAAe,CAAC,KAAK;IAC/B,MAAM,EAAE,qBAAa,CAAC,QAAQ;IAC9B,MAAM,EAAE;QACN,EAAE,EAAE,CAAC;QACL,IAAI,EAAE,YAAY;QAClB,MAAM,EAAE,IAAI;QACZ,IAAI,EAAE,MAAM;KACb;IACD,MAAM,EAAE;QACN,gCAAgC;QAChC,gCAAgC;KACjC;IACD,UAAU,EAAE,gCAAgC;IAC5C,MAAM,EAAE,gCAAgC;IACxC,SAAS,EAAE,aAAa;IACxB,SAAS,EAAE,aAAa;IACxB,QAAQ,EAAE,YAAY;IACtB,SAAS,EAAE,EAAE;IACb,WAAW,EAAE,IAAI;CAClB,CAAC;AAKW,QAAA,+BAA+B,GAAwC;IAClF,KAAK,EAAE,CAAC,8BAAsB,CAAC;IAC/B,IAAI,EAAE;QACJ,UAAU,EAAE,CAAC;QACb,SAAS,EAAE,CAAC;QACZ,YAAY,EAAE,EAAE;QAChB,UAAU,EAAE,CAAC;QACb,WAAW,EAAE,CAAC;KACf;CACF,CAAC;AAKW,QAAA,uBAAuB,GAAG;IACrC,OAAO,EAAE,oCAA4B;IACrC,UAAU,EAAE;QACV,SAAS,EAAE,GAAG;QACd,gBAAgB,EAAE;YAChB;gBACE,GAAG,EAAE,uCAAuC;gBAC5C,GAAG,EAAE,0DAA0D;gBAC/D,KAAK,EAAE,CAAC;aACT;SACF;QACD,mBAAmB,EAAE,uCAAuC;QAC5D,eAAe,EAAE,uCAAuC;KACzD;CACF,CAAC"}