"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _common = require("@nestjs/common");
const _core = require("@nestjs/core");
const _appmodule = require("./app.module");
const _microservices = require("@nestjs/microservices");
const logger = new _common.Logger('Bootstrap');
async function bootstrap() {
    const app = await _core.NestFactory.create(_appmodule.AppModule);
    if (!process.env.REDIS_URL) {
        logger.error('REDIS_URL environment variable is not set.');
        process.exit(1);
    }
    const redisUrl = new URL(process.env.REDIS_URL);
    // Configure the microservice options for Redis
    app.connectMicroservice({
        transport: _microservices.Transport.REDIS,
        options: {
            host: redisUrl.hostname,
            port: parseInt(redisUrl.port, 10),
            password: redisUrl.password,
            retryAttempts: 5,
            retryDelay: 2000,
            tls: redisUrl.protocol === 'rediss:' ? {
                rejectUnauthorized: false
            } : undefined
        }
    });
    // Start the microservice
    await app.startAllMicroservices();
    const port = process.env.PORT ?? 3000;
    await app.listen(port);
    logger.log(`Application is running on: http://localhost:${port}`);
    logger.log(`Swagger documentation is available at: http://localhost:${port}/api/docs`);
}
bootstrap().catch((err)=>{
    logger.error('Failed to start application', err);
    process.exit(1);
});

//# sourceMappingURL=main.js.map