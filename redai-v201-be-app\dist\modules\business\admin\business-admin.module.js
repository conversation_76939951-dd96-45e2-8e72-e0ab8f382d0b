"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessAdminModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const entities_1 = require("../entities");
const controllers_1 = require("./controllers");
const services_1 = require("./services");
const helpers_1 = require("./helpers");
const repositories_1 = require("../repositories");
let BusinessAdminModule = class BusinessAdminModule {
};
exports.BusinessAdminModule = BusinessAdminModule;
exports.BusinessAdminModule = BusinessAdminModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                entities_1.UserProduct,
                entities_1.UserClassification,
                entities_1.CustomField,
                entities_1.UserOrder,
                entities_1.Inventory,
                entities_1.UserConvert,
                entities_1.UserConvertCustomer,
                entities_1.Warehouse,
                entities_1.PhysicalWarehouse,
                entities_1.VirtualWarehouse,
                entities_1.File,
                entities_1.Folder
            ])
        ],
        controllers: [
            controllers_1.ProductAdminController,
            controllers_1.CustomFieldAdminController,
            controllers_1.UserConvertAdminController,
            controllers_1.UserConvertCustomerAdminController,
            controllers_1.UserOrderAdminController,
            controllers_1.AdminWarehouseController,
            controllers_1.AdminWarehouseCustomFieldController,
            controllers_1.AdminVirtualWarehouseController,
            controllers_1.AdminPhysicalWarehouseController,
            controllers_1.AdminFileController,
            controllers_1.AdminFolderController
        ],
        providers: [
            helpers_1.ValidationHelper,
            helpers_1.WarehouseValidationHelper,
            helpers_1.FileValidationHelper,
            helpers_1.FolderValidationHelper,
            helpers_1.FileHelper,
            helpers_1.FolderHelper,
            services_1.ProductAdminService,
            services_1.CustomFieldAdminService,
            services_1.UserConvertAdminService,
            services_1.UserConvertCustomerAdminService,
            services_1.UserOrderAdminService,
            services_1.AdminWarehouseService,
            services_1.AdminWarehouseCustomFieldService,
            services_1.AdminVirtualWarehouseService,
            services_1.AdminPhysicalWarehouseService,
            services_1.AdminFileService,
            services_1.AdminFolderService,
            repositories_1.UserProductAdminRepository,
            repositories_1.CustomFieldRepository,
            repositories_1.UserClassificationRepository,
            repositories_1.UserConvertRepository,
            repositories_1.UserConvertCustomerRepository,
            repositories_1.UserOrderRepository,
            repositories_1.WarehouseRepository,
            repositories_1.PhysicalWarehouseRepository,
            repositories_1.VirtualWarehouseRepository,
            repositories_1.FileRepository,
            repositories_1.FolderRepository,
            helpers_1.ValidationHelper
        ],
        exports: [
            typeorm_1.TypeOrmModule,
            services_1.ProductAdminService,
            services_1.CustomFieldAdminService,
            services_1.UserConvertAdminService,
            services_1.UserConvertCustomerAdminService,
            services_1.UserOrderAdminService,
            services_1.AdminWarehouseService,
            services_1.AdminWarehouseCustomFieldService,
            services_1.AdminVirtualWarehouseService,
            services_1.AdminPhysicalWarehouseService,
            services_1.AdminFileService,
            services_1.AdminFolderService
        ],
    })
], BusinessAdminModule);
//# sourceMappingURL=business-admin.module.js.map