"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericPageTemplateTag = void 0;
const typeorm_1 = require("typeorm");
const generic_page_template_entity_1 = require("./generic-page-template.entity");
let GenericPageTemplateTag = class GenericPageTemplateTag {
    templateId;
    tag;
    template;
};
exports.GenericPageTemplateTag = GenericPageTemplateTag;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ name: 'template_id', length: 36 }),
    __metadata("design:type", String)
], GenericPageTemplateTag.prototype, "templateId", void 0);
__decorate([
    (0, typeorm_1.PrimaryColumn)({ length: 50 }),
    __metadata("design:type", String)
], GenericPageTemplateTag.prototype, "tag", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => generic_page_template_entity_1.GenericPageTemplate, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'template_id' }),
    __metadata("design:type", generic_page_template_entity_1.GenericPageTemplate)
], GenericPageTemplateTag.prototype, "template", void 0);
exports.GenericPageTemplateTag = GenericPageTemplateTag = __decorate([
    (0, typeorm_1.Entity)('generic_page_template_tags')
], GenericPageTemplateTag);
//# sourceMappingURL=generic-page-template-tag.entity.js.map