{"version": 3, "file": "agent-tools.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/tools-build-in/services/agent-tools.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,qCAAqC;AACrC,2DAAkD;AAClD,8CAA2D;AAC3D,gCAA4H;AAMrH,IAAM,iBAAiB,yBAAvB,MAAM,iBAAiB;IAGC;IAFZ,MAAM,GAAG,IAAI,eAAM,CAAC,mBAAiB,CAAC,IAAI,CAAC,CAAC;IAE7D,YAA6B,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IAAI,CAAC;IAOxD,KAAK,CAAC,aAAa,CAAC,OAAe;QACjC,IAAI,CAAC;YAEH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,UAAU;iBAChC,kBAAkB,EAAE;iBACpB,MAAM,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;iBACtB,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC;iBACvB,KAAK,CAAC,qBAAqB,EAAE,EAAE,OAAO,EAAE,CAAC;iBACzC,SAAS,EAAE,CAAC;YAEf,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,yBAAY,CACpB,uCAA0B,CAAC,eAAe,EAC1C,+BAA+B,OAAO,EAAE,CACzC,CAAC;YACJ,CAAC;YAGD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,UAAU;iBACpC,kBAAkB,EAAE;iBACpB,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC;iBACnB,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC;iBACjC,KAAK,CAAC,0BAA0B,EAAE,EAAE,OAAO,EAAE,CAAC;iBAC9C,SAAS,EAAE,CAAC;YAEf,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;gBACrC,MAAM,IAAI,yBAAY,CACpB,uCAA0B,CAAC,oBAAoB,EAC/C,2BAA2B,CAC5B,CAAC;YACJ,CAAC;YAGD,MAAM,KAAK,GAAsB,EAAE,CAAC;YACpC,MAAM,cAAc,GAAyB,EAAE,CAAC;YAGhD,MAAM,YAAY,GAAG,IAAI,CAAC;YAE1B,IAAI,YAAY,EAAE,CAAC;gBACjB,OAAO;oBACL,KAAK,EAAE,cAAc;iBACtB,CAAC;YACJ,CAAC;YAGD,OAAO;gBACL,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,SAAS,EAAE,KAAK,CAAC,IAAI;gBACrB,KAAK,EAAE,KAAK;gBACZ,UAAU,EAAE,CAAC;aACd,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,yBAAY,EAAE,CAAC;gBAClC,MAAM,KAAK,CAAC;YACd,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,yCAAyC,KAAK,CAAC,OAAO,EAAE,EACxD,KAAK,CAAC,KAAK,CACZ,CAAC;YACF,MAAM,IAAI,yBAAY,CACpB,uCAA0B,CAAC,wBAAwB,EACnD,yCAAyC,KAAK,CAAC,OAAO,EAAE,CACzD,CAAC;QACJ,CAAC;IACH,CAAC;IAOO,KAAK,CAAC,qBAAqB,CACjC,WAAmB;QAEnB,IAAI,CAAC;YAEH,OAAO,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACxE,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAOO,KAAK,CAAC,iBAAiB,CAAC,OAAe;QAC7C,IAAI,CAAC;YAEH,OAAO,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,0CAA0C,KAAK,CAAC,OAAO,EAAE,EACzD,KAAK,CAAC,KAAK,CACZ,CAAC;YACF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IASO,KAAK,CAAC,oBAAoB,CAAC,MAAc;QAC/C,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,UAAU;iBACzB,kBAAkB,EAAE;iBACpB,MAAM,CAAC;gBACN,IAAI;gBACJ,SAAS;gBACT,gBAAgB;gBAChB,WAAW;gBACX,kBAAkB;gBAClB,YAAY;gBACZ,QAAQ;aACT,CAAC;iBACD,IAAI,CAAC,qBAAqB,EAAE,KAAK,CAAC;iBAClC,KAAK,CAAC,uBAAuB,EAAE,EAAE,MAAM,EAAE,CAAC;iBAC1C,QAAQ,CAAC,uBAAuB,EAAE,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;iBAC3D,OAAO,CAAC,oBAAoB,EAAE,MAAM,CAAC;iBACrC,KAAK,CAAC,CAAC,CAAC;iBACR,SAAS,EAAE,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,4CAA4C,KAAK,CAAC,OAAO,EAAE,EAC3D,KAAK,CAAC,KAAK,CACZ,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IASO,KAAK,CAAC,kBAAkB,CAAC,KAAwB;QACvD,MAAM,cAAc,GAAyB,EAAE,CAAC;QAGhD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC;gBAEH,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI;oBACpC,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE,EAAE;oBACd,QAAQ,EAAE,EAAE;iBACb,CAAC;gBAGF,IAAI,OAA2B,CAAC;gBAGhC,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;oBAEzB,OAAO,GAAG;wBACR,SAAS,EAAE;4BACT,IAAI,EAAE,IAAI,CAAC,IAAI;4BACf,WAAW,EAAE,IAAI,CAAC,WAAW;4BAC7B,WAAW,EAAE,IAAI,CAAC,8BAA8B,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC;yBACxE;wBACD,KAAK,EAAE;4BACL,GAAG,EAAE,IAAI,CAAC,QAAQ,IAAI,EAAE;4BACxB,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,KAAK;yBAC7B;wBACD,QAAQ,EAAE,kBAAY,CAAC,SAAS;qBACjC,CAAC;gBACJ,CAAC;qBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAElC,MAAM,KAAK,GAAQ;wBACjB,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;wBAC1B,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;qBACjC,CAAC;oBAGF,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;wBAE3E,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC;wBAG1D,IAAI,UAAU,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;4BAC3C,KAAK,CAAC,OAAO,GAAG;gCACd,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;gCACxB,CAAC,SAAS,CAAC,EAAE,GAAG,MAAM,EAAE;6BACzB,CAAC;4BACF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,UAAU,IAAI,MAAM,EAAE,CAAC,CAAC;wBAC3E,CAAC;6BAAM,CAAC;4BAEN,KAAK,CAAC,OAAO,GAAG;gCACd,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;gCACxB,CAAC,SAAS,CAAC,EAAE,MAAM;6BACpB,CAAC;4BACF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,MAAM,EAAE,CAAC,CAAC;wBAClE,CAAC;oBACH,CAAC;oBAED,OAAO,GAAG;wBACR,SAAS,EAAE;4BACT,IAAI,EAAE,IAAI,CAAC,IAAI;4BACf,WAAW,EAAE,IAAI,CAAC,WAAW;4BAC7B,WAAW,EAAE,IAAI,CAAC,8BAA8B,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC;yBACxE;wBACD,KAAK,EAAE,KAAK;wBACZ,QAAQ,EAAE,kBAAY,CAAC,UAAU;qBAClC,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBAEN,OAAO,GAAG;wBACR,SAAS,EAAE;4BACT,IAAI,EAAE,IAAI,CAAC,IAAI;4BACf,WAAW,EAAE,IAAI,CAAC,WAAW;4BAC7B,WAAW,EAAE,IAAI,CAAC,8BAA8B,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC;yBACxE;wBACD,KAAK,EAAE;4BACL,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;4BAC1B,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;yBACjC;wBACD,QAAQ,EAAE,kBAAY,CAAC,SAAS;qBACjC,CAAC;gBACJ,CAAC;gBAED,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC/B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,IAAI,CAAC,EAAE,wBAAwB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAE7F,SAAS;YACX,CAAC;QACH,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAQO,8BAA8B,CAAC,UAAe,EAAE,SAAiB;QACvE,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE,EAAE;gBACd,QAAQ,EAAE,EAAE;aACb,CAAC;QACJ,CAAC;QAGD,MAAM,WAAW,GAAG;YAClB,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE,UAAU,CAAC,QAAQ,IAAI,EAAE;YACnC,UAAU,EAAE;gBACV,WAAW,EAAE;oBACX,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,kBAAkB;oBAC/B,UAAU,EAAE,EAAE;iBACf;gBACD,UAAU,EAAE;oBACV,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,mBAAmB;oBAChC,UAAU,EAAE,EAAE;iBACf;gBACD,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,cAAc;oBAC3B,UAAU,EAAE,EAAE;iBACf;aACF;YACD,WAAW,EAAE,UAAU,CAAC,WAAW,IAAI,6BAA6B;SACrE,CAAC;QAGF,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;YAE1B,IAAI,UAAU,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;gBACtC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,0BAA0B,CAAC,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YACrH,CAAC;YAGD,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;gBACrC,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC,0BAA0B,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YACnH,CAAC;YAGD,IAAI,UAAU,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;gBAC/B,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,0BAA0B,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACvG,CAAC;QACH,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAOO,0BAA0B,CAAC,eAAoB;QACrD,MAAM,MAAM,GAAG,EAAE,CAAC;QAElB,IAAI,CAAC,eAAe;YAAE,OAAO,MAAM,CAAC;QAGpC,IAAI,eAAe,CAAC,UAAU,EAAE,CAAC;YAC/B,OAAO,eAAe,CAAC,UAAU,CAAC;QACpC,CAAC;QAGD,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACzC,MAAM,QAAQ,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;YAEtC,MAAM,CAAC,GAAG,CAAC,GAAG;gBACZ,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,QAAQ;gBAC/B,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,EAAE;aACxC,CAAC;YAGF,IAAI,QAAQ,CAAC,IAAI,KAAK,OAAO,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;gBAChD,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;YACrC,CAAC;YAGD,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;YACzC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAOO,UAAU,CAAC,IAAqB;QAEtC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;QACnE,CAAC;QAGD,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC,QAAQ,CAAC;QACvB,CAAC;QAGD,OAAO,oCAAoC,CAAC;IAC9C,CAAC;IAOO,aAAa,CAAC,IAAqB;QAEzC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QACjC,CAAC;QAGD,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;QAGD,OAAO,KAAK,CAAC;IACf,CAAC;IAOO,wBAAwB,CAAC,GAAW;QAC1C,MAAM,UAAU,GAAa,EAAE,CAAC;QAChC,MAAM,KAAK,GAAG,YAAY,CAAC;QAC3B,IAAI,KAA6B,CAAC;QAElC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YAC1C,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;CACF,CAAA;AApZY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;qCAI8B,oBAAU;GAHxC,iBAAiB,CAoZ7B"}