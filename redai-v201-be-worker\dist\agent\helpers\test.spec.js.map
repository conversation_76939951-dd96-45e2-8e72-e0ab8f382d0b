{"version": 3, "sources": ["../../../src/agent/helpers/test.spec.ts"], "sourcesContent": ["// src/modules/worker/helpers/cleanModelConfig.spec.ts\r\n\r\nimport { cleanModelConfig } from './model-resolver';\r\nimport { ModelFeature } from '../enums';\r\nimport type { AgentConfig } from '../interfaces';\r\n\r\ntype ModelConfig = AgentConfig['model'];\r\n\r\ndescribe('cleanModelConfig()', () => {\r\n  const baseConfig = (overrides: Partial<ModelConfig> = {}): ModelConfig => ({\r\n    name: 'test-model',\r\n    provider: 'OPENAI' as any,\r\n    capabilities: [],\r\n    parameters: { topK: 5, topP: 0.9, temperature: 0.7 },\r\n    type: 'OUT_SYSTEM',\r\n    ...overrides,\r\n  });\r\n\r\n  it('returns empty object when no MODIFY_* capabilities are present', () => {\r\n    const cfg = baseConfig({ capabilities: [] });\r\n    const cleaned = cleanModelConfig(cfg);\r\n    expect(cleaned.parameters).toEqual({});\r\n  });\r\n\r\n  it('keeps topK only when MODIFY_TOP_K is present', () => {\r\n    const cfg = baseConfig({\r\n      capabilities: [ModelFeature.MODIFY_TOP_K],\r\n    });\r\n    const cleaned = cleanModelConfig(cfg);\r\n    expect(cleaned.parameters).toEqual({ topK: 5 });\r\n  });\r\n\r\n  it('keeps topP only when MODIFY_TOP_P is present', () => {\r\n    const cfg = baseConfig({\r\n      capabilities: [ModelFeature.MODIFY_TOP_P],\r\n    });\r\n    const cleaned = cleanModelConfig(cfg);\r\n    expect(cleaned.parameters).toEqual({ topP: 0.9 });\r\n  });\r\n\r\n  it('keeps temperature only when MODIFY_TEMPERATURE is present', () => {\r\n    const cfg = baseConfig({\r\n      capabilities: [ModelFeature.MODIFY_TEMPERATURE],\r\n    });\r\n    const cleaned = cleanModelConfig(cfg);\r\n    expect(cleaned.parameters).toEqual({ temperature: 0.7 });\r\n  });\r\n\r\n  it('keeps multiple when multiple capabilities are present', () => {\r\n    const cfg = baseConfig({\r\n      capabilities: [\r\n        ModelFeature.MODIFY_TOP_K,\r\n        ModelFeature.MODIFY_TOP_P,\r\n      ],\r\n    });\r\n    const cleaned = cleanModelConfig(cfg);\r\n    expect(cleaned.parameters).toEqual({ topK: 5, topP: 0.9 });\r\n  });\r\n\r\n  it('initializes empty object even if original parameters was undefined', () => {\r\n    const cfg = baseConfig({ parameters: undefined });\r\n    const cleaned = cleanModelConfig(cfg);\r\n    expect(cleaned.parameters).toEqual({});\r\n  });\r\n});\r\n"], "names": ["describe", "baseConfig", "overrides", "name", "provider", "capabilities", "parameters", "topK", "topP", "temperature", "type", "it", "cfg", "cleaned", "cleanModelConfig", "expect", "toEqual", "ModelFeature", "MODIFY_TOP_K", "MODIFY_TOP_P", "MODIFY_TEMPERATURE", "undefined"], "mappings": "AAAA,sDAAsD;;;;;+BAErB;uBACJ;AAK7BA,SAAS,sBAAsB;IAC7B,MAAMC,aAAa,CAACC,YAAkC,CAAC,CAAC,GAAmB,CAAA;YACzEC,MAAM;YACNC,UAAU;YACVC,cAAc,EAAE;YAChBC,YAAY;gBAAEC,MAAM;gBAAGC,MAAM;gBAAKC,aAAa;YAAI;YACnDC,MAAM;YACN,GAAGR,SAAS;QACd,CAAA;IAEAS,GAAG,kEAAkE;QACnE,MAAMC,MAAMX,WAAW;YAAEI,cAAc,EAAE;QAAC;QAC1C,MAAMQ,UAAUC,IAAAA,+BAAgB,EAACF;QACjCG,OAAOF,QAAQP,UAAU,EAAEU,OAAO,CAAC,CAAC;IACtC;IAEAL,GAAG,gDAAgD;QACjD,MAAMC,MAAMX,WAAW;YACrBI,cAAc;gBAACY,mBAAY,CAACC,YAAY;aAAC;QAC3C;QACA,MAAML,UAAUC,IAAAA,+BAAgB,EAACF;QACjCG,OAAOF,QAAQP,UAAU,EAAEU,OAAO,CAAC;YAAET,MAAM;QAAE;IAC/C;IAEAI,GAAG,gDAAgD;QACjD,MAAMC,MAAMX,WAAW;YACrBI,cAAc;gBAACY,mBAAY,CAACE,YAAY;aAAC;QAC3C;QACA,MAAMN,UAAUC,IAAAA,+BAAgB,EAACF;QACjCG,OAAOF,QAAQP,UAAU,EAAEU,OAAO,CAAC;YAAER,MAAM;QAAI;IACjD;IAEAG,GAAG,6DAA6D;QAC9D,MAAMC,MAAMX,WAAW;YACrBI,cAAc;gBAACY,mBAAY,CAACG,kBAAkB;aAAC;QACjD;QACA,MAAMP,UAAUC,IAAAA,+BAAgB,EAACF;QACjCG,OAAOF,QAAQP,UAAU,EAAEU,OAAO,CAAC;YAAEP,aAAa;QAAI;IACxD;IAEAE,GAAG,yDAAyD;QAC1D,MAAMC,MAAMX,WAAW;YACrBI,cAAc;gBACZY,mBAAY,CAACC,YAAY;gBACzBD,mBAAY,CAACE,YAAY;aAC1B;QACH;QACA,MAAMN,UAAUC,IAAAA,+BAAgB,EAACF;QACjCG,OAAOF,QAAQP,UAAU,EAAEU,OAAO,CAAC;YAAET,MAAM;YAAGC,MAAM;QAAI;IAC1D;IAEAG,GAAG,sEAAsE;QACvE,MAAMC,MAAMX,WAAW;YAAEK,YAAYe;QAAU;QAC/C,MAAMR,UAAUC,IAAAA,+BAAgB,EAACF;QACjCG,OAAOF,QAAQP,UAAU,EAAEU,OAAO,CAAC,CAAC;IACtC;AACF"}