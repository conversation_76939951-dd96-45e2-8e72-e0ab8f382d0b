"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const pdf_edit_service_1 = require("../pdf/pdf-edit.service");
const fs = require("fs");
const path = require("path");
async function exampleUsage() {
    const pdfEditService = new pdf_edit_service_1.PdfEditService();
    try {
        const pdfPath = path.join(__dirname, '../../../test-files/sample.pdf');
        const pdfBytes = fs.readFileSync(pdfPath);
        const positions = [
            {
                pageIndex: 0,
                text: 'Văn bản thêm vào trang 1',
                xMm: 50,
                yMm: 50,
                size: 12,
            },
            {
                pageIndex: 0,
                text: 'Văn bản căn giữa',
                xMm: 100,
                yMm: 100,
                size: 16,
                isCenter: true,
            },
            {
                pageIndex: 1,
                text: '<PERSON><PERSON><PERSON> bản thêm vào trang 2',
                xMm: 50,
                yMm: 50,
                size: 14,
            },
        ];
        const result = await pdfEditService.editPdf(pdfBytes, positions);
        const outputPath = path.join(__dirname, '../../../test-files/output.pdf');
        fs.writeFileSync(outputPath, result.pdfBuffer);
        console.log('Đã lưu file PDF đã chỉnh sửa tại:', outputPath);
        console.log('Base64 của file PDF:', result.pdfBase64?.substring(0, 100) + '...');
        const signaturePath = path.join(__dirname, '../../../test-files/signature.png');
        const signatureBytes = fs.readFileSync(signaturePath);
        const signatureBase64 = signatureBytes.toString('base64');
        const positionsWithSignature = [
            {
                pageIndex: 0,
                text: 'Chữ ký của tôi:',
                xMm: 50,
                yMm: 150,
                size: 12,
            },
            {
                pageIndex: 0,
                signatureBase64: signatureBase64,
                xMm: 50,
                yMm: 170,
                signatureWidthMm: 50,
                signatureHeightMm: 20,
            },
        ];
        const resultWithSignature = await pdfEditService.editPdf(pdfBytes, positionsWithSignature);
        const outputSignaturePath = path.join(__dirname, '../../../test-files/output-with-signature.pdf');
        fs.writeFileSync(outputSignaturePath, resultWithSignature.pdfBuffer);
        console.log('Đã lưu file PDF với chữ ký tại:', outputSignaturePath);
    }
    catch (error) {
        console.error('Lỗi khi chạy ví dụ:', error);
    }
}
//# sourceMappingURL=pdf-edit.service.example.js.map