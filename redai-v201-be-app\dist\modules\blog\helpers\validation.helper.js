"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationHelper = void 0;
const common_1 = require("@nestjs/common");
const common_2 = require("../../../common");
const exceptions_1 = require("../exceptions");
const enums_1 = require("../enums");
let ValidationHelper = class ValidationHelper {
    validateBlogExists(blog) {
        if (!blog) {
            throw new common_2.AppException(exceptions_1.BLOG_ERROR_CODE.BLOG_NOT_FOUND);
        }
        if (!blog.enable) {
            throw new common_2.AppException(exceptions_1.BLOG_ERROR_CODE.BLOG_ACCESS_DENIED);
        }
        if (blog.status !== enums_1.BlogStatusEnum.APPROVED) {
            throw new common_2.AppException(exceptions_1.BLOG_ERROR_CODE.BLOG_INVALID_STATUS);
        }
    }
    validateUserExists(user) {
        if (!user) {
            throw new common_2.AppException(exceptions_1.BLOG_ERROR_CODE.BLOG_ACCESS_DENIED, 'Người dùng không tồn tại hoặc không hoạt động');
        }
    }
    validateNotAlreadyPurchased(hasPurchased) {
        if (hasPurchased) {
            throw new common_2.AppException(exceptions_1.BLOG_ERROR_CODE.BLOG_ALREADY_PURCHASED);
        }
    }
    validateSufficientPoints(userPoints, blogPoints) {
        if (userPoints < blogPoints) {
            throw new common_2.AppException(exceptions_1.BLOG_ERROR_CODE.BLOG_INSUFFICIENT_POINTS);
        }
    }
    validateNotOwnBlog(blogUserId, userId) {
        if (blogUserId === userId) {
            throw new common_2.AppException(exceptions_1.BLOG_ERROR_CODE.BLOG_CANNOT_PURCHASE_OWN);
        }
    }
    validatePurchaseExists(purchase) {
        if (!purchase) {
            throw new common_2.AppException(exceptions_1.BLOG_ERROR_CODE.BLOG_PURCHASE_NOT_FOUND);
        }
    }
    validatePointValues(userPointsBalance, blogPoint) {
        let userPoints = 0;
        let blogPoints = 0;
        try {
            userPoints = userPointsBalance ? Number(userPointsBalance) : 0;
            blogPoints = blogPoint ? Number(blogPoint) : 0;
            if (isNaN(userPoints) || isNaN(blogPoints)) {
                throw new Error('Giá trị điểm không hợp lệ');
            }
            return { userPoints, blogPoints };
        }
        catch (err) {
            throw new common_2.AppException(exceptions_1.BLOG_ERROR_CODE.BLOG_PURCHASE_FAILED, 'Giá trị điểm không hợp lệ');
        }
    }
};
exports.ValidationHelper = ValidationHelper;
exports.ValidationHelper = ValidationHelper = __decorate([
    (0, common_1.Injectable)()
], ValidationHelper);
//# sourceMappingURL=validation.helper.js.map