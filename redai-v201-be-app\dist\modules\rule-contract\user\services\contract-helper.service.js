"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ContractHelperService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContractHelperService = void 0;
const common_1 = require("@nestjs/common");
const contract_template_service_1 = require("../../../system-configuration/services/contract-template.service");
const pdf_edit_service_1 = require("../../../../shared/services/pdf/pdf-edit.service");
const s3_service_1 = require("../../../../shared/services/s3.service");
const s3_key_generator_util_1 = require("../../../../shared/utils/generators/s3-key-generator.util");
const file_media_type_util_1 = require("../../../../shared/utils/file/file-media-type.util");
const exceptions_1 = require("../../../../common/exceptions");
let ContractHelperService = ContractHelperService_1 = class ContractHelperService {
    contractTemplateService;
    pdfEditService;
    s3Service;
    logger = new common_1.Logger(ContractHelperService_1.name);
    constructor(contractTemplateService, pdfEditService, s3Service) {
        this.contractTemplateService = contractTemplateService;
        this.pdfEditService = pdfEditService;
        this.s3Service = s3Service;
    }
    async createIndividualRuleContract(userId, positions) {
        try {
            this.logger.log(`Tạo hợp đồng nguyên tắc cho cá nhân, userId: ${userId}`);
            const templateBuffer = await this.contractTemplateService.getContractTemplate(contract_template_service_1.ContractTemplateType.RULE_CONTRACT_CUSTOMER);
            const editedPdfResult = await this.pdfEditService.editPdf(templateBuffer, positions);
            const contractKey = (0, s3_key_generator_util_1.generateS3Key)({
                baseFolder: 'rule-contracts',
                categoryFolder: s3_key_generator_util_1.CategoryFolderEnum.DOCUMENT,
                fileName: `individual-contract-${userId}.pdf`,
                useTimeFolder: true,
            });
            await this.s3Service.uploadBuffer(contractKey, editedPdfResult.pdfBuffer, file_media_type_util_1.FileTypeEnum.PDF);
            return {
                contractKey,
                contractBuffer: editedPdfResult.pdfBuffer,
                contractBase64: editedPdfResult.pdfBase64 || '',
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo hợp đồng nguyên tắc cho cá nhân, userId: ${userId}: ${error.message}`, error.stack);
            if (error instanceof exceptions_1.AppException) {
                throw error;
            }
            throw new exceptions_1.AppException(exceptions_1.ErrorCode.INTERNAL_SERVER_ERROR, `Lỗi khi tạo hợp đồng nguyên tắc cho cá nhân: ${error.message}`);
        }
    }
    async createBusinessRuleContract(userId, positions) {
        try {
            this.logger.log(`Tạo hợp đồng nguyên tắc cho doanh nghiệp, userId: ${userId}`);
            const templateBuffer = await this.contractTemplateService.getContractTemplate(contract_template_service_1.ContractTemplateType.RULE_CONTRACT_BUSINESS);
            const editedPdfResult = await this.pdfEditService.editPdf(templateBuffer, positions);
            const contractKey = (0, s3_key_generator_util_1.generateS3Key)({
                baseFolder: 'rule-contracts',
                categoryFolder: s3_key_generator_util_1.CategoryFolderEnum.DOCUMENT,
                fileName: `business-contract-${userId}.pdf`,
                useTimeFolder: true,
            });
            await this.s3Service.uploadBuffer(contractKey, editedPdfResult.pdfBuffer, file_media_type_util_1.FileTypeEnum.PDF);
            return {
                contractKey,
                contractBuffer: editedPdfResult.pdfBuffer,
                contractBase64: editedPdfResult.pdfBase64 || '',
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi tạo hợp đồng nguyên tắc cho doanh nghiệp, userId: ${userId}: ${error.message}`, error.stack);
            if (error instanceof exceptions_1.AppException) {
                throw error;
            }
            throw new exceptions_1.AppException(exceptions_1.ErrorCode.INTERNAL_SERVER_ERROR, `Lỗi khi tạo hợp đồng nguyên tắc cho doanh nghiệp: ${error.message}`);
        }
    }
};
exports.ContractHelperService = ContractHelperService;
exports.ContractHelperService = ContractHelperService = ContractHelperService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [contract_template_service_1.ContractTemplateService,
        pdf_edit_service_1.PdfEditService,
        s3_service_1.S3Service])
], ContractHelperService);
//# sourceMappingURL=contract-helper.service.js.map