"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationThreadController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const guards_1 = require("../../auth/guards");
const decorators_1 = require("../../auth/decorators");
const response_1 = require("../../../common/response");
const api_error_response_decorator_1 = require("../../../common/decorators/api-error-response.decorator");
const chat_error_codes_1 = require("../exceptions/chat-error-codes");
const exceptions_1 = require("../../../common/exceptions");
const conversation_thread_service_1 = require("../services/conversation-thread.service");
const conversation_thread_dto_1 = require("../dto/conversation-thread.dto");
let ConversationThreadController = class ConversationThreadController {
    conversationThreadService;
    constructor(conversationThreadService) {
        this.conversationThreadService = conversationThreadService;
    }
    async create(user, createDto) {
        createDto.userId = user.id;
        const thread = await this.conversationThreadService.create(createDto);
        return response_1.ApiResponseDto.created(thread, 'Conversation thread created successfully');
    }
    async findAll(user, queryDto) {
        const threads = await this.conversationThreadService.findAllByUser(queryDto, user.id);
        return response_1.ApiResponseDto.success(threads, 'Conversation threads retrieved successfully');
    }
    async getStats(user) {
        const stats = await this.conversationThreadService.getStats(user.id);
        return response_1.ApiResponseDto.success(stats, 'Thread statistics retrieved successfully');
    }
    async findOne(user, threadId) {
        const thread = await this.conversationThreadService.findOne(threadId, user.id);
        return response_1.ApiResponseDto.success(thread, 'Conversation thread retrieved successfully');
    }
    async update(user, threadId, updateDto) {
        const thread = await this.conversationThreadService.update(threadId, user.id, updateDto);
        return response_1.ApiResponseDto.success(thread, 'Conversation thread updated successfully');
    }
    async getThreadMessages(user, threadId, queryDto) {
        const messages = await this.conversationThreadService.getThreadMessages(threadId, user.id, queryDto);
        return response_1.ApiResponseDto.success(messages, 'Thread messages retrieved successfully');
    }
    async remove(user, threadId) {
        await this.conversationThreadService.remove(threadId, user.id);
        return response_1.ApiResponseDto.success(null, 'Conversation thread deleted successfully');
    }
};
exports.ConversationThreadController = ConversationThreadController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new conversation thread',
        description: 'Creates a new conversation thread for the authenticated user',
    }),
    (0, swagger_1.ApiCreatedResponse)({
        description: 'Conversation thread created successfully',
        schema: response_1.ApiResponseDto.getSchema(conversation_thread_dto_1.ConversationThreadResponseDto),
    }),
    (0, api_error_response_decorator_1.ApiErrorResponse)(chat_error_codes_1.CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED, chat_error_codes_1.CHAT_ERROR_CODES.THREAD_CREATION_FAILED, exceptions_1.ErrorCode.INTERNAL_SERVER_ERROR),
    (0, swagger_1.ApiBody)({ type: conversation_thread_dto_1.CreateConversationThreadDto }),
    __param(0, (0, decorators_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, conversation_thread_dto_1.CreateConversationThreadDto]),
    __metadata("design:returntype", Promise)
], ConversationThreadController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all conversation threads for the user',
        description: 'Retrieves all conversation threads belonging to the authenticated user with pagination and search',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Conversation threads retrieved successfully',
        schema: response_1.ApiResponseDto.getPaginatedSchema(conversation_thread_dto_1.ConversationThreadResponseDto),
    }),
    (0, api_error_response_decorator_1.ApiErrorResponse)(chat_error_codes_1.CHAT_ERROR_CODES.THREAD_FETCH_FAILED, exceptions_1.ErrorCode.INTERNAL_SERVER_ERROR),
    __param(0, (0, decorators_1.CurrentUser)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, conversation_thread_dto_1.GetConversationThreadsQueryDto]),
    __metadata("design:returntype", Promise)
], ConversationThreadController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get conversation thread statistics',
        description: 'Retrieves statistics about the user\'s conversation threads',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Thread statistics retrieved successfully',
        schema: response_1.ApiResponseDto.getSchema({
            type: 'object',
            properties: {
                totalThreads: {
                    type: 'number',
                    description: 'Total number of threads',
                    example: 25,
                },
                recentThreads: {
                    type: 'number',
                    description: 'Number of threads created in the last 7 days',
                    example: 5,
                },
            },
        }),
    }),
    (0, api_error_response_decorator_1.ApiErrorResponse)(chat_error_codes_1.CHAT_ERROR_CODES.THREAD_FETCH_FAILED, exceptions_1.ErrorCode.INTERNAL_SERVER_ERROR),
    __param(0, (0, decorators_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ConversationThreadController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)(':threadId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get a specific conversation thread',
        description: 'Retrieves a specific conversation thread by ID for the authenticated user',
    }),
    (0, swagger_1.ApiParam)({
        name: 'threadId',
        description: 'UUID of the conversation thread',
        example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Conversation thread retrieved successfully',
        schema: response_1.ApiResponseDto.getSchema(conversation_thread_dto_1.ConversationThreadResponseDto),
    }),
    (0, api_error_response_decorator_1.ApiErrorResponse)(chat_error_codes_1.CHAT_ERROR_CODES.THREAD_NOT_FOUND, chat_error_codes_1.CHAT_ERROR_CODES.THREAD_FETCH_FAILED, exceptions_1.ErrorCode.INTERNAL_SERVER_ERROR),
    __param(0, (0, decorators_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('threadId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ConversationThreadController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':threadId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update a conversation thread',
        description: 'Updates a specific conversation thread for the authenticated user',
    }),
    (0, swagger_1.ApiParam)({
        name: 'threadId',
        description: 'UUID of the conversation thread',
        example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Conversation thread updated successfully',
        schema: response_1.ApiResponseDto.getSchema(conversation_thread_dto_1.ConversationThreadResponseDto),
    }),
    (0, api_error_response_decorator_1.ApiErrorResponse)(chat_error_codes_1.CHAT_ERROR_CODES.THREAD_NOT_FOUND, chat_error_codes_1.CHAT_ERROR_CODES.THREAD_UPDATE_FAILED, chat_error_codes_1.CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED, exceptions_1.ErrorCode.INTERNAL_SERVER_ERROR),
    (0, swagger_1.ApiBody)({ type: conversation_thread_dto_1.UpdateConversationThreadDto }),
    __param(0, (0, decorators_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('threadId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, conversation_thread_dto_1.UpdateConversationThreadDto]),
    __metadata("design:returntype", Promise)
], ConversationThreadController.prototype, "update", null);
__decorate([
    (0, common_1.Get)(':threadId/messages'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get messages in a conversation thread',
        description: 'Retrieves all messages in a specific conversation thread with pagination and filtering',
    }),
    (0, swagger_1.ApiParam)({
        name: 'threadId',
        description: 'UUID of the conversation thread',
        example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Thread messages retrieved successfully',
        schema: response_1.ApiResponseDto.getPaginatedSchema(conversation_thread_dto_1.ThreadMessageResponseDto),
    }),
    (0, api_error_response_decorator_1.ApiErrorResponse)(chat_error_codes_1.CHAT_ERROR_CODES.THREAD_NOT_FOUND, chat_error_codes_1.CHAT_ERROR_CODES.MESSAGES_FETCH_FAILED, exceptions_1.ErrorCode.INTERNAL_SERVER_ERROR),
    __param(0, (0, decorators_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('threadId')),
    __param(2, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, conversation_thread_dto_1.GetThreadMessagesQueryDto]),
    __metadata("design:returntype", Promise)
], ConversationThreadController.prototype, "getThreadMessages", null);
__decorate([
    (0, common_1.Delete)(':threadId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete a conversation thread',
        description: 'Deletes a specific conversation thread for the authenticated user',
    }),
    (0, swagger_1.ApiParam)({
        name: 'threadId',
        description: 'UUID of the conversation thread',
        example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Conversation thread deleted successfully',
        schema: response_1.ApiResponseDto.getSchema(Object),
    }),
    (0, api_error_response_decorator_1.ApiErrorResponse)(chat_error_codes_1.CHAT_ERROR_CODES.THREAD_NOT_FOUND, chat_error_codes_1.CHAT_ERROR_CODES.THREAD_DELETE_FAILED, exceptions_1.ErrorCode.INTERNAL_SERVER_ERROR),
    __param(0, (0, decorators_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('threadId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ConversationThreadController.prototype, "remove", null);
exports.ConversationThreadController = ConversationThreadController = __decorate([
    (0, swagger_1.ApiTags)('Conversation Threads'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(guards_1.JwtUserGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiExtraModels)(conversation_thread_dto_1.ConversationThreadResponseDto, conversation_thread_dto_1.ThreadMessageResponseDto, conversation_thread_dto_1.CreateConversationThreadDto, conversation_thread_dto_1.UpdateConversationThreadDto, conversation_thread_dto_1.GetConversationThreadsQueryDto, conversation_thread_dto_1.GetThreadMessagesQueryDto),
    (0, common_1.Controller)('user/chat/threads'),
    __metadata("design:paramtypes", [conversation_thread_service_1.ConversationThreadService])
], ConversationThreadController);
//# sourceMappingURL=conversation-thread.controller.js.map