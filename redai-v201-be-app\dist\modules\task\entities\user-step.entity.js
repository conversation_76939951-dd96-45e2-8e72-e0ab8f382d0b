"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserStep = void 0;
const typeorm_1 = require("typeorm");
const step_config_interface_1 = require("../interfaces/step-config.interface");
let UserStep = class UserStep {
    stepId;
    taskId;
    orderIndex;
    stepName;
    stepDescription;
    stepType;
    stepConfig;
    googleUserAuthId;
    facebookPageId;
    createdAt;
    updatedAt;
};
exports.UserStep = UserStep;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid', { name: 'step_id' }),
    __metadata("design:type", String)
], UserStep.prototype, "stepId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'task_id', type: 'uuid', nullable: false }),
    __metadata("design:type", String)
], UserStep.prototype, "taskId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'order_index', type: 'int', nullable: false }),
    __metadata("design:type", Number)
], UserStep.prototype, "orderIndex", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'step_name', type: 'varchar', length: 255, nullable: false }),
    __metadata("design:type", String)
], UserStep.prototype, "stepName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'step_description', type: 'text', nullable: true }),
    __metadata("design:type", String)
], UserStep.prototype, "stepDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'step_type',
        type: 'varchar',
        length: 50,
        nullable: false,
    }),
    __metadata("design:type", String)
], UserStep.prototype, "stepType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'step_config', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], UserStep.prototype, "stepConfig", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'google_user_auth_id', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], UserStep.prototype, "googleUserAuthId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'facebook_page_id', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], UserStep.prototype, "facebookPageId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'created_at',
        type: 'bigint',
        default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    }),
    __metadata("design:type", Number)
], UserStep.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'updated_at',
        type: 'bigint',
        default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    }),
    __metadata("design:type", Number)
], UserStep.prototype, "updatedAt", void 0);
exports.UserStep = UserStep = __decorate([
    (0, typeorm_1.Entity)('user_steps'),
    (0, typeorm_1.Unique)('unique_task_order', ['taskId', 'orderIndex'])
], UserStep);
//# sourceMappingURL=user-step.entity.js.map