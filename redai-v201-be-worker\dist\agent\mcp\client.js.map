{"version": 3, "sources": ["../../../src/agent/mcp/client.ts"], "sourcesContent": ["import debug from 'debug';\r\nimport { z } from 'zod';\r\n\r\n// Read package name from package.json\r\nlet debugLog: debug.Debugger;\r\n\r\nfunction getDebugLog() {\r\n  if (!debugLog) {\r\n    debugLog = debug('@langchain/mcp-adapters:client');\r\n  }\r\n  return debugLog;\r\n}\r\n\r\n/**\r\n * Stdio transport restart configuration\r\n */\r\nexport function createStdioRestartSchema() {\r\n  return z\r\n    .object({\r\n      /**\r\n       * Whether to automatically restart the process if it exits\r\n       */\r\n      enabled: z\r\n        .boolean()\r\n        .describe('Whether to automatically restart the process if it exits')\r\n        .optional(),\r\n      /**\r\n       * Maximum number of restart attempts\r\n       */\r\n      maxAttempts: z\r\n        .number()\r\n        .describe('The maximum number of restart attempts')\r\n        .optional(),\r\n      /**\r\n       * Delay in milliseconds between restart attempts\r\n       */\r\n      delayMs: z\r\n        .number()\r\n        .describe('The delay in milliseconds between restart attempts')\r\n        .optional(),\r\n    })\r\n    .describe('Configuration for stdio transport restart');\r\n}\r\n\r\n/**\r\n * Stdio transport connection\r\n */\r\nexport function createStdioConnectionSchema() {\r\n  return z\r\n    .object({\r\n      /**\r\n       * Optional transport type, inferred from the structure of the config if not provided. Included\r\n       * for compatibility with common MCP client config file formats.\r\n       */\r\n      transport: z.literal('stdio').optional(),\r\n      /**\r\n       * Optional transport type, inferred from the structure of the config if not provided. Included\r\n       * for compatibility with common MCP client config file formats.\r\n       */\r\n      type: z.literal('stdio').optional(),\r\n      /**\r\n       * The executable to run the server (e.g. `node`, `npx`, etc)\r\n       */\r\n      command: z.string().describe('The executable to run the server'),\r\n      /**\r\n       * Array of command line arguments to pass to the executable\r\n       */\r\n      args: z\r\n        .array(z.string())\r\n        .describe('Command line arguments to pass to the executable'),\r\n      /**\r\n       * Environment variables to set when spawning the process.\r\n       */\r\n      env: z\r\n        .record(z.string())\r\n        .describe('The environment to use when spawning the process')\r\n        .optional(),\r\n      /**\r\n       * The encoding to use when reading from the process\r\n       */\r\n      encoding: z\r\n        .string()\r\n        .describe('The encoding to use when reading from the process')\r\n        .optional(),\r\n      /**\r\n       * How to handle stderr of the child process. This matches the semantics of Node's `child_process.spawn`\r\n       *\r\n       * The default is \"inherit\", meaning messages to stderr will be printed to the parent process's stderr.\r\n       *\r\n       * @default \"inherit\"\r\n       */\r\n      stderr: z\r\n        .union([\r\n          z.literal('overlapped'),\r\n          z.literal('pipe'),\r\n          z.literal('ignore'),\r\n          z.literal('inherit'),\r\n        ])\r\n        .describe(\r\n          \"How to handle stderr of the child process. This matches the semantics of Node's `child_process.spawn`\",\r\n        )\r\n        .optional()\r\n        .default('inherit'),\r\n      /**\r\n       * The working directory to use when spawning the process.\r\n       */\r\n      cwd: z\r\n        .string()\r\n        .describe('The working directory to use when spawning the process')\r\n        .optional(),\r\n      /**\r\n       * Additional restart settings\r\n       */\r\n      restart: createStdioRestartSchema()\r\n        .describe('Settings for automatically restarting the server')\r\n        .optional(),\r\n    })\r\n    .describe('Configuration for stdio transport connection');\r\n}\r\n\r\n/**\r\n * Streamable HTTP transport reconnection configuration\r\n */\r\nexport function createStreamableReconnectSchema() {\r\n  return z\r\n    .object({\r\n      /**\r\n       * Whether to automatically reconnect if the connection is lost\r\n       */\r\n      enabled: z\r\n        .boolean()\r\n        .describe(\r\n          'Whether to automatically reconnect if the connection is lost',\r\n        )\r\n        .optional(),\r\n      /**\r\n       * Maximum number of reconnection attempts\r\n       */\r\n      maxAttempts: z\r\n        .number()\r\n        .describe('The maximum number of reconnection attempts')\r\n        .optional(),\r\n      /**\r\n       * Delay in milliseconds between reconnection attempts\r\n       */\r\n      delayMs: z\r\n        .number()\r\n        .describe('The delay in milliseconds between reconnection attempts')\r\n        .optional(),\r\n    })\r\n    .describe('Configuration for streamable HTTP transport reconnection');\r\n}\r\n\r\n/**\r\n * Streamable HTTP transport connection\r\n */\r\nexport function createStreamableHTTPConnectionSchema() {\r\n  return z\r\n    .object({\r\n      /**\r\n       * Optional transport type, inferred from the structure of the config. If \"sse\", will not attempt\r\n       * to connect using streamable HTTP.\r\n       */\r\n      transport: z.union([z.literal('http'), z.literal('sse')]).optional(),\r\n      /**\r\n       * Optional transport type, inferred from the structure of the config. If \"sse\", will not attempt\r\n       * to connect using streamable HTTP.\r\n       */\r\n      type: z.union([z.literal('http'), z.literal('sse')]).optional(),\r\n      /**\r\n       * The URL to connect to\r\n       */\r\n      url: z.string().url(),\r\n      /**\r\n       * Additional headers to send with the request, useful for authentication\r\n       */\r\n      headers: z.record(z.string()).optional(),\r\n      /**\r\n       * Whether to use Node's EventSource for SSE connections (not applicable to streamable HTTP)\r\n       *\r\n       * @default false\r\n       */\r\n      useNodeEventSource: z.boolean().optional().default(false),\r\n      /**\r\n       * Additional reconnection settings.\r\n       */\r\n      reconnect: createStreamableReconnectSchema().optional(),\r\n      /**\r\n       * Whether to automatically fallback to SSE if Streamable HTTP is not available or not supported\r\n       *\r\n       * @default true\r\n       */\r\n      automaticSSEFallback: z.boolean().optional().default(true),\r\n    })\r\n    .describe('Configuration for streamable HTTP transport connection');\r\n}\r\n\r\n/**\r\n * Create combined schema for all transport connection types\r\n */\r\nexport function createConnectionSchema() {\r\n  return z\r\n    .union([\r\n      createStdioConnectionSchema(),\r\n      createStreamableHTTPConnectionSchema(),\r\n    ])\r\n    .describe('Configuration for a single MCP server');\r\n}\r\n\r\n/**\r\n * Configuration for stdio transport connection\r\n */\r\nexport type StdioConnection = z.input<\r\n  ReturnType<typeof createStdioConnectionSchema>\r\n>;\r\n\r\n/**\r\n * Type for {@link StdioConnection} with default values applied.\r\n */\r\nexport type ResolvedStdioConnection = z.infer<\r\n  ReturnType<typeof createStdioConnectionSchema>\r\n>;\r\n\r\n/**\r\n * Configuration for streamable HTTP transport connection\r\n */\r\nexport type StreamableHTTPConnection = z.input<\r\n  ReturnType<typeof createStreamableHTTPConnectionSchema>\r\n>;\r\n\r\n/**\r\n * Type for {@link StreamableHTTPConnection} with default values applied.\r\n */\r\nexport type ResolvedStreamableHTTPConnection = z.infer<\r\n  ReturnType<typeof createStreamableHTTPConnectionSchema>\r\n>;\r\n\r\n/**\r\n * Union type for all transport connection types\r\n */\r\nexport type Connection = z.input<ReturnType<typeof createConnectionSchema>>;\r\n\r\n/**\r\n * Type for {@link Connection} with default values applied.\r\n */\r\nexport type ResolvedConnection = z.infer<\r\n  ReturnType<typeof createConnectionSchema>\r\n>;\r\n"], "names": ["createConnectionSchema", "createStdioConnectionSchema", "createStdioRestartSchema", "createStreamableHTTPConnectionSchema", "createStreamableReconnectSchema", "debugLog", "getDebugLog", "debug", "z", "object", "enabled", "boolean", "describe", "optional", "maxAttempts", "number", "delayMs", "transport", "literal", "type", "command", "string", "args", "array", "env", "record", "encoding", "stderr", "union", "default", "cwd", "restart", "url", "headers", "useNodeEventSource", "reconnect", "automaticSSEFallback"], "mappings": ";;;;;;;;;;;QAwMgBA;eAAAA;;QAzJAC;eAAAA;;QA/BAC;eAAAA;;QA4IAC;eAAAA;;QAjCAC;eAAAA;;;8DA3HE;qBACA;;;;;;AAElB,sCAAsC;AACtC,IAAIC;AAEJ,SAASC;IACP,IAAI,CAACD,UAAU;QACbA,WAAWE,IAAAA,cAAK,EAAC;IACnB;IACA,OAAOF;AACT;AAKO,SAASH;IACd,OAAOM,MAAC,CACLC,MAAM,CAAC;QACN;;OAEC,GACDC,SAASF,MAAC,CACPG,OAAO,GACPC,QAAQ,CAAC,4DACTC,QAAQ;QACX;;OAEC,GACDC,aAAaN,MAAC,CACXO,MAAM,GACNH,QAAQ,CAAC,0CACTC,QAAQ;QACX;;OAEC,GACDG,SAASR,MAAC,CACPO,MAAM,GACNH,QAAQ,CAAC,sDACTC,QAAQ;IACb,GACCD,QAAQ,CAAC;AACd;AAKO,SAASX;IACd,OAAOO,MAAC,CACLC,MAAM,CAAC;QACN;;;OAGC,GACDQ,WAAWT,MAAC,CAACU,OAAO,CAAC,SAASL,QAAQ;QACtC;;;OAGC,GACDM,MAAMX,MAAC,CAACU,OAAO,CAAC,SAASL,QAAQ;QACjC;;OAEC,GACDO,SAASZ,MAAC,CAACa,MAAM,GAAGT,QAAQ,CAAC;QAC7B;;OAEC,GACDU,MAAMd,MAAC,CACJe,KAAK,CAACf,MAAC,CAACa,MAAM,IACdT,QAAQ,CAAC;QACZ;;OAEC,GACDY,KAAKhB,MAAC,CACHiB,MAAM,CAACjB,MAAC,CAACa,MAAM,IACfT,QAAQ,CAAC,oDACTC,QAAQ;QACX;;OAEC,GACDa,UAAUlB,MAAC,CACRa,MAAM,GACNT,QAAQ,CAAC,qDACTC,QAAQ;QACX;;;;;;OAMC,GACDc,QAAQnB,MAAC,CACNoB,KAAK,CAAC;YACLpB,MAAC,CAACU,OAAO,CAAC;YACVV,MAAC,CAACU,OAAO,CAAC;YACVV,MAAC,CAACU,OAAO,CAAC;YACVV,MAAC,CAACU,OAAO,CAAC;SACX,EACAN,QAAQ,CACP,yGAEDC,QAAQ,GACRgB,OAAO,CAAC;QACX;;OAEC,GACDC,KAAKtB,MAAC,CACHa,MAAM,GACNT,QAAQ,CAAC,0DACTC,QAAQ;QACX;;OAEC,GACDkB,SAAS7B,2BACNU,QAAQ,CAAC,oDACTC,QAAQ;IACb,GACCD,QAAQ,CAAC;AACd;AAKO,SAASR;IACd,OAAOI,MAAC,CACLC,MAAM,CAAC;QACN;;OAEC,GACDC,SAASF,MAAC,CACPG,OAAO,GACPC,QAAQ,CACP,gEAEDC,QAAQ;QACX;;OAEC,GACDC,aAAaN,MAAC,CACXO,MAAM,GACNH,QAAQ,CAAC,+CACTC,QAAQ;QACX;;OAEC,GACDG,SAASR,MAAC,CACPO,MAAM,GACNH,QAAQ,CAAC,2DACTC,QAAQ;IACb,GACCD,QAAQ,CAAC;AACd;AAKO,SAAST;IACd,OAAOK,MAAC,CACLC,MAAM,CAAC;QACN;;;OAGC,GACDQ,WAAWT,MAAC,CAACoB,KAAK,CAAC;YAACpB,MAAC,CAACU,OAAO,CAAC;YAASV,MAAC,CAACU,OAAO,CAAC;SAAO,EAAEL,QAAQ;QAClE;;;OAGC,GACDM,MAAMX,MAAC,CAACoB,KAAK,CAAC;YAACpB,MAAC,CAACU,OAAO,CAAC;YAASV,MAAC,CAACU,OAAO,CAAC;SAAO,EAAEL,QAAQ;QAC7D;;OAEC,GACDmB,KAAKxB,MAAC,CAACa,MAAM,GAAGW,GAAG;QACnB;;OAEC,GACDC,SAASzB,MAAC,CAACiB,MAAM,CAACjB,MAAC,CAACa,MAAM,IAAIR,QAAQ;QACtC;;;;OAIC,GACDqB,oBAAoB1B,MAAC,CAACG,OAAO,GAAGE,QAAQ,GAAGgB,OAAO,CAAC;QACnD;;OAEC,GACDM,WAAW/B,kCAAkCS,QAAQ;QACrD;;;;OAIC,GACDuB,sBAAsB5B,MAAC,CAACG,OAAO,GAAGE,QAAQ,GAAGgB,OAAO,CAAC;IACvD,GACCjB,QAAQ,CAAC;AACd;AAKO,SAASZ;IACd,OAAOQ,MAAC,CACLoB,KAAK,CAAC;QACL3B;QACAE;KACD,EACAS,QAAQ,CAAC;AACd"}