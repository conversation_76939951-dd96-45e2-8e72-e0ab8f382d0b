"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericPageSubmission = void 0;
const typeorm_1 = require("typeorm");
const generic_page_enum_1 = require("../constants/generic-page.enum");
const generic_page_entity_1 = require("./generic-page.entity");
let GenericPageSubmission = class GenericPageSubmission {
    id;
    pageId;
    data;
    status;
    createdAt;
    updatedAt;
    ipAddress;
    userAgent;
    userId;
    page;
};
exports.GenericPageSubmission = GenericPageSubmission;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], GenericPageSubmission.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'page_id', length: 36 }),
    __metadata("design:type", String)
], GenericPageSubmission.prototype, "pageId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Object)
], GenericPageSubmission.prototype, "data", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: generic_page_enum_1.GenericPageSubmissionStatusEnum,
        default: generic_page_enum_1.GenericPageSubmissionStatusEnum.PENDING,
    }),
    __metadata("design:type", String)
], GenericPageSubmission.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_at', type: 'bigint' }),
    __metadata("design:type", Number)
], GenericPageSubmission.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_at', type: 'bigint' }),
    __metadata("design:type", Number)
], GenericPageSubmission.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ip_address', length: 45, nullable: true }),
    __metadata("design:type", String)
], GenericPageSubmission.prototype, "ipAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_agent', type: 'text', nullable: true }),
    __metadata("design:type", String)
], GenericPageSubmission.prototype, "userAgent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id', length: 36, nullable: true }),
    __metadata("design:type", String)
], GenericPageSubmission.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => generic_page_entity_1.GenericPage, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'page_id' }),
    __metadata("design:type", generic_page_entity_1.GenericPage)
], GenericPageSubmission.prototype, "page", void 0);
exports.GenericPageSubmission = GenericPageSubmission = __decorate([
    (0, typeorm_1.Entity)('generic_page_submissions')
], GenericPageSubmission);
//# sourceMappingURL=generic-page-submission.entity.js.map