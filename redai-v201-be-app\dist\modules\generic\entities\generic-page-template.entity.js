"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericPageTemplate = void 0;
const typeorm_1 = require("typeorm");
let GenericPageTemplate = class GenericPageTemplate {
    id;
    name;
    description;
    category;
    thumbnail;
    config;
    createdAt;
    updatedAt;
    createdBy;
    updatedBy;
};
exports.GenericPageTemplate = GenericPageTemplate;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], GenericPageTemplate.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], GenericPageTemplate.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], GenericPageTemplate.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, nullable: true }),
    __metadata("design:type", String)
], GenericPageTemplate.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], GenericPageTemplate.prototype, "thumbnail", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Object)
], GenericPageTemplate.prototype, "config", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_at', type: 'bigint' }),
    __metadata("design:type", Number)
], GenericPageTemplate.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_at', type: 'bigint' }),
    __metadata("design:type", Number)
], GenericPageTemplate.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', length: 36 }),
    __metadata("design:type", String)
], GenericPageTemplate.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', length: 36 }),
    __metadata("design:type", String)
], GenericPageTemplate.prototype, "updatedBy", void 0);
exports.GenericPageTemplate = GenericPageTemplate = __decorate([
    (0, typeorm_1.Entity)('generic_page_templates')
], GenericPageTemplate);
//# sourceMappingURL=generic-page-template.entity.js.map