"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericPageUserController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const current_user_decorator_1 = require("../../../auth/decorators/current-user.decorator");
const response_1 = require("../../../../common/response");
const services_1 = require("../services");
const dto_1 = require("../dto");
let GenericPageUserController = class GenericPageUserController {
    genericPageUserService;
    constructor(genericPageUserService) {
        this.genericPageUserService = genericPageUserService;
    }
    async getPublishedGenericPageByPath(path) {
        const result = await this.genericPageUserService.getPublishedGenericPageByPath(path);
        return response_1.ApiResponseDto.success(result);
    }
    async submitForm(id, submitFormDto, request, user) {
        const ipAddress = request.ip;
        const userAgent = request.headers['user-agent'];
        const result = await this.genericPageUserService.submitForm(id, submitFormDto, user ? String(user.id) : undefined, ipAddress, userAgent);
        return response_1.ApiResponseDto.success(result);
    }
};
exports.GenericPageUserController = GenericPageUserController;
__decorate([
    (0, common_1.Get)('by-path/:path'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin trang đã xuất bản theo đường dẫn' }),
    (0, swagger_1.ApiParam)({ name: 'path', description: 'Đường dẫn của trang' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Thông tin trang',
        schema: response_1.ApiResponseDto.getSchema(dto_1.GenericPageResponseDto),
    }),
    __param(0, (0, common_1.Param)('path')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], GenericPageUserController.prototype, "getPublishedGenericPageByPath", null);
__decorate([
    (0, common_1.Post)(':id/submit'),
    (0, swagger_1.ApiOperation)({ summary: 'Gửi dữ liệu form' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID của trang' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dữ liệu đã được gửi thành công',
        schema: response_1.ApiResponseDto.getSchema(dto_1.SubmissionResponseDto),
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __param(3, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.SubmitFormDto, Object, Object]),
    __metadata("design:returntype", Promise)
], GenericPageUserController.prototype, "submitForm", null);
exports.GenericPageUserController = GenericPageUserController = __decorate([
    (0, swagger_1.ApiTags)('User Generic Page'),
    (0, common_1.Controller)('user/generic-pages'),
    (0, swagger_1.ApiExtraModels)(response_1.ApiResponseDto, dto_1.GenericPageResponseDto, dto_1.SubmissionResponseDto),
    __metadata("design:paramtypes", [services_1.GenericPageUserService])
], GenericPageUserController);
//# sourceMappingURL=generic-page-user.controller.js.map