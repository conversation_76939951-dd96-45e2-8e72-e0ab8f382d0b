"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get ToolException () {
        return ToolException;
    },
    get loadMcpTools () {
        return loadMcpTools;
    }
});
const _tools = require("@langchain/core/tools");
const _debug = /*#__PURE__*/ _interop_require_default(require("debug"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const mcpBuildInToolName = 'execute_tool_buildin';
const mcpIntegrationToolName = 'execute_tool_integration';
// Replace direct initialization with lazy initialization
let debugLog;
function getDebugLog() {
    if (!debugLog) {
        debugLog = (0, _debug.default)('@langchain/mcp-adapters:tools');
    }
    return debugLog;
}
async function _embeddedResourceToArtifact(resource, client) {
    if (!resource.blob && !resource.text && resource.uri) {
        const response = await client.readResource({
            uri: resource.resource.uri
        });
        return response.contents.map((content)=>({
                type: 'resource',
                resource: {
                    ...content
                }
            }));
    }
    return [
        resource
    ];
}
let ToolException = class ToolException extends Error {
    constructor(message){
        super(message);
        this.name = 'ToolException';
    }
};
/**
 * Process the result from calling an MCP tool.
 * Extracts text content and non-text content for better worker compatibility.
 *
 * @param serverName
 * @param toolName
 * @param result - The result from the MCP tool call
 * @param client
 * @returns A tuple of [textContent, nonTextContent]
 */ async function _convertCallToolResult(serverName, toolName, result, client) {
    if (!result) {
        throw new ToolException(`MCP tool '${toolName}' on server '${serverName}' returned an invalid result - tool call response was undefined`);
    }
    if (!Array.isArray(result.content)) {
        throw new ToolException(`MCP tool '${toolName}' on server '${serverName}' returned an invalid result - expected an array of content, but was ${typeof result.content}`);
    }
    if (result.isError) {
        throw new ToolException(`MCP tool '${toolName}' on server '${serverName}' returned an error: ${result.content.map((content)=>content.text).join('\n')}`);
    }
    const mcpTextAndImageContent = result.content.filter((content)=>content.type === 'text' || content.type === 'image').map((content)=>{
        switch(content.type){
            case 'text':
                return {
                    type: 'text',
                    text: content.text
                };
            case 'image':
                return {
                    type: 'image_url',
                    image_url: {
                        url: `data:${content.mimeType};base64,${content.data}`
                    }
                };
            default:
                throw new ToolException(`MCP tool '${toolName}' on server '${serverName}' returned an invalid result - expected a text or image content, but was ${content.type}`);
        }
    });
    // Create the text content output
    const artifacts = (await Promise.all(result.content.filter((content)=>content.type === 'resource').map((content)=>_embeddedResourceToArtifact(content, client)))).flat();
    if (mcpTextAndImageContent.length === 1 && mcpTextAndImageContent[0].type === 'text') {
        return [
            mcpTextAndImageContent[0].text,
            artifacts
        ];
    }
    return [
        mcpTextAndImageContent,
        artifacts
    ];
}
/**
 * Call an MCP tool.
 *
 * Use this with `.bind` to capture the fist three arguments, then pass to the constructor of DynamicStructuredTool.
 *
 * @internal
 *
 * @param serverName
 * @param client - The MCP client
 * @param toolName - The name of the tool (forwarded to the client)
 * @param args - The arguments to pass to the tool
 * @param extra
 * @returns A tuple of [textContent, nonTextContent]
 */ async function _callTool(serverName, toolName, client, extra, args) {
    let result;
    try {
        getDebugLog()(`INFO: Calling tool ${toolName}(${JSON.stringify(args)})`);
        result = await client.callTool({
            name: toolName,
            arguments: args,
            extra
        });
    } catch (error) {
        getDebugLog()(`Error calling tool ${toolName}: ${String(error)}`);
        // eslint-disable-next-line no-instanceof/no-instanceof
        if (error instanceof ToolException) {
            throw error;
        }
        throw new ToolException(`Error calling tool ${toolName}: ${String(error)}`);
    }
    return _convertCallToolResult(serverName, toolName, result, client);
}
const defaultLoadMcpToolsOptions = {
    throwOnLoadError: true,
    prefixToolNameWithServerName: false,
    additionalToolNamePrefix: ''
};
async function loadMcpTools(serverName, client, options) {
    const { throwOnLoadError, prefixToolNameWithServerName, additionalToolNamePrefix } = {
        ...defaultLoadMcpToolsOptions,
        ...options ?? {}
    };
    if (!options?.apiKey) {
        throw new Error('API key is required');
    }
    // Get tools in a single operation
    const { resources } = await client.listResources();
    const resourceURI = resources[0].uri;
    const toolsResponseRaw = await client.readResource({
        uri: resourceURI,
        headers: {
            'x-api-key': options.apiKey
        }
    });
    const toolsResponse = JSON.parse(toolsResponseRaw?.contents[0].text)['result']['tools'];
    getDebugLog()(`INFO: Found ${toolsResponse.tools?.length || 0} MCP tools`);
    const initialPrefix = additionalToolNamePrefix ? `${additionalToolNamePrefix}__` : '';
    const serverPrefix = prefixToolNameWithServerName ? `${serverName}__` : '';
    const toolNamePrefix = `${initialPrefix}${serverPrefix}`;
    // Filter out tools without names and convert in a single map operation
    return (await Promise.all((toolsResponse.tools || []).filter((tool)=>!!tool.name).map(async (tool)=>{
        try {
            const dst = new _tools.DynamicStructuredTool({
                name: `${toolNamePrefix}${tool.name}`,
                description: tool.description || '',
                schema: tool.inputSchema,
                responseFormat: 'content_and_artifact',
                func: _callTool.bind(null, serverName, tool['toolType'] === 'IN_SYSTEM' ? mcpBuildInToolName : mcpIntegrationToolName, client, tool['extra'])
            });
            getDebugLog()(`INFO: Successfully loaded tool: ${dst.name}`);
            return dst;
        } catch (error) {
            getDebugLog()(`ERROR: Failed to load tool "${tool.name}":`, error);
            if (throwOnLoadError) {
                throw error;
            }
            return null;
        }
    }))).filter(Boolean);
}

//# sourceMappingURL=tools.js.map