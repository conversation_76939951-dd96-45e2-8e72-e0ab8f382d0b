{"version": 3, "sources": ["../../../src/agent/database/user-agent-runs.queries.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\nimport { UserAgentRunStatus } from '../../shared/enums';\nimport { AgentDatabaseService } from './database.service';\n\n/**\n * Interface for user agent run data (agent perspective)\n */\nexport interface UserAgentRun {\n  id: string;\n  payload: any; // JSONB data - this is what the agent needs\n  status: UserAgentRunStatus;\n  created_at: number;\n  created_by: number;\n}\n\n/**\n * Raw SQL queries for user_agent_runs table operations (Agent Module)\n * \n * The agent module only needs to:\n * 1. Query run by ID to get payload for processing\n * 2. Update run status during processing\n */\n@Injectable()\nexport class UserAgentRunsQueries {\n  constructor(private readonly databaseService: AgentDatabaseService) {}\n\n  /**\n   * Get a user agent run by ID (main agent operation)\n   * @param id Run ID\n   * @returns Promise<UserAgentRun | null>\n   */\n  async getRunById(id: string): Promise<UserAgentRun | null> {\n    const query = `\n      SELECT id, payload, status, created_at, created_by \n      FROM user_agent_runs \n      WHERE id = $1\n    `;\n    \n    const result = await this.databaseService.query(query, [id]);\n    \n    if (result.length === 0) {\n      return null;\n    }\n    \n    return {\n      id: result[0].id,\n      payload: result[0].payload, // This is the SystemAgentConfig payload\n      status: result[0].status as UserAgentRunStatus,\n      created_at: result[0].created_at,\n      created_by: result[0].created_by\n    };\n  }\n\n  /**\n   * Update run status (agent updates status during processing)\n   * @param id Run ID\n   * @param status New status\n   * @returns Promise<boolean> True if update was successful\n   */\n  async updateRunStatus(id: string, status: UserAgentRunStatus): Promise<boolean> {\n    const query = `\n      UPDATE user_agent_runs \n      SET status = $1 \n      WHERE id = $2\n    `;\n    \n    const result = await this.databaseService.query(query, [status, id]);\n    return Array.isArray(result) && result.length >= 0;\n  }\n}\n"], "names": ["UserAgentRunsQueries", "getRunById", "id", "query", "result", "databaseService", "length", "payload", "status", "created_at", "created_by", "updateRunStatus", "Array", "isArray", "constructor"], "mappings": ";;;;+BAuBaA;;;eAAAA;;;wBAvBc;iCAEU;;;;;;;;;;AAqB9B,IAAA,AAAMA,uBAAN,MAAMA;IAGX;;;;GAIC,GACD,MAAMC,WAAWC,EAAU,EAAgC;QACzD,MAAMC,QAAQ,CAAC;;;;IAIf,CAAC;QAED,MAAMC,SAAS,MAAM,IAAI,CAACC,eAAe,CAACF,KAAK,CAACA,OAAO;YAACD;SAAG;QAE3D,IAAIE,OAAOE,MAAM,KAAK,GAAG;YACvB,OAAO;QACT;QAEA,OAAO;YACLJ,IAAIE,MAAM,CAAC,EAAE,CAACF,EAAE;YAChBK,SAASH,MAAM,CAAC,EAAE,CAACG,OAAO;YAC1BC,QAAQJ,MAAM,CAAC,EAAE,CAACI,MAAM;YACxBC,YAAYL,MAAM,CAAC,EAAE,CAACK,UAAU;YAChCC,YAAYN,MAAM,CAAC,EAAE,CAACM,UAAU;QAClC;IACF;IAEA;;;;;GAKC,GACD,MAAMC,gBAAgBT,EAAU,EAAEM,MAA0B,EAAoB;QAC9E,MAAML,QAAQ,CAAC;;;;IAIf,CAAC;QAED,MAAMC,SAAS,MAAM,IAAI,CAACC,eAAe,CAACF,KAAK,CAACA,OAAO;YAACK;YAAQN;SAAG;QACnE,OAAOU,MAAMC,OAAO,CAACT,WAAWA,OAAOE,MAAM,IAAI;IACnD;IA5CAQ,YAAY,AAAiBT,eAAqC,CAAE;aAAvCA,kBAAAA;IAAwC;AA6CvE"}