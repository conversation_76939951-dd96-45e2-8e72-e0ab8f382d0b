{"version": 3, "file": "optimized-model-discovery.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/models/services/optimized-model-discovery.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+FAAmF;AACnF,yFAAoF;AACpF,uFAAkF;AAClF,mFAA8E;AAC9E,qGAA8F;AAC9F,iGAA0F;AAC1F,uFAAkG;AAClG,+EAA0E;AAC1E,mFAA4F;AAwCrF,IAAM,8BAA8B,sCAApC,MAAM,8BAA8B;IAItB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAXF,MAAM,GAAG,IAAI,eAAM,CAAC,gCAA8B,CAAC,IAAI,CAAC,CAAC;IAE1E,YACmB,gBAAkC,EAClC,uBAAgD,EAChD,sBAA8C,EAC9C,oBAA0C,EAC1C,2BAAwD,EACxD,yBAAoD,EACpD,qBAAmD,EACnD,kBAA6C,EAC7C,mBAA+C;QAR/C,qBAAgB,GAAhB,gBAAgB,CAAkB;QAClC,4BAAuB,GAAvB,uBAAuB,CAAyB;QAChD,2BAAsB,GAAtB,sBAAsB,CAAwB;QAC9C,yBAAoB,GAApB,oBAAoB,CAAsB;QAC1C,gCAA2B,GAA3B,2BAA2B,CAA6B;QACxD,8BAAyB,GAAzB,yBAAyB,CAA2B;QACpD,0BAAqB,GAArB,qBAAqB,CAA8B;QACnD,uBAAkB,GAAlB,kBAAkB,CAA2B;QAC7C,wBAAmB,GAAnB,mBAAmB,CAA4B;IAC/D,CAAC;IASJ,KAAK,CAAC,oBAAoB,CACxB,MAAuB,EACvB,KAAa,EACb,QAAgB;QAEhB,MAAM,WAAW,GAAG,0BAA0B,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAEpE,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,WAAW,EAAE;YACnD,SAAS,EAAE,sBAAsB;YACjC,KAAK;YACL,QAAQ;YACR,UAAU,EAAE,MAAM,CAAC,MAAM;SAC1B,CAAC,CAAC;QAEH,IAAI,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YAC1D,MAAM,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAGrC,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;YAG7D,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACrF,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,iBAAiB,CAAC;YAGxD,MAAM,aAAa,GAAuE,EAAE,CAAC;YAC7F,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvC,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAEnC,IAAI,KAAK,EAAE,CAAC;oBACV,aAAa,CAAC,IAAI,CAAC;wBACjB,KAAK;wBACL,UAAU,EAAE,KAAK,CAAC,UAAU;wBAC5B,KAAK,EAAE,KAAK,CAAC,KAAK;qBACnB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAGD,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;YAEhD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,aAAa,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,sBAAsB,gBAAgB,IAAI,CAAC,CAAC;YAGhI,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACpC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YACrE,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC;YAGtD,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,WAAW,EAAE;gBACjD,eAAe,EAAE,MAAM,CAAC,MAAM;gBAC9B,eAAe,EAAE,aAAa,CAAC,MAAM;gBACrC,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;gBACzC,eAAe,EAAE,MAAM,CAAC,eAAe;aACxC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAGzE,MAAM,CAAC,kBAAkB,GAAG;gBAC1B,mBAAmB,EAAE,gBAAgB;gBACrC,qBAAqB,EAAE,eAAe;gBACtC,SAAS,EAAE,YAAY,EAAE,QAAQ,IAAI,CAAC;gBACtC,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC;aACnD,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qCAAqC,MAAM,CAAC,gBAAgB,SAAS,MAAM,CAAC,mBAAmB,WAAW,CAAC,CAAC;YAC5H,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAC7D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAClF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IASD,KAAK,CAAC,kBAAkB,CACtB,MAAuB,EACvB,KAAa,EACb,QAAgB;QAEhB,MAAM,WAAW,GAAG,wBAAwB,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAElE,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,WAAW,EAAE;YACnD,SAAS,EAAE,oBAAoB;YAC/B,KAAK;YACL,QAAQ;YACR,UAAU,EAAE,MAAM,CAAC,MAAM;SAC1B,CAAC,CAAC;QAEH,IAAI,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YAC1D,MAAM,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAGrC,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;YAG7D,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACrF,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,iBAAiB,CAAC;YAGxD,MAAM,aAAa,GAAuE,EAAE,CAAC;YAC7F,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvC,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAEnC,IAAI,KAAK,EAAE,CAAC;oBACV,aAAa,CAAC,IAAI,CAAC;wBACjB,KAAK;wBACL,UAAU,EAAE,KAAK,CAAC,UAAU;wBAC5B,KAAK,EAAE,KAAK,CAAC,KAAK;qBACnB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAGD,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;YAEhD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,aAAa,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,sBAAsB,gBAAgB,IAAI,CAAC,CAAC;YAGhI,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACpC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YACnE,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC;YAGtD,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,WAAW,EAAE;gBACjD,eAAe,EAAE,MAAM,CAAC,MAAM;gBAC9B,eAAe,EAAE,aAAa,CAAC,MAAM;gBACrC,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;gBACzC,eAAe,EAAE,MAAM,CAAC,eAAe;aACxC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAGzE,MAAM,CAAC,kBAAkB,GAAG;gBAC1B,mBAAmB,EAAE,gBAAgB;gBACrC,qBAAqB,EAAE,eAAe;gBACtC,SAAS,EAAE,YAAY,EAAE,QAAQ,IAAI,CAAC;gBACtC,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC;aACnD,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,MAAM,CAAC,gBAAgB,SAAS,MAAM,CAAC,mBAAmB,WAAW,CAAC,CAAC;YAC1H,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAC7D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAChF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAOO,KAAK,CAAC,mBAAmB,CAAC,QAAgB;QAChD,MAAM,QAAQ,GAAG,YAAY,QAAQ,EAAE,CAAC;QAGxC,IAAI,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAEtE,IAAI,CAAC,QAAQ,EAAE,CAAC;YAEd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,QAAQ,EAAE,CAAC,CAAC;YAE1D,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,EAAE,CAAC;YACjF,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;YAGlF,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAE7D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,QAAQ,CAAC,MAAM,2BAA2B,QAAQ,EAAE,CAAC,CAAC;QACjG,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,QAAQ,KAAK,QAAQ,CAAC,MAAM,YAAY,CAAC,CAAC;QACzF,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAOO,UAAU,CAAC,KAAoB;QACrC,OAAO,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC;IAC7C,CAAC;IAOO,qBAAqB,CAAC,QAAgB;QAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,CAAC;QACzD,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;QAGhF,IAAI,aAAa,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;YAC9C,OAAO,GAAG,CAAC;QACb,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IAQO,KAAK,CAAC,oBAAoB,CAChC,aAAiF,EACjF,KAAa;QAEb,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO;gBACL,gBAAgB,EAAE,CAAC;gBACnB,aAAa,EAAE,CAAC;gBAChB,gBAAgB,EAAE,CAAC;gBACnB,mBAAmB,EAAE,CAAC;gBACtB,eAAe,EAAE,CAAC;gBAClB,WAAW,EAAE,EAAE;gBACf,MAAM,EAAE,EAAE;aACX,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YAEH,MAAM,aAAa,GAAoB,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC1F,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;gBAC/B,eAAe,EAAE,UAAU;gBAC3B,QAAQ,EAAE;oBACR,KAAK;oBACL,aAAa,EAAE,KAAK;iBACrB;aACF,CAAC,CAAC,CAAC;YAGJ,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,CACtE,aAAa,EACb,KAAK,CACN,CAAC;YAGF,MAAM,WAAW,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC;gBAChE,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;gBAC/B,eAAe,EAAE,UAAU;gBAC3B,KAAK,EAAE,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;aACzD,CAAC,CAAC,CAAC;YAEJ,OAAO;gBACL,gBAAgB,EAAE,aAAa,CAAC,MAAM;gBACtC,aAAa,EAAE,aAAa,CAAC,MAAM;gBACnC,gBAAgB,EAAE,UAAU,CAAC,gBAAgB;gBAC7C,mBAAmB,EAAE,UAAU,CAAC,mBAAmB;gBACnD,eAAe,EAAE,UAAU,CAAC,eAAe;gBAC3C,WAAW;gBACX,MAAM,EAAE,UAAU,CAAC,MAAM;aAC1B,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACnF,OAAO;gBACL,gBAAgB,EAAE,aAAa,CAAC,MAAM;gBACtC,aAAa,EAAE,aAAa,CAAC,MAAM;gBACnC,gBAAgB,EAAE,CAAC;gBACnB,mBAAmB,EAAE,CAAC;gBACtB,eAAe,EAAE,CAAC;gBAClB,WAAW,EAAE,EAAE;gBACf,MAAM,EAAE,CAAC,qBAAqB,KAAK,CAAC,OAAO,EAAE,CAAC;aAC/C,CAAC;QACJ,CAAC;IACH,CAAC;IAQO,KAAK,CAAC,kBAAkB,CAC9B,aAAiF,EACjF,KAAa;QAEb,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO;gBACL,gBAAgB,EAAE,CAAC;gBACnB,aAAa,EAAE,CAAC;gBAChB,gBAAgB,EAAE,CAAC;gBACnB,mBAAmB,EAAE,CAAC;gBACtB,eAAe,EAAE,CAAC;gBAClB,WAAW,EAAE,EAAE;gBACf,MAAM,EAAE,EAAE;aACX,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YAEH,MAAM,aAAa,GAAoB,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC1F,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;gBAC/B,eAAe,EAAE,UAAU;gBAC3B,QAAQ,EAAE;oBACR,KAAK;oBACL,aAAa,EAAE,KAAK;iBACrB;aACF,CAAC,CAAC,CAAC;YAGJ,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CACpE,aAAa,EACb,KAAK,CACN,CAAC;YAGF,MAAM,WAAW,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC;gBAChE,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;gBAC/B,eAAe,EAAE,UAAU;gBAC3B,KAAK,EAAE,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;aACzD,CAAC,CAAC,CAAC;YAEJ,OAAO;gBACL,gBAAgB,EAAE,aAAa,CAAC,MAAM;gBACtC,aAAa,EAAE,aAAa,CAAC,MAAM;gBACnC,gBAAgB,EAAE,UAAU,CAAC,gBAAgB;gBAC7C,mBAAmB,EAAE,UAAU,CAAC,mBAAmB;gBACnD,eAAe,EAAE,UAAU,CAAC,eAAe;gBAC3C,WAAW;gBACX,MAAM,EAAE,UAAU,CAAC,MAAM;aAC1B,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACjF,OAAO;gBACL,gBAAgB,EAAE,aAAa,CAAC,MAAM;gBACtC,aAAa,EAAE,aAAa,CAAC,MAAM;gBACnC,gBAAgB,EAAE,CAAC;gBACnB,mBAAmB,EAAE,CAAC;gBACtB,eAAe,EAAE,CAAC;gBAClB,WAAW,EAAE,EAAE;gBACf,MAAM,EAAE,CAAC,qBAAqB,KAAK,CAAC,OAAO,EAAE,CAAC;aAC/C,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AA7XY,wEAA8B;yCAA9B,8BAA8B;IAD1C,IAAA,mBAAU,GAAE;qCAK0B,qCAAgB;QACT,mDAAuB;QACxB,iDAAsB;QACxB,6CAAoB;QACb,6DAA2B;QAC7B,yDAAyB;QAC7B,8DAA4B;QAC/B,uDAAyB;QACxB,0DAA0B;GAZvD,8BAA8B,CA6X1C"}