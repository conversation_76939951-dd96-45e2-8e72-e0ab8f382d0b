"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentConfigQueries = void 0;
const common_1 = require("@nestjs/common");
const database_service_1 = require("./database.service");
const system_agent_config_interface_1 = require("../interfaces/system-agent-config.interface");
let AgentConfigQueries = class AgentConfigQueries {
    databaseService;
    constructor(databaseService) {
        this.databaseService = databaseService;
    }
    async getAllSystemAgentConfigs() {
        const query = `
      SELECT 
        -- agents table
        a.id as agent_id,
        a.name as agent_name,
        a.instruction as agent_instruction,
        a.model_config as agent_model_config,
        a.vector_store_id as agent_vector_store_id,
        
        -- agents_system table
        ags.name_code as system_name_code,
        ags.description as system_description,
        ags.is_supervisor as system_is_supervisor,
        ags.active as system_active,
        
        -- system_models table
        sm.model_id,
        sm.provider as model_provider,
        
        -- Aggregated API keys
        COALESCE(
          array_agg(DISTINCT skl.api_key) FILTER (WHERE skl.api_key IS NOT NULL), 
          ARRAY[]::text[]
        ) as api_keys,
        
        -- Aggregated MCP server names
        COALESCE(
          array_agg(DISTINCT ms.name_server) FILTER (WHERE ms.name_server IS NOT NULL), 
          ARRAY[]::text[]
        ) as mcp_server_names,
        
        -- Aggregated MCP configs
        COALESCE(
          array_agg(DISTINCT ms.config) FILTER (WHERE ms.config IS NOT NULL), 
          ARRAY[]::jsonb[]
        ) as mcp_configs
        
      FROM agents a
      INNER JOIN agents_system ags ON a.id = ags.id
      INNER JOIN system_models sm ON ags.system_model_id = sm.id
      LEFT JOIN system_model_key_llm smkl ON sm.id = smkl.model_id
      LEFT JOIN system_key_llm skl ON smkl.llm_key_id = skl.id AND skl.deleted_at IS NULL
      LEFT JOIN agent_system_mcp asm ON ags.id = asm.agent_id
      LEFT JOIN mcp_systems ms ON asm.mcp_id = ms.id
      
      WHERE ags.active = true 
        AND a.deleted_at IS NULL
        AND sm.active = true
      
      GROUP BY 
        a.id, a.name, a.instruction, a.model_config, a.vector_store_id,
        ags.name_code, ags.description, ags.is_supervisor, ags.active,
        sm.model_id, sm.provider
      
      ORDER BY ags.is_supervisor DESC, a.name ASC
    `;
        const rawResults = await this.databaseService.query(query);
        return this.buildSystemAgentConfigMap(rawResults);
    }
    async getSystemAgentConfigById(agentId) {
        const query = `
      SELECT 
        -- agents table
        a.id as agent_id,
        a.name as agent_name,
        a.instruction as agent_instruction,
        a.model_config as agent_model_config,
        a.vector_store_id as agent_vector_store_id,
        
        -- agents_system table
        ags.name_code as system_name_code,
        ags.description as system_description,
        ags.is_supervisor as system_is_supervisor,
        ags.active as system_active,
        
        -- system_models table
        sm.model_id,
        sm.provider as model_provider,
        
        -- Aggregated API keys
        COALESCE(
          array_agg(DISTINCT skl.api_key) FILTER (WHERE skl.api_key IS NOT NULL), 
          ARRAY[]::text[]
        ) as api_keys,
        
        -- Aggregated MCP server names
        COALESCE(
          array_agg(DISTINCT ms.name_server) FILTER (WHERE ms.name_server IS NOT NULL), 
          ARRAY[]::text[]
        ) as mcp_server_names,
        
        -- Aggregated MCP configs
        COALESCE(
          array_agg(DISTINCT ms.config) FILTER (WHERE ms.config IS NOT NULL), 
          ARRAY[]::jsonb[]
        ) as mcp_configs
        
      FROM agents a
      INNER JOIN agents_system ags ON a.id = ags.id
      INNER JOIN system_models sm ON ags.system_model_id = sm.id
      LEFT JOIN system_model_key_llm smkl ON sm.id = smkl.model_id
      LEFT JOIN system_key_llm skl ON smkl.llm_key_id = skl.id AND skl.deleted_at IS NULL
      LEFT JOIN agent_system_mcp asm ON ags.id = asm.agent_id
      LEFT JOIN mcp_systems ms ON asm.mcp_id = ms.id
      
      WHERE a.id = $1 
        AND ags.active = true 
        AND a.deleted_at IS NULL
        AND sm.active = true
      
      GROUP BY 
        a.id, a.name, a.instruction, a.model_config, a.vector_store_id,
        ags.name_code, ags.description, ags.is_supervisor, ags.active,
        sm.model_id, sm.provider
    `;
        const rawResults = await this.databaseService.query(query, [agentId]);
        if (rawResults.length === 0) {
            return null;
        }
        const configMap = this.buildSystemAgentConfigMap(rawResults);
        return configMap[agentId] || null;
    }
    buildSystemAgentConfigMap(rawResults) {
        const configMap = {};
        for (const row of rawResults) {
            const agentId = row.agent_id;
            const mcpConfig = [];
            if (row.mcp_server_names && row.mcp_configs) {
                for (let i = 0; i < row.mcp_server_names.length; i++) {
                    if (row.mcp_server_names[i] && row.mcp_configs[i]) {
                        mcpConfig.push({
                            serverName: row.mcp_server_names[i],
                            config: row.mcp_configs[i]
                        });
                    }
                }
            }
            const modelConfig = row.agent_model_config || {};
            configMap[agentId] = {
                id: agentId,
                name: row.agent_name,
                description: row.system_description || '',
                instruction: row.agent_instruction || '',
                mcpConfig,
                vectorStoreId: row.agent_vector_store_id || '',
                trimmingConfig: {
                    type: 'top_k',
                    threshold: modelConfig.trimmingThreshold || 10
                },
                model: {
                    name: row.model_id,
                    provider: this.mapProviderEnum(row.model_provider),
                    inputModalities: modelConfig.inputModalities || ['text'],
                    outputModalities: modelConfig.outputModalities || ['text'],
                    samplingParameters: modelConfig.samplingParameters || ['temperature', 'max_tokens'],
                    features: modelConfig.features || ['tool_call'],
                    parameters: {
                        temperature: modelConfig.temperature || 0.7,
                        topP: modelConfig.topP,
                        topK: modelConfig.topK,
                        maxTokens: modelConfig.maxTokens || 1000,
                        maxOutputTokens: modelConfig.maxOutputTokens
                    },
                    type: 'SYSTEM',
                    apiKeys: row.api_keys || []
                }
            };
        }
        return configMap;
    }
    mapProviderEnum(provider) {
        switch (provider?.toUpperCase()) {
            case 'OPENAI':
                return system_agent_config_interface_1.ModelProviderEnum.OPENAI;
            case 'XAI':
                return system_agent_config_interface_1.ModelProviderEnum.XAI;
            case 'ANTHROPIC':
                return system_agent_config_interface_1.ModelProviderEnum.ANTHROPIC;
            case 'GOOGLE':
                return system_agent_config_interface_1.ModelProviderEnum.GOOGLE;
            case 'DEEPSEEK':
                return system_agent_config_interface_1.ModelProviderEnum.DEEPSEEK;
            default:
                return system_agent_config_interface_1.ModelProviderEnum.OPENAI;
        }
    }
};
exports.AgentConfigQueries = AgentConfigQueries;
exports.AgentConfigQueries = AgentConfigQueries = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.ChatDatabaseService])
], AgentConfigQueries);
//# sourceMappingURL=agent-config.queries.js.map