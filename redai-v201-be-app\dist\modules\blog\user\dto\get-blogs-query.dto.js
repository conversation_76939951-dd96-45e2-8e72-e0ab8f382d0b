"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetBlogsQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const query_dto_1 = require("../../../../common/dto/query.dto");
const enums_1 = require("../../enums");
class GetBlogsQueryDto extends query_dto_1.QueryDto {
    status;
    author_type;
    tags;
}
exports.GetBlogsQueryDto = GetBlogsQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Lọc theo trạng thái',
        enum: enums_1.BlogStatusEnum,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(enums_1.BlogStatusEnum),
    __metadata("design:type", String)
], GetBlogsQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Lọc theo loại tác giả',
        enum: enums_1.AuthorTypeEnum,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(enums_1.AuthorTypeEnum),
    __metadata("design:type", String)
], GetBlogsQueryDto.prototype, "author_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Lọc theo tags',
        type: [String],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_transformer_1.Type)(() => String),
    __metadata("design:type", Array)
], GetBlogsQueryDto.prototype, "tags", void 0);
//# sourceMappingURL=get-blogs-query.dto.js.map