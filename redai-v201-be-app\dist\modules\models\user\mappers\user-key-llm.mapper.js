"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserKeyLlmMapper = void 0;
class UserKeyLlmMapper {
    static toResponseDto(entity) {
        return {
            id: entity.id,
            name: entity.name,
            provider: entity.provider,
            createdAt: entity.createdAt,
            updatedAt: entity.updatedAt,
        };
    }
    static toResponseDtoArray(entities) {
        return entities.map(entity => this.toResponseDto(entity));
    }
    static maskApiKey(encryptedApiKey) {
        if (!encryptedApiKey) {
            return '****';
        }
        if (encryptedApiKey.length <= 8) {
            return '****';
        }
        const start = encryptedApiKey.substring(0, 4);
        const end = encryptedApiKey.substring(encryptedApiKey.length - 4);
        const middle = '*'.repeat(Math.max(4, encryptedApiKey.length - 8));
        return `${start}${middle}${end}`;
    }
    static validateKeyName(name) {
        if (!name || name.trim().length === 0) {
            return false;
        }
        const dangerousChars = /[<>\"'&]/;
        if (dangerousChars.test(name)) {
            return false;
        }
        const trimmedName = name.trim();
        if (trimmedName.length < 2 || trimmedName.length > 255) {
            return false;
        }
        return true;
    }
    static toTestConnectionSuccessDto(responseTime) {
        return {
            success: true,
            responseTime,
        };
    }
    static toTestConnectionFailureDto(error) {
        return {
            success: false,
            error
        };
    }
    static normalizeProvider(provider) {
        return provider.toUpperCase().trim();
    }
    static getDefaultBaseUrl(provider) {
        switch (provider.toUpperCase()) {
            case 'OPENAI':
                return 'https://api.openai.com/v1';
            case 'ANTHROPIC':
                return 'https://api.anthropic.com';
            case 'GOOGLE':
                return 'https://generativelanguage.googleapis.com/v1';
            case 'XAI':
                return 'https://api.x.ai/v1';
            case 'DEEPSEEK':
                return 'https://api.deepseek.com/v1';
            case 'META':
                return 'https://api.llama-api.com/v1';
            default:
                return undefined;
        }
    }
    static isKeyExpired(metadata) {
        if (!metadata || !metadata.expiresAt) {
            return false;
        }
        const expiresAt = typeof metadata.expiresAt === 'number'
            ? metadata.expiresAt
            : Date.parse(metadata.expiresAt);
        return Date.now() > expiresAt;
    }
    static isKeyExpiringSoon(metadata) {
        if (!metadata || !metadata.expiresAt) {
            return false;
        }
        const expiresAt = typeof metadata.expiresAt === 'number'
            ? metadata.expiresAt
            : Date.parse(metadata.expiresAt);
        const sevenDaysFromNow = Date.now() + (7 * 24 * 60 * 60 * 1000);
        return expiresAt <= sevenDaysFromNow && expiresAt > Date.now();
    }
    static formatLastTestResult(lastTestResult) {
        if (!lastTestResult) {
            return 'Chưa test';
        }
        const { success, testedAt, responseTime, error } = lastTestResult;
        const testDate = new Date(testedAt).toLocaleString('vi-VN');
        if (success) {
            const timeStr = responseTime ? ` (${responseTime}ms)` : '';
            return `✅ Thành công${timeStr} - ${testDate}`;
        }
        else {
            return `❌ Thất bại: ${error || 'Unknown error'} - ${testDate}`;
        }
    }
}
exports.UserKeyLlmMapper = UserKeyLlmMapper;
//# sourceMappingURL=user-key-llm.mapper.js.map