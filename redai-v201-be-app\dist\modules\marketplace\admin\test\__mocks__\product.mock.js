"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mockCreateProductResult = exports.mockPaginatedProductResponseDto = exports.mockProductDetailResponseDto = exports.mockProductResponseDto = exports.mockProducts = exports.mockAdminProduct = exports.mockProduct = void 0;
const enums_1 = require("../../../enums");
exports.mockProduct = {
    id: 1,
    name: 'Sản phẩm test',
    description: 'Mô tả sản phẩm test',
    listedPrice: 1000,
    discountedPrice: 800,
    category: enums_1.ProductCategory.AGENT,
    status: enums_1.ProductStatus.APPROVED,
    userId: 1,
    employeeId: null,
    'user.id': 1,
    'user.full_name': 'Người dùng',
    'user.email': '<EMAIL>',
    employee: {},
    images: [
        { key: 'image1.jpg', position: 0 },
        { key: 'image2.jpg', position: 1 },
    ],
    userManual: 'manual.pdf',
    detail: 'detail.pdf',
    createdAt: 1625097600000,
    updatedAt: 1625097600000,
    sourceId: 'source-123',
};
exports.mockAdminProduct = {
    id: 2,
    name: 'Sản phẩm admin',
    description: 'Mô tả sản phẩm admin',
    listedPrice: 2000,
    discountedPrice: 1800,
    category: enums_1.ProductCategory.FUNCTION,
    status: enums_1.ProductStatus.APPROVED,
    userId: null,
    employeeId: 1,
    'user.id': null,
    'employee.id': 1,
    'employee.full_name': 'Admin',
    'employee.email': '<EMAIL>',
    images: [
        { key: 'admin-image1.jpg', position: 0 },
    ],
    userManual: 'admin-manual.pdf',
    detail: 'admin-detail.pdf',
    createdAt: 1625097600000,
    updatedAt: 1625097600000,
    sourceId: 'admin-source-123',
};
exports.mockProducts = [
    exports.mockProduct,
    exports.mockAdminProduct,
];
exports.mockProductResponseDto = {
    id: 1,
    name: 'Sản phẩm test',
    description: 'Mô tả sản phẩm test',
    listedPrice: 1000,
    discountedPrice: 800,
    category: enums_1.ProductCategory.AGENT,
    status: enums_1.ProductStatus.APPROVED,
    seller: {
        id: 1,
        name: 'Người dùng',
        avatar: null,
        type: 'user',
    },
    images: ['https://example.com/image1.jpg'],
    createdAt: 1625097600000,
    updatedAt: 1625097600000,
    soldCount: 25,
    canPurchase: true,
};
exports.mockProductDetailResponseDto = {
    id: 1,
    name: 'Sản phẩm test',
    description: 'Mô tả sản phẩm test',
    listedPrice: 1000,
    discountedPrice: 800,
    category: enums_1.ProductCategory.AGENT,
    status: enums_1.ProductStatus.APPROVED,
    seller: {
        id: 1,
        name: 'Người dùng',
        avatar: null,
        type: 'user',
    },
    images: [
        'https://example.com/image1.jpg',
        'https://example.com/image2.jpg',
    ],
    userManual: 'https://example.com/manual.pdf',
    detail: 'https://example.com/detail.pdf',
    createdAt: 1625097600000,
    updatedAt: 1625097600000,
    sourceId: 'source-123',
    soldCount: 25,
    canPurchase: true,
};
exports.mockPaginatedProductResponseDto = {
    items: [exports.mockProductResponseDto],
    meta: {
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
    },
};
exports.mockCreateProductResult = {
    product: exports.mockProductDetailResponseDto,
    uploadUrls: {
        productId: '1',
        imagesUploadUrls: [
            {
                url: 'https://example.com/upload/image1.jpg',
                key: 'marketplace/IMAGE/2023/01/01/product-image-0-123456789-1',
                index: 0
            }
        ],
        userManualUploadUrl: 'https://example.com/upload/manual.pdf',
        detailUploadUrl: 'https://example.com/upload/detail.pdf'
    }
};
//# sourceMappingURL=product.mock.js.map