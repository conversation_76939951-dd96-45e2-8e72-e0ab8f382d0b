"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GENERIC_PAGE_ERROR_CODES = void 0;
const common_1 = require("@nestjs/common");
const common_2 = require("../../../common");
exports.GENERIC_PAGE_ERROR_CODES = {
    GENERIC_PAGE_NOT_FOUND: new common_2.ErrorCode(40000, 'Không tìm thấy trang', common_1.HttpStatus.NOT_FOUND),
    GENERIC_PAGE_TEMPLATE_NOT_FOUND: new common_2.ErrorCode(40001, 'Không tìm thấy mẫu trang', common_1.HttpStatus.NOT_FOUND),
    GENERIC_PAGE_SUBMISSION_NOT_FOUND: new common_2.ErrorCode(40002, 'Không tìm thấy dữ liệu gử<PERSON>', common_1.HttpStatus.NOT_FOUND),
    GENERIC_PAGE_CREATE_ERROR: new common_2.ErrorCode(40010, 'Lỗi khi tạo trang', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    GENERIC_PAGE_UPDATE_ERROR: new common_2.ErrorCode(40011, 'Lỗi khi cập nhật trang', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    GENERIC_PAGE_DELETE_ERROR: new common_2.ErrorCode(40012, 'Lỗi khi xóa trang', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    GENERIC_PAGE_TEMPLATE_CREATE_ERROR: new common_2.ErrorCode(40013, 'Lỗi khi tạo mẫu trang', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    GENERIC_PAGE_TEMPLATE_UPDATE_ERROR: new common_2.ErrorCode(40014, 'Lỗi khi cập nhật mẫu trang', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    GENERIC_PAGE_TEMPLATE_DELETE_ERROR: new common_2.ErrorCode(40015, 'Lỗi khi xóa mẫu trang', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    GENERIC_PAGE_SUBMISSION_CREATE_ERROR: new common_2.ErrorCode(40016, 'Lỗi khi gửi dữ liệu form', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    GENERIC_PAGE_PATH_ALREADY_EXISTS: new common_2.ErrorCode(40020, 'Đường dẫn đã tồn tại', common_1.HttpStatus.BAD_REQUEST),
    GENERIC_PAGE_INVALID_CONFIG: new common_2.ErrorCode(40021, 'Cấu hình trang không hợp lệ', common_1.HttpStatus.BAD_REQUEST),
    GENERIC_PAGE_INVALID_STATUS: new common_2.ErrorCode(40022, 'Trạng thái trang không hợp lệ', common_1.HttpStatus.BAD_REQUEST),
    GENERIC_PAGE_TEMPLATE_INVALID_CONFIG: new common_2.ErrorCode(40023, 'Cấu hình mẫu trang không hợp lệ', common_1.HttpStatus.BAD_REQUEST),
    GENERIC_PAGE_SUBMISSION_INVALID_DATA: new common_2.ErrorCode(40024, 'Dữ liệu gửi không hợp lệ', common_1.HttpStatus.BAD_REQUEST),
    GENERIC_PAGE_ALREADY_PUBLISHED: new common_2.ErrorCode(40030, 'Trang đã được xuất bản', common_1.HttpStatus.BAD_REQUEST),
    GENERIC_PAGE_NOT_PUBLISHED: new common_2.ErrorCode(40031, 'Trang chưa được xuất bản', common_1.HttpStatus.BAD_REQUEST),
    GENERIC_PAGE_ALREADY_ARCHIVED: new common_2.ErrorCode(40032, 'Trang đã được lưu trữ', common_1.HttpStatus.BAD_REQUEST),
};
//# sourceMappingURL=generic-page-error.code.js.map