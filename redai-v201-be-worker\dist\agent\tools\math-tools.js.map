{"version": 3, "sources": ["../../../src/agent/tools/math-tools.ts"], "sourcesContent": ["import { tool } from '@langchain/core/tools';\r\nimport { z } from 'zod';\r\n\r\nexport const additionTool = tool(\r\n  async ({ a, b }) => {\r\n    return `${a} + ${b} = ${a + b}`;\r\n  },\r\n  {\r\n    name: 'addition',\r\n    description: 'Add two numbers together',\r\n    schema: z.object({\r\n      a: z.number().describe('The first number'),\r\n      b: z.number().describe('The second number'),\r\n    }),\r\n  },\r\n);\r\n\r\nexport const subtractionTool = tool(\r\n  async ({ a, b }) => {\r\n    return `${a} - ${b} = ${a - b}`;\r\n  },\r\n  {\r\n    name: 'subtraction',\r\n    description: 'Subtract the second number from the first',\r\n    schema: z.object({\r\n      a: z.number().describe('The first number'),\r\n      b: z.number().describe('The second number'),\r\n    }),\r\n  },\r\n);\r\n\r\nexport const multiplicationTool = tool(\r\n  async ({ a, b }) => {\r\n    return `${a} * ${b} = ${a * b}`;\r\n  },\r\n  {\r\n    name: 'multiplication',\r\n    description: 'Multiply two numbers together',\r\n    schema: z.object({\r\n      a: z.number().describe('The first number'),\r\n      b: z.number().describe('The second number'),\r\n    }),\r\n  },\r\n);\r\n\r\nexport const divisionTool = tool(\r\n  async ({ a, b }) => {\r\n    if (b === 0) {\r\n      return 'Error: Division by zero is not allowed';\r\n    }\r\n    return `${a} / ${b} = ${a / b}`;\r\n  },\r\n  {\r\n    name: 'division',\r\n    description: 'Divide the first number by the second',\r\n    schema: z.object({\r\n      a: z.number().describe('The first number'),\r\n      b: z.number().describe('The second number (cannot be zero)'),\r\n    }),\r\n  },\r\n);\r\n\r\nexport const modularTool = tool(\r\n  async ({ a, b }) => {\r\n    if (b === 0) {\r\n      return 'Error: Modulo by zero is not allowed';\r\n    }\r\n    return `${a} % ${b} = ${a % b}`;\r\n  },\r\n  {\r\n    name: 'modular',\r\n    description:\r\n      'Calculate the remainder when the first number is divided by the second',\r\n    schema: z.object({\r\n      a: z.number().describe('The first number'),\r\n      b: z.number().describe('The second number (cannot be zero)'),\r\n    }),\r\n  },\r\n);\r\n\r\nexport const mathTools = {\r\n  addition: additionTool,\r\n  subtraction: subtractionTool,\r\n  multiplication: multiplicationTool,\r\n  division: divisionTool,\r\n  modular: modularTool,\r\n};\r\n"], "names": ["additionTool", "divisionTool", "mathTools", "modularTool", "multiplicationTool", "subtractionTool", "tool", "a", "b", "name", "description", "schema", "z", "object", "number", "describe", "addition", "subtraction", "multiplication", "division", "modular"], "mappings": ";;;;;;;;;;;QAGaA;eAAAA;;QA0CAC;eAAAA;;QAmCAC;eAAAA;;QAlBAC;eAAAA;;QA/BAC;eAAAA;;QAdAC;eAAAA;;;uBAjBQ;qBACH;AAEX,MAAML,eAAeM,IAAAA,WAAI,EAC9B,OAAO,EAAEC,CAAC,EAAEC,CAAC,EAAE;IACb,OAAO,GAAGD,EAAE,GAAG,EAAEC,EAAE,GAAG,EAAED,IAAIC,GAAG;AACjC,GACA;IACEC,MAAM;IACNC,aAAa;IACbC,QAAQC,MAAC,CAACC,MAAM,CAAC;QACfN,GAAGK,MAAC,CAACE,MAAM,GAAGC,QAAQ,CAAC;QACvBP,GAAGI,MAAC,CAACE,MAAM,GAAGC,QAAQ,CAAC;IACzB;AACF;AAGK,MAAMV,kBAAkBC,IAAAA,WAAI,EACjC,OAAO,EAAEC,CAAC,EAAEC,CAAC,EAAE;IACb,OAAO,GAAGD,EAAE,GAAG,EAAEC,EAAE,GAAG,EAAED,IAAIC,GAAG;AACjC,GACA;IACEC,MAAM;IACNC,aAAa;IACbC,QAAQC,MAAC,CAACC,MAAM,CAAC;QACfN,GAAGK,MAAC,CAACE,MAAM,GAAGC,QAAQ,CAAC;QACvBP,GAAGI,MAAC,CAACE,MAAM,GAAGC,QAAQ,CAAC;IACzB;AACF;AAGK,MAAMX,qBAAqBE,IAAAA,WAAI,EACpC,OAAO,EAAEC,CAAC,EAAEC,CAAC,EAAE;IACb,OAAO,GAAGD,EAAE,GAAG,EAAEC,EAAE,GAAG,EAAED,IAAIC,GAAG;AACjC,GACA;IACEC,MAAM;IACNC,aAAa;IACbC,QAAQC,MAAC,CAACC,MAAM,CAAC;QACfN,GAAGK,MAAC,CAACE,MAAM,GAAGC,QAAQ,CAAC;QACvBP,GAAGI,MAAC,CAACE,MAAM,GAAGC,QAAQ,CAAC;IACzB;AACF;AAGK,MAAMd,eAAeK,IAAAA,WAAI,EAC9B,OAAO,EAAEC,CAAC,EAAEC,CAAC,EAAE;IACb,IAAIA,MAAM,GAAG;QACX,OAAO;IACT;IACA,OAAO,GAAGD,EAAE,GAAG,EAAEC,EAAE,GAAG,EAAED,IAAIC,GAAG;AACjC,GACA;IACEC,MAAM;IACNC,aAAa;IACbC,QAAQC,MAAC,CAACC,MAAM,CAAC;QACfN,GAAGK,MAAC,CAACE,MAAM,GAAGC,QAAQ,CAAC;QACvBP,GAAGI,MAAC,CAACE,MAAM,GAAGC,QAAQ,CAAC;IACzB;AACF;AAGK,MAAMZ,cAAcG,IAAAA,WAAI,EAC7B,OAAO,EAAEC,CAAC,EAAEC,CAAC,EAAE;IACb,IAAIA,MAAM,GAAG;QACX,OAAO;IACT;IACA,OAAO,GAAGD,EAAE,GAAG,EAAEC,EAAE,GAAG,EAAED,IAAIC,GAAG;AACjC,GACA;IACEC,MAAM;IACNC,aACE;IACFC,QAAQC,MAAC,CAACC,MAAM,CAAC;QACfN,GAAGK,MAAC,CAACE,MAAM,GAAGC,QAAQ,CAAC;QACvBP,GAAGI,MAAC,CAACE,MAAM,GAAGC,QAAQ,CAAC;IACzB;AACF;AAGK,MAAMb,YAAY;IACvBc,UAAUhB;IACViB,aAAaZ;IACba,gBAAgBd;IAChBe,UAAUlB;IACVmB,SAASjB;AACX"}