"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericPageTemplateAdminController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const guards_1 = require("../../../auth/guards");
const current_employee_decorator_1 = require("../../../auth/decorators/current-employee.decorator");
const response_1 = require("../../../../common/response");
const services_1 = require("../services");
const dto_1 = require("../dto");
let GenericPageTemplateAdminController = class GenericPageTemplateAdminController {
    genericPageTemplateAdminService;
    constructor(genericPageTemplateAdminService) {
        this.genericPageTemplateAdminService = genericPageTemplateAdminService;
    }
    async createGenericPageTemplate(createGenericPageTemplateDto, employee) {
        const result = await this.genericPageTemplateAdminService.createGenericPageTemplate(createGenericPageTemplateDto, String(employee.id));
        return response_1.ApiResponseDto.created(result);
    }
    async updateGenericPageTemplate(id, updateGenericPageTemplateDto, employee) {
        const result = await this.genericPageTemplateAdminService.updateGenericPageTemplate(id, updateGenericPageTemplateDto, String(employee.id));
        return response_1.ApiResponseDto.success(result);
    }
    async getGenericPageTemplateById(id) {
        const result = await this.genericPageTemplateAdminService.getGenericPageTemplateById(id);
        return response_1.ApiResponseDto.success(result);
    }
    async deleteGenericPageTemplate(id) {
        await this.genericPageTemplateAdminService.deleteGenericPageTemplate(id);
        return response_1.ApiResponseDto.success(null, 'Mẫu trang đã được xóa thành công');
    }
};
exports.GenericPageTemplateAdminController = GenericPageTemplateAdminController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo mẫu trang mới' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Mẫu trang đã được tạo',
        schema: response_1.ApiResponseDto.getSchema(dto_1.GenericPageTemplateResponseDto),
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_employee_decorator_1.CurrentEmployee)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateGenericPageTemplateDto, Object]),
    __metadata("design:returntype", Promise)
], GenericPageTemplateAdminController.prototype, "createGenericPageTemplate", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật mẫu trang' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID của mẫu trang' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Mẫu trang đã được cập nhật',
        schema: response_1.ApiResponseDto.getSchema(dto_1.GenericPageTemplateResponseDto),
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_employee_decorator_1.CurrentEmployee)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateGenericPageTemplateDto, Object]),
    __metadata("design:returntype", Promise)
], GenericPageTemplateAdminController.prototype, "updateGenericPageTemplate", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thông tin mẫu trang theo ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID của mẫu trang' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Thông tin mẫu trang',
        schema: response_1.ApiResponseDto.getSchema(dto_1.GenericPageTemplateResponseDto),
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], GenericPageTemplateAdminController.prototype, "getGenericPageTemplateById", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Xóa mẫu trang' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID của mẫu trang' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Mẫu trang đã được xóa',
        schema: response_1.ApiResponseDto.getSchema(null),
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], GenericPageTemplateAdminController.prototype, "deleteGenericPageTemplate", null);
exports.GenericPageTemplateAdminController = GenericPageTemplateAdminController = __decorate([
    (0, swagger_1.ApiTags)('Admin Generic Page Template'),
    (0, common_1.Controller)('admin/generic-page-templates'),
    (0, common_1.UseGuards)(guards_1.JwtEmployeeGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiExtraModels)(response_1.ApiResponseDto, dto_1.GenericPageTemplateResponseDto, response_1.PaginatedResult),
    __metadata("design:paramtypes", [services_1.GenericPageTemplateAdminService])
], GenericPageTemplateAdminController);
//# sourceMappingURL=generic-page-template-admin.controller.js.map