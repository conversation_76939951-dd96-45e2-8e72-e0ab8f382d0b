"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiResponse = exports.SortDirection = void 0;
var SortDirection;
(function (SortDirection) {
    SortDirection["ASC"] = "ASC";
    SortDirection["DESC"] = "DESC";
})(SortDirection || (exports.SortDirection = SortDirection = {}));
class ApiResponse {
    code;
    message;
    result;
    constructor(code, message, result) {
        this.code = code;
        this.message = message;
        this.result = result;
    }
    static success(data, message = 'Success') {
        return new ApiResponse(200, message, data);
    }
    static created(data, message = 'Created successfully') {
        return new ApiResponse(201, message, data);
    }
    static error(message, code = 400) {
        return new ApiResponse(code, message, null);
    }
}
exports.ApiResponse = ApiResponse;
//# sourceMappingURL=common-interfaces.mock.js.map