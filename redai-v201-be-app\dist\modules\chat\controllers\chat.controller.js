"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatController = void 0;
const jwt_user_guard_1 = require("../../auth/guards/jwt-user.guard");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const current_user_decorator_1 = require("../../auth/decorators/current-user.decorator");
const chat_service_1 = require("../services/chat.service");
const message_request_dto_1 = require("../dto/message-request.dto");
const message_response_dto_1 = require("../dto/message-response.dto");
const response_1 = require("../../../common/response");
let ChatController = class ChatController {
    chatService;
    constructor(chatService) {
        this.chatService = chatService;
    }
    async testPubSub(messageRequest) {
        const testUserId = 1;
        const testJwt = 'test-jwt-token';
        return this.chatService.processMessage(messageRequest, testUserId, testJwt);
    }
    async sendMessage(userId, messageRequest, request) {
        const authHeader = request.headers.authorization;
        const jwt = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : '';
        return response_1.ApiResponseDto.created(await this.chatService.processMessage(messageRequest, userId, jwt));
    }
    async cancelRun(runId) {
        const success = await this.chatService.cancelRun(runId);
        return {
            success,
            message: success ? 'Run cancelled successfully' : 'Failed to cancel run',
            runId,
        };
    }
    async getAgentConfigSummary() {
        return this.chatService.getAgentConfigSummary();
    }
    async getRedisHealth() {
        const isHealthy = await this.chatService.getRedisHealth();
        return {
            healthy: isHealthy,
            status: isHealthy ? 'connected' : 'disconnected',
            timestamp: Date.now(),
        };
    }
};
exports.ChatController = ChatController;
__decorate([
    (0, common_1.Post)('test-pubsub'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({
        summary: 'Test pub/sub communication (no auth)',
        description: 'Test endpoint to verify Redis pub/sub communication without authentication'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [message_request_dto_1.MessageRequestDto]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "testPubSub", null);
__decorate([
    (0, common_1.Post)('message'),
    (0, common_1.UseGuards)(jwt_user_guard_1.JwtUserGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({
        summary: 'Send message to agent',
        description: 'Send a message to a specific agent and create a run for processing. The agent will process the message asynchronously.'
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Message sent successfully and run created',
        schema: response_1.ApiResponseDto.getSchema(message_response_dto_1.MessageResponseDto),
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Invalid request data or agent not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Unauthorized - valid JWT token required',
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, message_request_dto_1.MessageRequestDto, Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "sendMessage", null);
__decorate([
    (0, common_1.Delete)('runs/:runId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiParam)({
        name: 'runId',
        description: 'ID of the run to cancel',
        example: 'run_123456-789-abc'
    }),
    (0, swagger_1.ApiOperation)({
        summary: 'Cancel a run',
        description: 'Cancel a running or pending run. This will stop processing and mark the run as failed.'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Run cancelled successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Run cancelled successfully' },
                runId: { type: 'string', example: 'run_123456-789-abc' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Run not found or already completed',
    }),
    __param(0, (0, common_1.Param)('runId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "cancelRun", null);
__decorate([
    (0, common_1.Post)('debug/agents'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get agent configuration summary',
        description: 'Get a summary of all available agent configurations for debugging purposes.'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Agent configuration summary retrieved successfully',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getAgentConfigSummary", null);
__decorate([
    (0, common_1.Post)('debug/redis'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get Redis health status',
        description: 'Check if Redis connection is healthy for pub/sub communication.'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Redis health status retrieved successfully',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getRedisHealth", null);
exports.ChatController = ChatController = __decorate([
    (0, swagger_1.ApiTags)('Chat'),
    (0, common_1.Controller)('chat'),
    __metadata("design:paramtypes", [chat_service_1.ChatService])
], ChatController);
//# sourceMappingURL=chat.controller.js.map