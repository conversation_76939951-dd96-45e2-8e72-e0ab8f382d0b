"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RuleContractExtendedResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const rule_contract_entity_1 = require("../../entities/rule-contract.entity");
class RuleContractExtendedResponseDto {
    status;
    type;
    contractBase64;
    contractUrl;
}
exports.RuleContractExtendedResponseDto = RuleContractExtendedResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái hợp đồng',
        enum: rule_contract_entity_1.ContractStatusEnum,
        example: rule_contract_entity_1.ContractStatusEnum.DRAFT,
    }),
    __metadata("design:type", String)
], RuleContractExtendedResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại hợp đồng',
        enum: rule_contract_entity_1.ContractTypeEnum,
        example: rule_contract_entity_1.ContractTypeEnum.INDIVIDUAL,
    }),
    __metadata("design:type", String)
], RuleContractExtendedResponseDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nội dung hợp đồng dạng Base64',
        example: 'JVBERi0xLjcKJeLjz9MKNSAwIG9iago8PC9GaWx0ZXIvRmxhdGVEZWNvZGUvTGVuZ3RoIDM...',
        type: String,
    }),
    __metadata("design:type", String)
], RuleContractExtendedResponseDto.prototype, "contractBase64", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'URL tải hợp đồng',
        example: 'https://cdn.example.com/contracts/contract-123.pdf',
        type: String,
    }),
    __metadata("design:type", String)
], RuleContractExtendedResponseDto.prototype, "contractUrl", void 0);
//# sourceMappingURL=rule-contract-response.dto.extended.js.map