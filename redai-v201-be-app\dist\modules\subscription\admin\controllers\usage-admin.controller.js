"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsageAdminController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const usage_admin_service_1 = require("../services/usage-admin.service");
const jwt_user_guard_1 = require("../../../auth/guards/jwt-user.guard");
const roles_guard_1 = require("../../../auth/guards/roles.guard");
const roles_decorator_1 = require("../../../auth/decorators/roles.decorator");
let UsageAdminController = class UsageAdminController {
    usageAdminService;
    constructor(usageAdminService) {
        this.usageAdminService = usageAdminService;
    }
};
exports.UsageAdminController = UsageAdminController;
exports.UsageAdminController = UsageAdminController = __decorate([
    (0, swagger_1.ApiTags)('Subscription - Admin Usage'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_user_guard_1.JwtUserGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('ADMIN'),
    (0, common_1.Controller)('admin/subscription/usage'),
    __metadata("design:paramtypes", [usage_admin_service_1.UsageAdminService])
], UsageAdminController);
//# sourceMappingURL=usage-admin.controller.js.map