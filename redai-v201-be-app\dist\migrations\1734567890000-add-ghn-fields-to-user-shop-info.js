"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddGhnFieldsToUserShopInfo1734567890000 = void 0;
class AddGhnFieldsToUserShopInfo1734567890000 {
    name = 'AddGhnFieldsToUserShopInfo1734567890000';
    async up(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE "user_shop_info" 
      ADD COLUMN "ghn_district_id" integer NULL COMMENT 'District ID cho GHN API'
    `);
        await queryRunner.query(`
      ALTER TABLE "user_shop_info" 
      ADD COLUMN "ghn_ward_code" varchar(20) NULL COMMENT 'Ward Code cho GHN API'
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE "user_shop_info" 
      DROP COLUMN "ghn_ward_code"
    `);
        await queryRunner.query(`
      ALTER TABLE "user_shop_info" 
      DROP COLUMN "ghn_district_id"
    `);
    }
}
exports.AddGhnFieldsToUserShopInfo1734567890000 = AddGhnFieldsToUserShopInfo1734567890000;
//# sourceMappingURL=1734567890000-add-ghn-fields-to-user-shop-info.js.map