"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mockFoldersPaginatedResult = exports.mockSubFolderDetailResponseDto = exports.mockFolderDetailResponseDto = exports.mockFolderResponseDto = exports.mockFolders = exports.mockFolder = void 0;
exports.mockFolder = {
    id: 1,
    name: 'Documents',
    parentId: null,
    userId: 1,
    path: '/Documents',
    root: null,
    createdAt: 1625097600000,
    updatedAt: 1625097600000,
};
exports.mockFolders = [
    exports.mockFolder,
    {
        id: 2,
        name: 'Images',
        parentId: null,
        userId: 1,
        path: '/Images',
        root: null,
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
    },
    {
        id: 3,
        name: 'Reports',
        parentId: 1,
        userId: 1,
        path: '/Documents/Reports',
        root: null,
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
    },
];
exports.mockFolderResponseDto = {
    id: 1,
    name: 'Documents',
    parentId: null,
    userId: 1,
    path: '/Documents',
    root: null,
    createdAt: 1625097600000,
    updatedAt: 1625097600000,
};
exports.mockFolderDetailResponseDto = {
    id: 1,
    name: 'Documents',
    parent: null,
    owner: {
        id: 1,
        name: 'User 1',
    },
    path: '/Documents',
    root: null,
    fileCount: 2,
    files: [
        {
            id: 1,
            name: 'test-file.pdf',
            folderId: 1,
            size: 1024000,
            createdAt: 1625097600000,
            updatedAt: 1625097600000,
        },
        {
            id: 2,
            name: 'document.docx',
            folderId: 1,
            size: 512000,
            createdAt: 1625097600000,
            updatedAt: 1625097600000,
        },
    ],
    createdAt: 1625097600000,
    formattedCreatedAt: '01/07/2021 00:00:00',
    updatedAt: 1625097600000,
    formattedUpdatedAt: '01/07/2021 00:00:00',
};
exports.mockSubFolderDetailResponseDto = {
    id: 3,
    name: 'Reports',
    parent: {
        id: 1,
        name: 'Documents',
        path: '/Documents',
    },
    owner: {
        id: 1,
        name: 'User 1',
    },
    path: '/Documents/Reports',
    root: null,
    fileCount: 1,
    files: [
        {
            id: 4,
            name: 'report.pdf',
            folderId: 3,
            size: 1024000,
            createdAt: 1625097600000,
            updatedAt: 1625097600000,
        },
    ],
    createdAt: 1625097600000,
    formattedCreatedAt: '01/07/2021 00:00:00',
    updatedAt: 1625097600000,
    formattedUpdatedAt: '01/07/2021 00:00:00',
};
exports.mockFoldersPaginatedResult = {
    items: [
        exports.mockFolderResponseDto,
        {
            id: 2,
            name: 'Images',
            parentId: null,
            userId: 1,
            path: '/Images',
            root: null,
            createdAt: 1625097600000,
            updatedAt: 1625097600000,
        },
    ],
    meta: {
        currentPage: 1,
        itemsPerPage: 10,
        itemCount: 2,
        totalItems: 2,
        totalPages: 1,
    },
};
//# sourceMappingURL=folder.mock.js.map