"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserTask = void 0;
const typeorm_1 = require("typeorm");
const task_status_enum_1 = require("../enums/task-status.enum");
let UserTask = class UserTask {
    taskId;
    userId;
    agentId;
    taskName;
    taskDescription;
    status;
    active;
    createdAt;
    updatedAt;
    deletedAt;
};
exports.UserTask = UserTask;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid', { name: 'task_id' }),
    __metadata("design:type", String)
], UserTask.prototype, "taskId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id', type: 'int', nullable: false }),
    __metadata("design:type", Number)
], UserTask.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'agent_id', type: 'uuid', nullable: false }),
    __metadata("design:type", String)
], UserTask.prototype, "agentId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'task_name', type: 'varchar', length: 255, nullable: false }),
    __metadata("design:type", String)
], UserTask.prototype, "taskName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'task_description', type: 'text', nullable: true }),
    __metadata("design:type", String)
], UserTask.prototype, "taskDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'status',
        type: 'enum',
        enum: task_status_enum_1.TaskStatus,
        default: task_status_enum_1.TaskStatus.PENDING,
    }),
    __metadata("design:type", String)
], UserTask.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'active', type: 'boolean', default: true }),
    __metadata("design:type", Boolean)
], UserTask.prototype, "active", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'created_at',
        type: 'bigint',
        default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    }),
    __metadata("design:type", Number)
], UserTask.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'updated_at',
        type: 'bigint',
        default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    }),
    __metadata("design:type", Number)
], UserTask.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'deleted_at', type: 'bigint', nullable: true }),
    __metadata("design:type", Number)
], UserTask.prototype, "deletedAt", void 0);
exports.UserTask = UserTask = __decorate([
    (0, typeorm_1.Entity)('user_tasks')
], UserTask);
//# sourceMappingURL=user-task.entity.js.map