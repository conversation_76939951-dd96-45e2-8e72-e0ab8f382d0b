"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentStrategyUser = void 0;
const typeorm_1 = require("typeorm");
let AgentStrategyUser = class AgentStrategyUser {
    id;
    agentsStrategyId;
    example;
    userId;
    ownedAt;
};
exports.AgentStrategyUser = AgentStrategyUser;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AgentStrategyUser.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'agents_strategy_id', type: 'uuid', nullable: true }),
    __metadata("design:type", Object)
], AgentStrategyUser.prototype, "agentsStrategyId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'example', type: 'jsonb', default: '[]' }),
    __metadata("design:type", Array)
], AgentStrategyUser.prototype, "example", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id', type: 'integer', nullable: true }),
    __metadata("design:type", Object)
], AgentStrategyUser.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'owned_at',
        type: 'bigint',
        default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
    }),
    __metadata("design:type", Number)
], AgentStrategyUser.prototype, "ownedAt", void 0);
exports.AgentStrategyUser = AgentStrategyUser = __decorate([
    (0, typeorm_1.Entity)('agents_strategy_user')
], AgentStrategyUser);
//# sourceMappingURL=agents-strategy-user.entity.js.map