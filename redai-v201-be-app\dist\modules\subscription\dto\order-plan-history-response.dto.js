"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderPlanHistoryResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const order_status_enum_1 = require("../enums/order-status.enum");
class OrderPlanHistoryResponseDto {
    id;
    userId;
    planId;
    planPricingId;
    subscriptionId;
    planName;
    point;
    billingCycle;
    usageLimit;
    usageUnit;
    status;
    createdAt;
    isExtension;
}
exports.OrderPlanHistoryResponseDto = OrderPlanHistoryResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của đơn hàng',
        example: 1
    }),
    __metadata("design:type", Number)
], OrderPlanHistoryResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của người dùng',
        example: 10
    }),
    __metadata("design:type", Number)
], OrderPlanHistoryResponseDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của gói dịch vụ',
        example: 1
    }),
    __metadata("design:type", Number)
], OrderPlanHistoryResponseDto.prototype, "planId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của tùy chọn giá',
        example: 1
    }),
    __metadata("design:type", Number)
], OrderPlanHistoryResponseDto.prototype, "planPricingId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của đăng ký',
        example: 1,
        nullable: true
    }),
    __metadata("design:type", Object)
], OrderPlanHistoryResponseDto.prototype, "subscriptionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên gói dịch vụ',
        example: 'Basic Plan'
    }),
    __metadata("design:type", String)
], OrderPlanHistoryResponseDto.prototype, "planName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số điểm đã sử dụng',
        example: '100'
    }),
    __metadata("design:type", String)
], OrderPlanHistoryResponseDto.prototype, "point", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Chu kỳ thanh toán',
        example: 'MONTHLY'
    }),
    __metadata("design:type", String)
], OrderPlanHistoryResponseDto.prototype, "billingCycle", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Giới hạn sử dụng',
        example: '1000',
        nullable: true
    }),
    __metadata("design:type", Object)
], OrderPlanHistoryResponseDto.prototype, "usageLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Đơn vị sử dụng',
        example: 'API_CALLS',
        nullable: true
    }),
    __metadata("design:type", Object)
], OrderPlanHistoryResponseDto.prototype, "usageUnit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái đơn hàng',
        example: 'COMPLETED',
        enum: order_status_enum_1.OrderStatus
    }),
    __metadata("design:type", String)
], OrderPlanHistoryResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời điểm tạo',
        example: '1625097600000'
    }),
    __metadata("design:type", String)
], OrderPlanHistoryResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Đánh dấu đây là gia hạn',
        example: false
    }),
    __metadata("design:type", Boolean)
], OrderPlanHistoryResponseDto.prototype, "isExtension", void 0);
//# sourceMappingURL=order-plan-history-response.dto.js.map