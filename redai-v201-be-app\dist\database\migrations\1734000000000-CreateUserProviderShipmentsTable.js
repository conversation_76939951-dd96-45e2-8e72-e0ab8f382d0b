"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUserProviderShipmentsTable1734000000000 = void 0;
const typeorm_1 = require("typeorm");
class CreateUserProviderShipmentsTable1734000000000 {
    name = 'CreateUserProviderShipmentsTable1734000000000';
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE TYPE "provider_shipment_type" AS ENUM ('GHN', 'GHTK', 'AHAMOVE', 'JT')
    `);
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'user_provider_shipments',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()',
                },
                {
                    name: 'user_id',
                    type: 'int',
                    isNullable: false,
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                },
                {
                    name: 'key',
                    type: 'text',
                    isNullable: false,
                },
                {
                    name: 'type',
                    type: 'enum',
                    enum: ['GHN', 'GHTK', 'AHAMOVE', 'JT'],
                    enumName: 'provider_shipment_type',
                    isNullable: false,
                },
                {
                    name: 'created_at',
                    type: 'bigint',
                    default: '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
                },
            ],
            foreignKeys: [
                {
                    columnNames: ['user_id'],
                    referencedTableName: 'users',
                    referencedColumnNames: ['id'],
                    onDelete: 'CASCADE',
                },
            ],
            indices: [
                {
                    name: 'idx_user_provider_shipments_user_id',
                    columnNames: ['user_id'],
                },
                {
                    name: 'idx_user_provider_shipments_type',
                    columnNames: ['type'],
                },
                {
                    name: 'idx_user_provider_shipments_user_type',
                    columnNames: ['user_id', 'type'],
                },
                {
                    name: 'idx_user_provider_shipments_created_at',
                    columnNames: ['created_at'],
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable('user_provider_shipments');
        await queryRunner.query(`DROP TYPE "provider_shipment_type"`);
    }
}
exports.CreateUserProviderShipmentsTable1734000000000 = CreateUserProviderShipmentsTable1734000000000;
//# sourceMappingURL=1734000000000-CreateUserProviderShipmentsTable.js.map