"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.STRATEGY_ERROR_CODES = void 0;
const common_1 = require("@nestjs/common");
const exceptions_1 = require("../../../common/exceptions");
exports.STRATEGY_ERROR_CODES = {
    STRATEGY_NOT_FOUND: new exceptions_1.ErrorCode(10350, 'Không tìm thấy chiến lược', common_1.HttpStatus.NOT_FOUND),
    STRATEGY_VERSION_NOT_FOUND: new exceptions_1.ErrorCode(10351, 'Không tìm thấy phiên bản chiến lượ<PERSON>', common_1.HttpStatus.NOT_FOUND),
    STRATEGY_ACCESS_DENIED: new exceptions_1.ErrorCode(10352, 'Không có quyền truy cập chiến lượ<PERSON>', common_1.HttpStatus.FORBIDDEN),
    STRATEGY_ASSIGN_FAILED: new exceptions_1.ErrorCode(10353, '<PERSON>hông thể gán chiến lược cho agent', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    STRATEGY_REMOVE_FAILED: new exceptions_1.ErrorCode(10354, 'Không thể gỡ chiến lược khỏi agent', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    STRATEGY_NOT_ASSIGNED: new exceptions_1.ErrorCode(10355, 'Agent chưa được gán chiến lược', common_1.HttpStatus.NOT_FOUND),
    STRATEGY_NO_VERSIONS: new exceptions_1.ErrorCode(10356, 'Chiến lược không có phiên bản nào', common_1.HttpStatus.NOT_FOUND),
    STRATEGY_FETCH_FAILED: new exceptions_1.ErrorCode(10357, 'Không thể truy vấn thông tin chiến lược', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
};
//# sourceMappingURL=strategy-error.code.js.map