"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "createHandoffTool", {
    enumerable: true,
    get: function() {
        return createHandoffTool;
    }
});
const _zod = require("zod");
const _tools = require("@langchain/core/tools");
const _messages = require("@langchain/core/messages");
const _langgraph = require("@langchain/langgraph");
// Factory to create Zod schema based on available worker IDs
const createHandoffSchema = (agentIds)=>_zod.z.object({
        agentId: _zod.z.string().nonempty().refine((id)=>agentIds.includes(id), {
            message: 'Invalid agentId'
        }).describe(`ID of the worker agent to handoff to, must be of the following values: 
${agentIds.join('\n')}`),
        taskDescription: _zod.z.string().nonempty().describe('Optional description for the worker worker')
    });
function createHandoffTool(workerAgentIds) {
    if (workerAgentIds.length === 0) {
        throw new Error('createHandoffTool requires at least one workerAgentId');
    }
    const schema = createHandoffSchema(workerAgentIds);
    const toolName = 'handoff_to_worker_agents';
    const handler = async (args, config)=>{
        const state = (0, _langgraph.getCurrentTaskInput)();
        const { agentId, taskDescription } = args;
        const toolMessage = new _messages.ToolMessage({
            content: `Successfully transferred to ${agentId}`,
            name: toolName,
            tool_call_id: config.toolCall.id,
            response_metadata: {
                invoker: state.activeAgent
            }
        });
        const aiMessage = new _messages.AIMessage({
            content: `<task-from-supervisor>
${taskDescription}
<task-from-supervisor>`,
            response_metadata: {
                invoker: agentId
            }
        });
        // Emit Command to switch context
        return new _langgraph.Command({
            goto: 'worker',
            graph: _langgraph.Command.PARENT,
            update: {
                messages: state.messages.concat(toolMessage, aiMessage),
                activeAgent: agentId,
                isSupervisor: false
            }
        });
    };
    return (0, _tools.tool)(handler, {
        name: toolName,
        description: 'Handoff the task to a worker worker',
        schema
    });
}

//# sourceMappingURL=handoff-tool.js.map