// src/modules/worker/helpers/model-resolver.ts
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get cleanModelConfig () {
        return cleanModelConfig;
    },
    get resolveModels () {
        return resolveModels;
    }
});
const _openai = require("@langchain/openai");
const _anthropic = require("@langchain/anthropic");
const _googlegenai = require("@langchain/google-genai");
const _xai = require("@langchain/xai");
const _deepseek = require("@langchain/deepseek");
const _enums = require("../enums");
function cleanModelConfig(config) {
    // clone so we don’t mutate the original
    const cleaned = {
        ...config,
        parameters: config.parameters ? {
            ...config.parameters
        } : {}
    };
    // Always leave parameters as an object (may now be empty)
    return cleaned;
}
const resolveModels = {
    [_enums.ModelProviderEnum.OPENAI]: (config)=>{
        const cfg = cleanModelConfig(config);
        return new _openai.ChatOpenAI({
            model: cfg.name,
            ...cfg.parameters
        });
    },
    [_enums.ModelProviderEnum.ANTHROPIC]: (config)=>{
        const cfg = cleanModelConfig(config);
        return new _anthropic.ChatAnthropic({
            model: cfg.name,
            streaming: true,
            streamUsage: true,
            ...cfg.parameters
        });
    },
    [_enums.ModelProviderEnum.GOOGLE]: (config)=>{
        const cfg = cleanModelConfig(config);
        if (cfg.parameters?.maxTokens) {
            delete cfg.parameters.maxTokens;
            cfg.parameters['maxOutputTokens'] = cfg.parameters.maxTokens;
        }
        return new _googlegenai.ChatGoogleGenerativeAI({
            model: cfg.name,
            streaming: true,
            streamUsage: true,
            ...cfg.parameters
        });
    },
    [_enums.ModelProviderEnum.XAI]: (config)=>{
        const cfg = cleanModelConfig(config);
        return new _xai.ChatXAI({
            model: cfg.name,
            streaming: true,
            ...cfg.parameters
        });
    },
    [_enums.ModelProviderEnum.DEEPSEEK]: (config)=>{
        const cfg = cleanModelConfig(config);
        return new _deepseek.ChatDeepSeek({
            model: cfg.name,
            streaming: true,
            streamUsage: true,
            ...cfg.parameters
        });
    }
};

//# sourceMappingURL=model-resolver.js.map