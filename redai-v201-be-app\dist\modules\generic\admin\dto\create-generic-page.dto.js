"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateGenericPageDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class CreateGenericPageDto {
    name;
    description;
    path;
    config;
}
exports.CreateGenericPageDto = CreateGenericPageDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên của trang',
        example: 'Trang liên hệ',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Tên trang không được để trống' }),
    (0, class_validator_1.IsString)({ message: 'Tên trang phải là chuỗi' }),
    (0, class_validator_1.MinLength)(3, { message: 'Tên trang phải có ít nhất 3 ký tự' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Tên trang không được vượt quá 255 ký tự' }),
    __metadata("design:type", String)
], CreateGenericPageDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả về trang',
        example: 'Form liên hệ cho khách hàng',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Mô tả phải là chuỗi' }),
    __metadata("design:type", String)
], CreateGenericPageDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Đường dẫn URL của trang',
        example: 'lien-he',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Đường dẫn không được để trống' }),
    (0, class_validator_1.IsString)({ message: 'Đường dẫn phải là chuỗi' }),
    (0, class_validator_1.MinLength)(3, { message: 'Đường dẫn phải có ít nhất 3 ký tự' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Đường dẫn không được vượt quá 255 ký tự' }),
    (0, class_validator_1.Matches)(/^[a-z0-9-]+$/, {
        message: 'Đường dẫn chỉ được chứa chữ thường, số và dấu gạch ngang',
    }),
    __metadata("design:type", String)
], CreateGenericPageDto.prototype, "path", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cấu hình trang dạng JSON',
        example: {
            formId: 'contact-form',
            title: 'Liên hệ với chúng tôi',
            subtitle: 'Hãy để lại thông tin, chúng tôi sẽ liên hệ lại với bạn',
            groups: [],
        },
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Cấu hình trang không được để trống' }),
    (0, class_validator_1.IsObject)({ message: 'Cấu hình trang phải là đối tượng JSON' }),
    (0, class_transformer_1.Type)(() => Object),
    __metadata("design:type", Object)
], CreateGenericPageDto.prototype, "config", void 0);
//# sourceMappingURL=create-generic-page.dto.js.map