"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AgentMediaRepository_1, AgentUrlRepository_1, AgentProductRepository_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentProductRepository = exports.AgentUrlRepository = exports.AgentMediaRepository = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const typeorm_transactional_1 = require("typeorm-transactional");
const entities_1 = require("../entities");
const exceptions_1 = require("../../../common/exceptions");
const exceptions_2 = require("../exceptions");
let AgentMediaRepository = AgentMediaRepository_1 = class AgentMediaRepository extends typeorm_1.Repository {
    dataSource;
    logger = new common_1.Logger(AgentMediaRepository_1.name);
    constructor(dataSource) {
        super(entities_1.AgentMedia, dataSource.createEntityManager());
        this.dataSource = dataSource;
    }
    createBaseQuery() {
        return this.createQueryBuilder('agentMedia')
            .select(['agentMedia.mediaId', 'agentMedia.agentId']);
    }
    async findPaginated(agentId, queryDto) {
        try {
            const qb = this.createBaseQuery()
                .where('agentMedia.agentId = :agentId', { agentId })
                .skip((queryDto.page - 1) * queryDto.limit)
                .take(queryDto.limit);
            const [items, total] = await qb.getManyAndCount();
            return {
                items,
                meta: {
                    totalItems: total,
                    itemCount: items.length,
                    itemsPerPage: queryDto.limit,
                    totalPages: Math.ceil(total / queryDto.limit),
                    currentPage: queryDto.page,
                },
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi truy vấn danh sách media của agent: ${error.message}`);
            throw error;
        }
    }
    async addMedia(agentId, mediaId) {
        try {
            const existingAgentMedia = await this.findOne({
                where: { agentId, mediaId },
            });
            if (!existingAgentMedia) {
                const agentMedia = this.create({
                    agentId,
                    mediaId,
                });
                await this.save(agentMedia);
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi thêm media vào agent: ${error.message}`);
            throw error;
        }
    }
    async findByAgentId(agentId) {
        try {
            return this.createBaseQuery()
                .where('agentMedia.agentId = :agentId', { agentId })
                .getMany();
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy danh sách media của agent: ${error.message}`);
            throw error;
        }
    }
    async removeMedia(agentId, mediaId) {
        try {
            const qb = this.createQueryBuilder()
                .delete()
                .where('agentId = :agentId', { agentId })
                .andWhere('mediaId = :mediaId', { mediaId });
            const result = await qb.execute();
            if (result.affected === 0) {
                throw new exceptions_1.AppException(exceptions_2.AGENT_ERROR_CODES.MEDIA_NOT_FOUND);
            }
        }
        catch (error) {
            if (error instanceof exceptions_1.AppException) {
                throw error;
            }
            this.logger.error(`Lỗi khi xóa media khỏi agent: ${error.message}`);
            throw new exceptions_1.AppException(exceptions_2.AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
        }
    }
};
exports.AgentMediaRepository = AgentMediaRepository;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AgentMediaRepository.prototype, "addMedia", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AgentMediaRepository.prototype, "removeMedia", null);
exports.AgentMediaRepository = AgentMediaRepository = AgentMediaRepository_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], AgentMediaRepository);
let AgentUrlRepository = AgentUrlRepository_1 = class AgentUrlRepository extends typeorm_1.Repository {
    dataSource;
    logger = new common_1.Logger(AgentUrlRepository_1.name);
    constructor(dataSource) {
        super(entities_1.AgentUrl, dataSource.createEntityManager());
        this.dataSource = dataSource;
    }
    createBaseQuery() {
        return this.createQueryBuilder('agentUrl')
            .select(['agentUrl.urlId', 'agentUrl.agentId']);
    }
    async findPaginated(agentId, queryDto) {
        try {
            const qb = this.createBaseQuery()
                .where('agentUrl.agentId = :agentId', { agentId })
                .skip((queryDto.page - 1) * queryDto.limit)
                .take(queryDto.limit);
            const [items, total] = await qb.getManyAndCount();
            return {
                items,
                meta: {
                    totalItems: total,
                    itemCount: items.length,
                    itemsPerPage: queryDto.limit,
                    totalPages: Math.ceil(total / queryDto.limit),
                    currentPage: queryDto.page,
                },
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi truy vấn danh sách URL của agent: ${error.message}`);
            throw error;
        }
    }
    async addUrl(agentId, urlId) {
        try {
            const existingAgentUrl = await this.findOne({
                where: { agentId, urlId },
            });
            if (!existingAgentUrl) {
                const agentUrl = this.create({
                    agentId,
                    urlId,
                });
                await this.save(agentUrl);
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi thêm URL vào agent: ${error.message}`);
            throw error;
        }
    }
    async findByAgentId(agentId) {
        try {
            return this.createBaseQuery()
                .where('agentUrl.agentId = :agentId', { agentId })
                .getMany();
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy danh sách URL của agent: ${error.message}`);
            throw error;
        }
    }
    async removeUrl(agentId, urlId) {
        try {
            const qb = this.createQueryBuilder()
                .delete()
                .where('agentId = :agentId', { agentId })
                .andWhere('urlId = :urlId', { urlId });
            const result = await qb.execute();
            if (result.affected === 0) {
                throw new exceptions_1.AppException(exceptions_2.AGENT_ERROR_CODES.URL_NOT_FOUND);
            }
        }
        catch (error) {
            if (error instanceof exceptions_1.AppException) {
                throw error;
            }
            this.logger.error(`Lỗi khi xóa URL khỏi agent: ${error.message}`);
            throw new exceptions_1.AppException(exceptions_2.AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
        }
    }
};
exports.AgentUrlRepository = AgentUrlRepository;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AgentUrlRepository.prototype, "addUrl", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AgentUrlRepository.prototype, "removeUrl", null);
exports.AgentUrlRepository = AgentUrlRepository = AgentUrlRepository_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], AgentUrlRepository);
let AgentProductRepository = AgentProductRepository_1 = class AgentProductRepository extends typeorm_1.Repository {
    dataSource;
    logger = new common_1.Logger(AgentProductRepository_1.name);
    constructor(dataSource) {
        super(entities_1.AgentProduct, dataSource.createEntityManager());
        this.dataSource = dataSource;
    }
    createBaseQuery() {
        return this.createQueryBuilder('agentProduct')
            .select(['agentProduct.productId', 'agentProduct.agentId']);
    }
    async findPaginated(agentId, queryDto) {
        try {
            const qb = this.createBaseQuery()
                .where('agentProduct.agentId = :agentId', { agentId })
                .skip((queryDto.page - 1) * queryDto.limit)
                .take(queryDto.limit);
            const [items, total] = await qb.getManyAndCount();
            return {
                items,
                meta: {
                    totalItems: total,
                    itemCount: items.length,
                    itemsPerPage: queryDto.limit,
                    totalPages: Math.ceil(total / queryDto.limit),
                    currentPage: queryDto.page,
                },
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi truy vấn danh sách sản phẩm của agent: ${error.message}`);
            throw error;
        }
    }
    async addProduct(agentId, productId) {
        try {
            const existingAgentProduct = await this.findOne({
                where: { agentId, productId: parseInt(productId) },
            });
            if (!existingAgentProduct) {
                const agentProduct = this.create({
                    agentId,
                    productId: parseInt(productId),
                });
                await this.save(agentProduct);
            }
        }
        catch (error) {
            this.logger.error(`Lỗi khi thêm sản phẩm vào agent: ${error.message}`);
            throw error;
        }
    }
    async findByAgentId(agentId) {
        try {
            return this.createBaseQuery()
                .where('agentProduct.agentId = :agentId', { agentId })
                .getMany();
        }
        catch (error) {
            this.logger.error(`Lỗi khi lấy danh sách sản phẩm của agent: ${error.message}`);
            throw error;
        }
    }
    async removeProduct(agentId, productId) {
        try {
            const qb = this.createQueryBuilder()
                .delete()
                .where('agentId = :agentId', { agentId })
                .andWhere('productId = :productId', { productId: parseInt(productId) });
            const result = await qb.execute();
            if (result.affected === 0) {
                throw new exceptions_1.AppException(exceptions_2.AGENT_ERROR_CODES.PRODUCT_NOT_FOUND);
            }
        }
        catch (error) {
            if (error instanceof exceptions_1.AppException) {
                throw error;
            }
            this.logger.error(`Lỗi khi xóa sản phẩm khỏi agent: ${error.message}`);
            throw new exceptions_1.AppException(exceptions_2.AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
        }
    }
};
exports.AgentProductRepository = AgentProductRepository;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AgentProductRepository.prototype, "addProduct", null);
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AgentProductRepository.prototype, "removeProduct", null);
exports.AgentProductRepository = AgentProductRepository = AgentProductRepository_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], AgentProductRepository);
//# sourceMappingURL=agent-resource.repository.js.map