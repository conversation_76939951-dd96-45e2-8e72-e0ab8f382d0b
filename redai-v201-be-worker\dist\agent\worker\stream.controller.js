// src/stream/stream.controller.ts
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "StreamController", {
    enumerable: true,
    get: function() {
        return StreamController;
    }
});
const _common = require("@nestjs/common");
const _express = require("express");
const _infra = require("../../infra");
const _nanoid = require("nanoid");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let StreamController = class StreamController {
    async cancelStream(threadId) {
        if (!threadId) {
            throw new _common.BadRequestException('threadId is required');
        }
        const client = this.redisService.getRawClient();
        // publish on cancel:<threadId> channel
        await client.publish(`cancel:${threadId}`, '');
    }
    async streamEvents(req, res, threadId) {
        if (!threadId) {
            throw new _common.BadRequestException('threadId is required');
        }
        const streamKey = `agent_stream:${threadId}`;
        const client = this.redisService.getRawClient();
        const groupName = `sse-group:${threadId}`;
        const consumerId = `consumer:${(0, _nanoid.nanoid)()}`;
        const subscriber = client.duplicate();
        // Create (or ignore if exists) the consumer group
        try {
            await client.xgroup('CREATE', streamKey, groupName, '0', 'MKSTREAM');
        } catch (e) {
            if (!/BUSYGROUP/.test(e.message)) throw e;
        }
        // SSE headers
        res.set({
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache, no-transform',
            Connection: 'keep-alive',
            'Retry-After': '2'
        });
        res.flushHeaders();
        // Clean up on disconnect
        req.on('close', async ()=>{
            await subscriber.unsubscribe(streamKey);
            res.end();
        });
        // helper to parse field arrays
        const parseFields = (fields)=>{
            const obj = {};
            for(let i = 0; i < fields.length; i += 2){
                const key = fields[i];
                try {
                    obj[key] = JSON.parse(fields[i + 1]);
                } catch  {
                    obj[key] = fields[i + 1];
                }
            }
            return obj;
        };
        // Read only new messages (">") for this group
        const readNew = async ()=>{
            const chunks = await client.xreadgroup('GROUP', groupName, consumerId, 'COUNT', 20, 'STREAMS', streamKey, '>');
            if (!chunks) return;
            // @ts-ignore
            const [[, messages]] = chunks;
            for (const [id, fields] of messages){
                const payload = parseFields(fields);
                // include SSE id header so client could reconnect with Last-Event-ID
                res.write(`id: ${id}\n`);
                res.write(`data: ${JSON.stringify(payload)}\n\n`);
                if (payload.event === 'llm_stream_end') {
                    res.write(`event: end\ndata: {}\n\n`);
                    await subscriber.unsubscribe(streamKey);
                    res.end();
                    return;
                }
            }
        };
        // Subscribe to pub/sub notifications on this thread’s channel
        await subscriber.subscribe(streamKey);
        subscriber.on('message', async ()=>{
            await readNew();
        });
        // Initial catch-up: in case events arrived before we subscribed
        await readNew();
    }
    constructor(redisService){
        this.redisService = redisService;
    }
};
_ts_decorate([
    (0, _common.Post)('cancel'),
    (0, _common.HttpCode)(204),
    _ts_param(0, (0, _common.Query)('threadId')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], StreamController.prototype, "cancelStream", null);
_ts_decorate([
    (0, _common.Get)('events'),
    _ts_param(0, (0, _common.Req)()),
    _ts_param(1, (0, _common.Res)()),
    _ts_param(2, (0, _common.Query)('threadId')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _express.Request === "undefined" ? Object : _express.Request,
        typeof _express.Response === "undefined" ? Object : _express.Response,
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], StreamController.prototype, "streamEvents", null);
StreamController = _ts_decorate([
    (0, _common.Controller)('stream'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _infra.RedisService === "undefined" ? Object : _infra.RedisService
    ])
], StreamController);

//# sourceMappingURL=stream.controller.js.map