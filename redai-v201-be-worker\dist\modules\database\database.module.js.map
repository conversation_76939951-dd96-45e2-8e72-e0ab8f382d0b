{"version": 3, "sources": ["../../../src/modules/database/database.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { User } from './entities/user.entity';\r\nimport { UserRepository } from './repositories/user.repository';\r\n\r\n@Module({\r\n  imports: [TypeOrmModule.forFeature([User])],\r\n  providers: [UserRepository],\r\n  exports: [TypeOrmModule, UserRepository],\r\n})\r\nexport class DatabaseModule {}\r\n"], "names": ["DatabaseModule", "imports", "TypeOrmModule", "forFeature", "User", "providers", "UserRepository", "exports"], "mappings": ";;;;+BAUaA;;;eAAAA;;;wBAVU;yBACO;4BACT;gCACU;;;;;;;AAOxB,IAAA,AAAMA,iBAAN,MAAMA;AAAgB;;;QAJ3BC,SAAS;YAACC,sBAAa,CAACC,UAAU,CAAC;gBAACC,gBAAI;aAAC;SAAE;QAC3CC,WAAW;YAACC,8BAAc;SAAC;QAC3BC,SAAS;YAACL,sBAAa;YAAEI,8BAAc;SAAC"}