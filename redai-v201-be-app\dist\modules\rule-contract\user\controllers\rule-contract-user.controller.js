"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RuleContractUserController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_user_guard_1 = require("../../../auth/guards/jwt-user.guard");
const response_1 = require("../../../../common/response");
const services_1 = require("../services");
const dto_1 = require("../dto");
const swagger_2 = require("../../../../common/swagger");
const current_user_decorator_1 = require("../../../auth/decorators/current-user.decorator");
const rule_contract_state_service_1 = require("../../state-machine/rule-contract-state.service");
const rule_contract_types_1 = require("../../state-machine/rule-contract.types");
const rule_contract_entity_1 = require("../../entities/rule-contract.entity");
let RuleContractUserController = class RuleContractUserController {
    ruleContractUserService;
    ruleContractStateService;
    constructor(ruleContractUserService, ruleContractStateService) {
        this.ruleContractUserService = ruleContractUserService;
        this.ruleContractStateService = ruleContractStateService;
    }
    async registerTypeRuleContract(user, dto) {
        const createData = {
            userId: user.id,
            contractType: dto.type,
        };
        await this.ruleContractStateService.createContract(user.id, createData);
        return response_1.ApiResponseDto.success({
            status: (0, rule_contract_types_1.mapStateToStatus)(rule_contract_types_1.RuleContractState.DRAFT),
            type: dto.type,
        }, 'Đăng ký loại hợp đồng nguyên tắc thành công');
    }
    async getContracts(user, queryDto) {
        const contracts = await this.ruleContractUserService.getContracts(user.id, queryDto);
        return response_1.ApiResponseDto.success(contracts, 'Lấy danh sách hợp đồng nguyên tắc thành công');
    }
    async getContractById(user, id) {
        const contract = await this.ruleContractUserService.getContractById(user.id, id);
        return response_1.ApiResponseDto.success(contract, 'Lấy chi tiết hợp đồng nguyên tắc thành công');
    }
    async createIndividualRuleContract(user, dto) {
        const result = await this.ruleContractStateService.createIndividualContract(user.id, {
            name: dto.name,
            address: dto.address,
            phone: dto.phone,
            dateOfBirth: dto.dateOfBirth,
            cccd: dto.cccd,
            issuePlace: dto.issuePlace,
            issueDate: dto.issueDate,
            taxCode: dto.taxCode,
        });
        return response_1.ApiResponseDto.success({
            status: rule_contract_entity_1.ContractStatusEnum.DRAFT,
            type: rule_contract_entity_1.ContractTypeEnum.INDIVIDUAL,
            contractBase64: result.contractBase64,
            contractUrl: result.contractUrl || '',
        }, 'Tạo hợp đồng nguyên tắc cho cá nhân thành công');
    }
    async getLatestContractStatus(user) {
        const status = await this.ruleContractUserService.getLatestContractStatus(user.id);
        return response_1.ApiResponseDto.success(status, 'Lấy trạng thái hợp đồng nguyên tắc mới nhất thành công');
    }
    async signContract(user, id, dto) {
        await this.ruleContractUserService.getContractById(user.id, id);
        const state = await this.ruleContractStateService.signContract(id, {
            signatureData: dto.signatureData,
        });
        return response_1.ApiResponseDto.success({ state }, 'Ký hợp đồng nguyên tắc thành công');
    }
    async resubmitContract(user, id) {
        await this.ruleContractUserService.getContractById(user.id, id);
        const state = await this.ruleContractStateService.resubmitContract(id);
        return response_1.ApiResponseDto.success({ state }, 'Gửi lại hợp đồng thành công');
    }
    async upgradeToBusinessContract(user, id) {
        await this.ruleContractUserService.getContractById(user.id, id);
        const state = await this.ruleContractStateService.upgradeToBusinessContract(id, {});
        return response_1.ApiResponseDto.success({ state }, 'Nâng cấp hợp đồng thành công');
    }
    async getCurrentState(user, id) {
        await this.ruleContractUserService.getContractById(user.id, id);
        const state = await this.ruleContractStateService.getCurrentState(id);
        return response_1.ApiResponseDto.success({ state }, 'Lấy trạng thái hợp đồng thành công');
    }
};
exports.RuleContractUserController = RuleContractUserController;
__decorate([
    (0, common_1.Post)('register'),
    (0, swagger_1.ApiOperation)({ summary: 'Đăng ký loại hợp đồng nguyên tắc' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Đăng ký loại hợp đồng nguyên tắc thành công',
        schema: {
            allOf: [
                { $ref: (0, swagger_1.getSchemaPath)(response_1.ApiResponseDto) },
                {
                    properties: {
                        result: { $ref: (0, swagger_1.getSchemaPath)(dto_1.RuleContractStatusResponseDto) },
                    },
                },
            ],
        },
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dto_1.RegisterRuleContractDto]),
    __metadata("design:returntype", Promise)
], RuleContractUserController.prototype, "registerTypeRuleContract", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách hợp đồng nguyên tắc của người dùng' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lấy danh sách hợp đồng nguyên tắc thành công',
        schema: {
            allOf: [
                { $ref: (0, swagger_1.getSchemaPath)(response_1.ApiResponseDto) },
                {
                    properties: {
                        result: {
                            allOf: [
                                { $ref: (0, swagger_1.getSchemaPath)(response_1.PaginatedResult) },
                                {
                                    properties: {
                                        items: {
                                            type: 'array',
                                            items: { $ref: (0, swagger_1.getSchemaPath)(dto_1.RuleContractResponseDto) },
                                        },
                                    },
                                },
                            ],
                        },
                    },
                },
            ],
        },
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dto_1.RuleContractQueryDto]),
    __metadata("design:returntype", Promise)
], RuleContractUserController.prototype, "getContracts", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy chi tiết hợp đồng nguyên tắc' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của hợp đồng',
        type: 'number',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lấy chi tiết hợp đồng nguyên tắc thành công',
        schema: {
            allOf: [
                { $ref: (0, swagger_1.getSchemaPath)(response_1.ApiResponseDto) },
                {
                    properties: {
                        result: { $ref: (0, swagger_1.getSchemaPath)(dto_1.RuleContractResponseDto) },
                    },
                },
            ],
        },
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], RuleContractUserController.prototype, "getContractById", null);
__decorate([
    (0, common_1.Post)('individual'),
    (0, swagger_1.ApiOperation)({ summary: 'Tạo hợp đồng nguyên tắc cho cá nhân' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Tạo hợp đồng nguyên tắc cho cá nhân thành công',
        schema: {
            allOf: [
                { $ref: (0, swagger_1.getSchemaPath)(response_1.ApiResponseDto) },
                {
                    properties: {
                        result: { $ref: (0, swagger_1.getSchemaPath)(dto_1.RuleContractExtendedResponseDto) },
                    },
                },
            ],
        },
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dto_1.CreateIndividualRuleContractDto]),
    __metadata("design:returntype", Promise)
], RuleContractUserController.prototype, "createIndividualRuleContract", null);
__decorate([
    (0, common_1.Get)('latest-status'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy trạng thái hợp đồng nguyên tắc mới nhất của người dùng' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lấy trạng thái hợp đồng nguyên tắc mới nhất thành công',
        schema: {
            allOf: [
                { $ref: (0, swagger_1.getSchemaPath)(response_1.ApiResponseDto) },
                {
                    properties: {
                        result: {
                            oneOf: [
                                { $ref: (0, swagger_1.getSchemaPath)(dto_1.RuleContractStatusResponseDto) },
                                { type: 'null' }
                            ]
                        },
                    },
                },
            ],
        },
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], RuleContractUserController.prototype, "getLatestContractStatus", null);
__decorate([
    (0, common_1.Post)(':id/sign'),
    (0, swagger_1.ApiOperation)({ summary: 'Ký hợp đồng nguyên tắc' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của hợp đồng',
        type: 'number',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Ký hợp đồng nguyên tắc thành công',
        schema: {
            allOf: [
                { $ref: (0, swagger_1.getSchemaPath)(response_1.ApiResponseDto) },
                {
                    properties: {
                        result: {
                            type: 'object',
                            properties: {
                                state: {
                                    type: 'string',
                                    enum: Object.values(rule_contract_types_1.RuleContractState),
                                    example: rule_contract_types_1.RuleContractState.PENDING_APPROVAL,
                                },
                            },
                        },
                    },
                },
            ],
        },
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, dto_1.SignContractDto]),
    __metadata("design:returntype", Promise)
], RuleContractUserController.prototype, "signContract", null);
__decorate([
    (0, common_1.Post)(':id/resubmit'),
    (0, swagger_1.ApiOperation)({ summary: 'Gửi lại hợp đồng đã bị từ chối' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của hợp đồng',
        type: 'number',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Gửi lại hợp đồng thành công',
        schema: {
            allOf: [
                { $ref: (0, swagger_1.getSchemaPath)(response_1.ApiResponseDto) },
                {
                    properties: {
                        result: {
                            type: 'object',
                            properties: {
                                state: {
                                    type: 'string',
                                    enum: Object.values(rule_contract_types_1.RuleContractState),
                                    example: rule_contract_types_1.RuleContractState.DRAFT,
                                },
                            },
                        },
                    },
                },
            ],
        },
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], RuleContractUserController.prototype, "resubmitContract", null);
__decorate([
    (0, common_1.Post)(':id/upgrade-to-business'),
    (0, swagger_1.ApiOperation)({ summary: 'Nâng cấp hợp đồng từ cá nhân lên doanh nghiệp' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của hợp đồng',
        type: 'number',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Nâng cấp hợp đồng thành công',
        schema: {
            allOf: [
                { $ref: (0, swagger_1.getSchemaPath)(response_1.ApiResponseDto) },
                {
                    properties: {
                        result: {
                            type: 'object',
                            properties: {
                                state: {
                                    type: 'string',
                                    enum: Object.values(rule_contract_types_1.RuleContractState),
                                    example: rule_contract_types_1.RuleContractState.DRAFT,
                                },
                            },
                        },
                    },
                },
            ],
        },
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], RuleContractUserController.prototype, "upgradeToBusinessContract", null);
__decorate([
    (0, common_1.Get)(':id/state'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy trạng thái hiện tại của hợp đồng' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID của hợp đồng',
        type: 'number',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lấy trạng thái hợp đồng thành công',
        schema: {
            allOf: [
                { $ref: (0, swagger_1.getSchemaPath)(response_1.ApiResponseDto) },
                {
                    properties: {
                        result: {
                            type: 'object',
                            properties: {
                                state: {
                                    type: 'string',
                                    enum: Object.values(rule_contract_types_1.RuleContractState),
                                    example: rule_contract_types_1.RuleContractState.DRAFT,
                                },
                            },
                        },
                    },
                },
            ],
        },
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], RuleContractUserController.prototype, "getCurrentState", null);
exports.RuleContractUserController = RuleContractUserController = __decorate([
    (0, common_1.Controller)('user/rule-contracts'),
    (0, common_1.UseGuards)(jwt_user_guard_1.JwtUserGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiTags)(swagger_2.SWAGGER_API_TAGS.USER_RULE_CONTRACT),
    (0, swagger_1.ApiExtraModels)(response_1.ApiResponseDto, dto_1.RuleContractResponseDto, dto_1.RuleContractStatusResponseDto, dto_1.RuleContractExtendedResponseDto, response_1.PaginatedResult),
    __metadata("design:paramtypes", [services_1.RuleContractUserService,
        rule_contract_state_service_1.RuleContractStateService])
], RuleContractUserController);
//# sourceMappingURL=rule-contract-user.controller.js.map