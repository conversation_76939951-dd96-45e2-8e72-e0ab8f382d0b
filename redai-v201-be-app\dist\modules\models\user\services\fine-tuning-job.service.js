"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var FineTuningJobService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FineTuningJobService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const exceptions_1 = require("../../../../common/exceptions");
const models_exception_1 = require("../../exceptions/models.exception");
const provider_enum_1 = require("../../constants/provider.enum");
const user_data_fine_tune_entity_1 = require("../../entities/user-data-fine-tune.entity");
const user_model_fine_tune_entity_1 = require("../../entities/user-model-fine-tune.entity");
const fine_tune_histories_entity_1 = require("../../entities/fine-tune-histories.entity");
const system_models_entity_1 = require("../../entities/system-models.entity");
const user_models_entity_1 = require("../../entities/user-models.entity");
const model_registry_entity_1 = require("../../entities/model-registry.entity");
const user_repository_1 = require("../../../user/repositories/user.repository");
const user_key_llm_repository_1 = require("../../repositories/user-key-llm.repository");
const openai_service_1 = require("../../../../shared/services/ai/openai.service");
const google_ai_service_1 = require("../../../../shared/services/ai/google_ai.service");
const s3_service_1 = require("../../../../shared/services/s3.service");
const api_key_encryption_helper_1 = require("../../helpers/api-key-encryption.helper");
let FineTuningJobService = FineTuningJobService_1 = class FineTuningJobService {
    dataSource;
    userRepository;
    userKeyLlmRepository;
    openAiService;
    googleAIService;
    s3Service;
    apiKeyEncryptionHelper;
    logger = new common_1.Logger(FineTuningJobService_1.name);
    constructor(dataSource, userRepository, userKeyLlmRepository, openAiService, googleAIService, s3Service, apiKeyEncryptionHelper) {
        this.dataSource = dataSource;
        this.userRepository = userRepository;
        this.userKeyLlmRepository = userKeyLlmRepository;
        this.openAiService = openAiService;
        this.googleAIService = googleAIService;
        this.s3Service = s3Service;
        this.apiKeyEncryptionHelper = apiKeyEncryptionHelper;
    }
    async createFineTuningJob(userId, dto, systemApiKey) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const dataset = await this.validateDataset(dto.datasetId, userId);
            const { baseModelInfo, apiKey, userKeyInfo } = await this.validateModelAndApiKey(userId, dto.baseModelId, dto.userKeyLlmId, dto.provider, systemApiKey);
            const tokenInfo = await this.calculateTokensAndCost(dataset.trainDataset, baseModelInfo.trainingPricing);
            await this.deductUserPoints(userId, tokenInfo.totalCost, queryRunner);
            let jobResult;
            let trainingFileId;
            let validationFileId;
            let trainingDataUri;
            if (dto.provider === provider_enum_1.ProviderFineTuneEnum.OPENAI) {
                trainingFileId = await this.openAiService.uploadTrainingFile(dataset.trainDataset, apiKey);
                if (dataset.validDataset) {
                    validationFileId = await this.openAiService.uploadTrainingFile(dataset.validDataset, apiKey);
                }
                jobResult = await this.openAiService.createFineTuningJob({
                    trainingFileId,
                    validationFileId,
                    model: baseModelInfo.modelId,
                    suffix: dto.suffix,
                    hyperparameters: dto.hyperparameters ? {
                        nEpochs: dto.hyperparameters.epochs,
                        batchSize: dto.hyperparameters.batchSize,
                        learningRateMultiplier: dto.hyperparameters.learningRate,
                    } : undefined,
                }, apiKey);
            }
            else if (dto.provider === provider_enum_1.ProviderFineTuneEnum.GOOGLE) {
                if (!dto.googleCloud) {
                    throw new exceptions_1.AppException(models_exception_1.MODELS_ERROR_CODES.MISSING_GOOGLE_CLOUD_CONFIG, 'Thiếu thông tin Google Cloud configuration');
                }
                trainingDataUri = await this.googleAIService.uploadTrainingFile(dataset.trainDataset, dto.googleCloud.bucketName, apiKey);
                jobResult = await this.googleAIService.createFineTuningJob({
                    baseModelId: baseModelInfo.modelId,
                    displayName: dto.name,
                    description: dto.description,
                    trainingDataUri,
                    hyperParameters: dto.hyperparameters ? {
                        epochCount: dto.hyperparameters.epochs,
                        batchSize: dto.hyperparameters.batchSize,
                        learningRate: dto.hyperparameters.learningRate,
                    } : undefined,
                }, apiKey, dto.googleCloud.projectId, dto.googleCloud.location);
            }
            await this.saveFineTuningJob(userId, dto, dataset, baseModelInfo, jobResult, tokenInfo, trainingFileId, trainingDataUri, userKeyInfo, queryRunner);
            await queryRunner.commitTransaction();
            const user = await this.dataSource.getRepository('User').findOne({ where: { id: userId } });
            this.logger.log(`Fine-tuning job created successfully: ${jobResult.id || jobResult.name}`);
            return {
                jobId: jobResult.id || jobResult.name,
                jobName: jobResult.name,
                status: jobResult.status || jobResult.state,
                baseModel: baseModelInfo.modelId,
                trainingFileId,
                trainingDataUri,
                estimatedTokens: tokenInfo.totalTokens,
                costDeducted: tokenInfo.totalCost,
                remainingBalance: user?.pointsBalance || 0,
                userKeyLlmId: userKeyInfo?.id,
                userKeyLlmName: userKeyInfo?.name,
                createdAt: Date.now(),
            };
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`Error creating fine-tuning job: ${error.message}`, error.stack);
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async validateModelAndApiKey(userId, baseModelId, userKeyLlmId, provider, systemApiKey) {
        if (userKeyLlmId) {
            const userKey = await this.userKeyLlmRepository.findOne({
                where: {
                    id: userKeyLlmId,
                    userId,
                    provider: provider,
                    deletedAt: null
                },
            });
            if (!userKey) {
                throw new exceptions_1.AppException(models_exception_1.MODELS_ERROR_CODES.USER_KEY_LLM_NOT_FOUND, 'Không tìm thấy user key LLM hoặc key không thuộc về bạn');
            }
            const userModel = await this.dataSource.getRepository(user_models_entity_1.UserModels).findOne({
                where: { id: baseModelId },
            });
            if (!userModel) {
                throw new exceptions_1.AppException(models_exception_1.MODELS_ERROR_CODES.MODEL_NOT_FOUND, 'Không tìm thấy user model');
            }
            const modelRegistry = await this.dataSource.getRepository(model_registry_entity_1.ModelRegistry).findOne({
                where: {
                    id: userModel.modelRegistryId,
                    deletedAt: null,
                },
            });
            if (!modelRegistry) {
                throw new exceptions_1.AppException(models_exception_1.MODELS_ERROR_CODES.MODEL_REGISTRY_NOT_FOUND, 'Không tìm thấy thông tin model registry');
            }
            if (modelRegistry.provider !== provider) {
                throw new exceptions_1.AppException(models_exception_1.MODELS_ERROR_CODES.INVALID_PROVIDER, `Provider ${provider} không khớp với model ${modelRegistry.provider}`);
            }
            const decryptedApiKey = this.apiKeyEncryptionHelper.decryptUserApiKey(userKey.apiKey, userId);
            return {
                baseModelInfo: {
                    ...userModel,
                    modelId: userModel.modelId,
                    trainingPricing: modelRegistry.trainingPricing,
                    provider: modelRegistry.provider,
                },
                apiKey: decryptedApiKey,
                userKeyInfo: {
                    id: userKey.id,
                    name: userKey.name,
                    provider: userKey.provider,
                },
            };
        }
        else {
            if (!systemApiKey) {
                throw new exceptions_1.AppException(models_exception_1.MODELS_ERROR_CODES.MISSING_API_KEY, 'Không có API key để thực hiện fine-tuning. Vui lòng cung cấp user key hoặc system key.');
            }
            const systemModel = await this.dataSource.getRepository(system_models_entity_1.SystemModels).findOne({
                where: { id: baseModelId, active: true },
            });
            if (!systemModel) {
                throw new exceptions_1.AppException(models_exception_1.MODELS_ERROR_CODES.MODEL_NOT_FOUND, 'Không tìm thấy system model hoặc model không active');
            }
            const modelRegistry = await this.dataSource.getRepository(model_registry_entity_1.ModelRegistry).findOne({
                where: {
                    id: systemModel.modelRegistryId,
                    deletedAt: null,
                },
            });
            if (!modelRegistry) {
                throw new exceptions_1.AppException(models_exception_1.MODELS_ERROR_CODES.MODEL_REGISTRY_NOT_FOUND, 'Không tìm thấy thông tin model registry');
            }
            if (modelRegistry.provider !== provider) {
                throw new exceptions_1.AppException(models_exception_1.MODELS_ERROR_CODES.INVALID_PROVIDER, `Provider ${provider} không khớp với model ${modelRegistry.provider}`);
            }
            return {
                baseModelInfo: {
                    ...systemModel,
                    modelId: systemModel.modelId,
                    trainingPricing: modelRegistry.trainingPricing,
                    provider: modelRegistry.provider,
                },
                apiKey: systemApiKey,
            };
        }
    }
    async validateDataset(datasetId, userId) {
        const dataset = await this.dataSource.getRepository(user_data_fine_tune_entity_1.UserDataFineTune).findOne({
            where: { id: datasetId, userId, deletedAt: null },
        });
        if (!dataset) {
            throw new exceptions_1.AppException(models_exception_1.MODELS_ERROR_CODES.DATASET_NOT_FOUND, 'Không tìm thấy dataset hoặc bạn không có quyền truy cập');
        }
        if (!dataset.trainDataset) {
            throw new exceptions_1.AppException(models_exception_1.MODELS_ERROR_CODES.MISSING_TRAINING_DATA, 'Dataset không có dữ liệu training');
        }
        return dataset;
    }
    async calculateTokensAndCost(trainFileKey, trainingPricing) {
        try {
            const fileBytes = await this.s3Service.downloadFileAsBytes(trainFileKey);
            const fileContent = new TextDecoder().decode(fileBytes);
            const lines = fileContent.split('\n').filter(line => line.trim());
            let totalTokens = 0;
            for (const line of lines) {
                try {
                    const data = JSON.parse(line);
                    const content = JSON.stringify(data);
                    const estimatedTokens = Math.ceil(content.length / 3);
                    totalTokens += estimatedTokens;
                }
                catch (parseError) {
                    this.logger.warn(`Không thể parse dòng JSONL: ${line}`);
                }
            }
            const totalCost = Math.ceil((totalTokens / 1000) * trainingPricing);
            return { totalTokens, totalCost };
        }
        catch (error) {
            this.logger.error(`Error calculating tokens: ${error.message}`, error.stack);
            throw new exceptions_1.AppException(models_exception_1.MODELS_ERROR_CODES.TOKEN_CALCULATION_FAILED, 'Không thể tính toán số lượng token');
        }
    }
    async deductUserPoints(userId, cost, queryRunner) {
        try {
            await this.userRepository.updateUserBalance(userId, -cost);
            this.logger.log(`Deducted ${cost} R-Points from user ${userId}`);
        }
        catch (error) {
            throw new exceptions_1.AppException(models_exception_1.MODELS_ERROR_CODES.INSUFFICIENT_POINTS, error.message || 'Số dư R-Points không đủ');
        }
    }
    async saveFineTuningJob(userId, dto, dataset, baseModelInfo, jobResult, tokenInfo, trainingFileId, trainingDataUri, userKeyInfo, queryRunner) {
        const fineTuneHistory = queryRunner.manager.create(fine_tune_histories_entity_1.FineTuneHistories, {
            modelName: dto.name,
            token: tokenInfo.totalTokens,
            method: {
                provider: dto.provider,
                hyperparameters: dto.hyperparameters,
                trainingFile: trainingFileId || trainingDataUri,
            },
            metadata: {
                jobId: jobResult.id || jobResult.name,
                jobName: jobResult.name,
                baseModel: baseModelInfo.modelId,
                datasetId: dto.datasetId,
                costDeducted: tokenInfo.totalCost,
                userKeyLlmId: userKeyInfo?.id,
                description: dto.description,
            },
            userId,
            startDate: Date.now(),
            endDate: Date.now(),
        });
        const savedHistory = await queryRunner.manager.save(fineTuneHistory);
        const fineTuneModel = queryRunner.manager.create(user_model_fine_tune_entity_1.UserModelFineTune, {
            modelId: jobResult.fineTunedModel || `${dto.name}-${Date.now()}`,
            modelBase: baseModelInfo.modelId,
            modelRegistryId: baseModelInfo.registryId || baseModelInfo.id,
            llmKeyId: userKeyInfo?.id || null,
            detailId: savedHistory.id,
        });
        return await queryRunner.manager.save(fineTuneModel);
    }
};
exports.FineTuningJobService = FineTuningJobService;
exports.FineTuningJobService = FineTuningJobService = FineTuningJobService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource,
        user_repository_1.UserRepository,
        user_key_llm_repository_1.UserKeyLlmRepository,
        openai_service_1.OpenAiService,
        google_ai_service_1.GoogleAIService,
        s3_service_1.S3Service,
        api_key_encryption_helper_1.ApiKeyEncryptionHelper])
], FineTuningJobService);
//# sourceMappingURL=fine-tuning-job.service.js.map