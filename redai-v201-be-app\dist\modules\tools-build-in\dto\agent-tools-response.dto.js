"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentToolsResponseDto = exports.ToolResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ToolResponseDto {
    id;
    name;
    description;
    type;
    versionId;
    versionNumber;
    toolName;
    parameters;
    endpoint;
    method;
    integration;
    apiKeyInfo;
}
exports.ToolResponseDto = ToolResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của tool',
        example: '550e8400-e29b-41d4-a716-446655440000',
    }),
    __metadata("design:type", String)
], ToolResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên tool',
        example: 'Web Search',
    }),
    __metadata("design:type", String)
], ToolResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả tool',
        example: 'Tìm kiếm thông tin trên web',
    }),
    __metadata("design:type", String)
], ToolResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Loại tool',
        example: 'ADMIN',
    }),
    __metadata("design:type", String)
], ToolResponseDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID phiên bản của tool',
        example: '550e8400-e29b-41d4-a716-446655440000',
        required: false,
    }),
    __metadata("design:type", String)
], ToolResponseDto.prototype, "versionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số phiên bản của tool',
        example: 'V.1.0.0',
        required: false,
    }),
    __metadata("design:type", String)
], ToolResponseDto.prototype, "versionNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên hàm của tool',
        example: 'webSearch',
        required: false,
    }),
    __metadata("design:type", String)
], ToolResponseDto.prototype, "toolName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tham số của tool',
        example: {
            type: 'object',
            properties: {
                query: {
                    type: 'string',
                    description: 'Câu truy vấn tìm kiếm',
                },
            },
            required: ['query'],
        },
        required: false,
    }),
    __metadata("design:type", Object)
], ToolResponseDto.prototype, "parameters", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Endpoint của tool',
        example: '/api/v1/search',
        required: false,
    }),
    __metadata("design:type", String)
], ToolResponseDto.prototype, "endpoint", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Phương thức HTTP của tool',
        example: 'GET',
        required: false,
    }),
    __metadata("design:type", String)
], ToolResponseDto.prototype, "method", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin tích hợp của tool',
        required: false,
    }),
    __metadata("design:type", Object)
], ToolResponseDto.prototype, "integration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin API key',
        required: false,
    }),
    __metadata("design:type", Object)
], ToolResponseDto.prototype, "apiKeyInfo", void 0);
class AgentToolsResponseDto {
    agentId;
    agentName;
    tools;
    totalTools;
}
exports.AgentToolsResponseDto = AgentToolsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của agent',
        example: '550e8400-e29b-41d4-a716-446655440000',
    }),
    __metadata("design:type", String)
], AgentToolsResponseDto.prototype, "agentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên của agent',
        example: 'Marketing Agent',
    }),
    __metadata("design:type", String)
], AgentToolsResponseDto.prototype, "agentName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách tool',
        type: [ToolResponseDto],
    }),
    __metadata("design:type", Array)
], AgentToolsResponseDto.prototype, "tools", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tổng số tool',
        example: 10,
    }),
    __metadata("design:type", Number)
], AgentToolsResponseDto.prototype, "totalTools", void 0);
//# sourceMappingURL=agent-tools-response.dto.js.map