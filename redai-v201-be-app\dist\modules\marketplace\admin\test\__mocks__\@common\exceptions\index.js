"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppException = exports.ErrorCode = void 0;
class ErrorCode {
    code;
    message;
    status;
    constructor(code, message, status) {
        this.code = code;
        this.message = message;
        this.status = status;
    }
}
exports.ErrorCode = ErrorCode;
class AppException extends Error {
    errorCode;
    constructor(errorCode, message) {
        super(message || errorCode.message);
        this.errorCode = errorCode;
        this.name = 'AppException';
    }
}
exports.AppException = AppException;
//# sourceMappingURL=index.js.map