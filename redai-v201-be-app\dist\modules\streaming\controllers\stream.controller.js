"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var StreamController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
let StreamController = StreamController_1 = class StreamController {
    logger = new common_1.Logger(StreamController_1.name);
    constructor() { }
    async streamEvents(req, res, threadId) {
        if (!threadId || threadId.trim() === '') {
            throw new common_1.BadRequestException('threadId is required and cannot be empty');
        }
        this.logger.log(`🔥 Starting SSE stream for thread ${threadId}`);
        try {
            res.set({
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache, no-transform',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Cache-Control',
                'X-Accel-Buffering': 'no',
            });
            res.write(`event: connected\n`);
            res.write(`data: {"threadId":"${threadId}","timestamp":${Date.now()},"status":"connected"}\n\n`);
            this.logger.log(`✅ SSE headers set for thread ${threadId}`);
            const testInterval = setInterval(() => {
                if (res.destroyed) {
                    clearInterval(testInterval);
                    return;
                }
                const testMessage = {
                    event: 'test_message',
                    data: {
                        threadId,
                        message: 'SSE connection active',
                        timestamp: Date.now(),
                    },
                };
                res.write(`id: test_${Date.now()}\n`);
                res.write(`data: ${JSON.stringify(testMessage)}\n\n`);
                this.logger.debug(`📡 Sent test message for thread ${threadId}`);
            }, 5000);
            req.on('close', () => {
                clearInterval(testInterval);
                this.logger.log(`🔌 Client disconnected from thread ${threadId}`);
            });
            req.on('error', (error) => {
                clearInterval(testInterval);
                this.logger.error(`❌ SSE error for thread ${threadId}:`, error);
                if (!res.destroyed) {
                    res.end();
                }
            });
        }
        catch (error) {
            this.logger.error(`💥 Failed to establish SSE for thread ${threadId}:`, error);
            if (!res.headersSent) {
                throw error;
            }
        }
    }
    getHealth() {
        return {
            status: 'healthy',
            timestamp: Date.now(),
        };
    }
};
exports.StreamController = StreamController;
__decorate([
    (0, common_1.Get)('events/:threadId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Stream events via Server-Sent Events',
        description: 'Establishes SSE connection to stream real-time events for a specific thread. Supports multi-device viewing with complete stream replay.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'threadId',
        description: 'Thread ID to stream events for',
        example: 'thread_123456',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'SSE stream established successfully',
        headers: {
            'Content-Type': { description: 'text/event-stream' },
            'Cache-Control': { description: 'no-cache' },
            'Connection': { description: 'keep-alive' },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Invalid threadId parameter',
    }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __param(2, (0, common_1.Param)('threadId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, String]),
    __metadata("design:returntype", Promise)
], StreamController.prototype, "streamEvents", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({
        summary: 'Health check for streaming service',
        description: 'Returns the health status of the streaming service',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Service is healthy',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Object)
], StreamController.prototype, "getHealth", null);
exports.StreamController = StreamController = StreamController_1 = __decorate([
    (0, swagger_1.ApiTags)('Streaming'),
    (0, common_1.Controller)('stream'),
    __metadata("design:paramtypes", [])
], StreamController);
//# sourceMappingURL=stream.controller.js.map