"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const models_admin_module_1 = require("./admin/models-admin.module");
const models_user_module_1 = require("./user/models-user.module");
const entities = require("./entities");
let ModelsModule = class ModelsModule {
};
exports.ModelsModule = ModelsModule;
exports.ModelsModule = ModelsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                entities.ModelRegistry,
                entities.SystemKeyLlm,
                entities.FineTuneHistories,
                entities.UserKeyLlm,
                entities.UserDataFineTune,
                entities.AdminDataFineTune,
                entities.UserModels,
                entities.UserModelKeyLlm,
                entities.UserModelFineTune,
                entities.SystemModels,
                entities.SystemModelKeyLlm,
            ]),
            models_admin_module_1.ModelsAdminModule,
            models_user_module_1.ModelsUserModule,
        ],
        providers: [],
        exports: [
            models_admin_module_1.ModelsAdminModule,
            models_user_module_1.ModelsUserModule,
            typeorm_1.TypeOrmModule,
        ],
    })
], ModelsModule);
//# sourceMappingURL=models.module.js.map