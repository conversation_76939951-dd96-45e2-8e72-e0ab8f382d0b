"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserTaskExecution = void 0;
const typeorm_1 = require("typeorm");
const task_execution_status_enum_1 = require("../enums/task-execution-status.enum");
let UserTaskExecution = class UserTaskExecution {
    taskExecutionId;
    taskId;
    startTime;
    endTime;
    overallStatus;
    executionDetails;
    createdAt;
};
exports.UserTaskExecution = UserTaskExecution;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid', { name: 'task_execution_id' }),
    __metadata("design:type", String)
], UserTaskExecution.prototype, "taskExecutionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'task_id', type: 'uuid', nullable: false }),
    __metadata("design:type", String)
], UserTaskExecution.prototype, "taskId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'start_time', type: 'bigint', nullable: false }),
    __metadata("design:type", Number)
], UserTaskExecution.prototype, "startTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'end_time', type: 'bigint', nullable: true }),
    __metadata("design:type", Number)
], UserTaskExecution.prototype, "endTime", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'overall_status',
        type: 'enum',
        enum: task_execution_status_enum_1.TaskExecutionStatus,
        default: task_execution_status_enum_1.TaskExecutionStatus.RUNNING,
        nullable: false,
    }),
    __metadata("design:type", String)
], UserTaskExecution.prototype, "overallStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'execution_details', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], UserTaskExecution.prototype, "executionDetails", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'created_at',
        type: 'bigint',
        default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    }),
    __metadata("design:type", Number)
], UserTaskExecution.prototype, "createdAt", void 0);
exports.UserTaskExecution = UserTaskExecution = __decorate([
    (0, typeorm_1.Entity)('user_task_executions')
], UserTaskExecution);
//# sourceMappingURL=user-task-execution.entity.js.map