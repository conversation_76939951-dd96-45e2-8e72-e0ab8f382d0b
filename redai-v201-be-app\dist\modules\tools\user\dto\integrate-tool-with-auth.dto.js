"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntegrateToolWithAuthResponseDto = exports.IntegrateToolWithAuthDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const integrate_openapi_dto_1 = require("./integrate-openapi.dto");
const auth_config_dto_1 = require("./auth-config.dto");
class IntegrateToolWithAuthDto extends integrate_openapi_dto_1.IntegrateOpenApiDto {
    authConfig;
}
exports.IntegrateToolWithAuthDto = IntegrateToolWithAuthDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cấu hình xác thực',
        oneOf: [
            { $ref: '#/components/schemas/ApiKeyAuthDto' },
            { $ref: '#/components/schemas/OAuthAuthDto' },
            { $ref: '#/components/schemas/NoAuthDto' }
        ],
        example: {
            authType: auth_config_dto_1.AuthTypeEnum.API_KEY,
            schemeName: 'ApiKeyAuth',
            apiKey: 'api_key_123456',
            apiKeyLocation: 'header',
            paramName: 'X-API-KEY'
        }
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => auth_config_dto_1.AuthConfigDto, {
        discriminator: {
            property: 'authType',
            subTypes: [
                { value: auth_config_dto_1.ApiKeyAuthDto, name: auth_config_dto_1.AuthTypeEnum.API_KEY },
                { value: auth_config_dto_1.OAuthAuthDto, name: auth_config_dto_1.AuthTypeEnum.OAUTH },
                { value: auth_config_dto_1.NoAuthDto, name: auth_config_dto_1.AuthTypeEnum.NONE }
            ]
        }
    }),
    __metadata("design:type", Object)
], IntegrateToolWithAuthDto.prototype, "authConfig", void 0);
class IntegrateToolWithAuthResponseDto {
    toolsCreated;
    resourcesCreated;
    authConfig;
}
exports.IntegrateToolWithAuthResponseDto = IntegrateToolWithAuthResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số lượng công cụ đã tạo',
        example: 5
    }),
    __metadata("design:type", Number)
], IntegrateToolWithAuthResponseDto.prototype, "toolsCreated", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số lượng tài nguyên đã tạo',
        example: 3
    }),
    __metadata("design:type", Number)
], IntegrateToolWithAuthResponseDto.prototype, "resourcesCreated", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin về cấu hình xác thực đã tạo',
        example: {
            apiKeyCreated: 1,
            oauthCreated: 0
        }
    }),
    __metadata("design:type", Object)
], IntegrateToolWithAuthResponseDto.prototype, "authConfig", void 0);
//# sourceMappingURL=integrate-tool-with-auth.dto.js.map