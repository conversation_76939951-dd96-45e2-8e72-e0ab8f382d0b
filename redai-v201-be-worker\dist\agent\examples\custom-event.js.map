{"version": 3, "sources": ["../../../src/agent/examples/custom-event.ts"], "sourcesContent": ["import { dispatchCustomEvent } from '@langchain/core/callbacks/dispatch';\r\nimport { MessagesAnnotation, StateGraph } from '@langchain/langgraph';\r\n\r\nconst graphNode = async (_state: typeof MessagesAnnotation.State) => {\r\n  const chunks = [\r\n    'Four',\r\n    'score',\r\n    'and',\r\n    'seven',\r\n    'years',\r\n    'ago',\r\n    'our',\r\n    'fathers',\r\n    '...',\r\n  ];\r\n  for (const chunk of chunks) {\r\n    await dispatchCustomEvent('my_custom_event', { chunk });\r\n  }\r\n  return {\r\n    messages: [\r\n      {\r\n        role: 'assistant',\r\n        content: chunks.join(' '),\r\n      },\r\n    ],\r\n  };\r\n};\r\n\r\nconst graphWithDispatch = new StateGraph(MessagesAnnotation)\r\n  .addNode('model', graphNode)\r\n  .addEdge('__start__', 'model')\r\n  .compile();\r\n\r\nasync function test() {\r\n  const eventStream = graphWithDispatch.streamEvents(\r\n    {\r\n      messages: [\r\n        {\r\n          role: 'user',\r\n          content: 'What are you thinking about?',\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      version: 'v2',\r\n    },\r\n  );\r\n\r\n  for await (const { event, name, data } of eventStream) {\r\n    if (event === 'on_custom_event' && name === 'my_custom_event') {\r\n      console.log(`${data.chunk}|`);\r\n    }\r\n  }\r\n}\r\n\r\ntest().then();\r\n"], "names": ["graphNode", "_state", "chunks", "chunk", "dispatchCustomEvent", "messages", "role", "content", "join", "graphWithDispatch", "StateGraph", "MessagesAnnotation", "addNode", "addEdge", "compile", "test", "eventStream", "streamEvents", "version", "event", "name", "data", "console", "log", "then"], "mappings": ";;;;0BAAoC;2BACW;AAE/C,MAAMA,YAAY,OAAOC;IACvB,MAAMC,SAAS;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,KAAK,MAAMC,SAASD,OAAQ;QAC1B,MAAME,IAAAA,6BAAmB,EAAC,mBAAmB;YAAED;QAAM;IACvD;IACA,OAAO;QACLE,UAAU;YACR;gBACEC,MAAM;gBACNC,SAASL,OAAOM,IAAI,CAAC;YACvB;SACD;IACH;AACF;AAEA,MAAMC,oBAAoB,IAAIC,qBAAU,CAACC,6BAAkB,EACxDC,OAAO,CAAC,SAASZ,WACjBa,OAAO,CAAC,aAAa,SACrBC,OAAO;AAEV,eAAeC;IACb,MAAMC,cAAcP,kBAAkBQ,YAAY,CAChD;QACEZ,UAAU;YACR;gBACEC,MAAM;gBACNC,SAAS;YACX;SACD;IACH,GACA;QACEW,SAAS;IACX;IAGF,WAAW,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,IAAI,EAAE,IAAIL,YAAa;QACrD,IAAIG,UAAU,qBAAqBC,SAAS,mBAAmB;YAC7DE,QAAQC,GAAG,CAAC,GAAGF,KAAKlB,KAAK,CAAC,CAAC,CAAC;QAC9B;IACF;AACF;AAEAY,OAAOS,IAAI"}