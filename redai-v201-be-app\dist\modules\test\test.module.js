"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestModule = void 0;
const common_1 = require("@nestjs/common");
const test_upload_controller_1 = require("./controllers/test-upload.controller");
const test_upload_service_1 = require("./services/test-upload.service");
const s3_service_1 = require("../../shared/services/s3.service");
let TestModule = class TestModule {
};
exports.TestModule = TestModule;
exports.TestModule = TestModule = __decorate([
    (0, common_1.Module)({
        controllers: [test_upload_controller_1.TestUploadController],
        providers: [test_upload_service_1.TestUploadService, s3_service_1.S3Service],
        exports: [test_upload_service_1.TestUploadService],
    })
], TestModule);
//# sourceMappingURL=test.module.js.map