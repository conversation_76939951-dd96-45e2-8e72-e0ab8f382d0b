"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenAiService = void 0;
const common_1 = require("@nestjs/common");
let OpenAiService = class OpenAiService {
    createVectorStore = jest.fn().mockResolvedValue({
        id: 'mock-vector-store-id',
        object: 'vector_store',
        created_at: Date.now(),
        name: 'Mock Vector Store'
    });
    createVectorStoreWithFile = jest.fn().mockImplementation((config, fileId) => {
        return Promise.resolve({
            vectorStoreId: 'mock-vector-store-id'
        });
    });
    createVectorStoreWithMultipleFiles = jest.fn().mockImplementation((config, fileIds) => {
        const errors = [];
        let successCount = 0;
        fileIds.forEach(fileId => {
            if (fileId.startsWith('error')) {
                errors.push({
                    fileId,
                    message: 'Mock error for testing'
                });
            }
            else {
                successCount++;
            }
        });
        return Promise.resolve({
            vectorStoreId: 'mock-vector-store-id',
            successCount,
            errorCount: errors.length,
            errors: errors.length > 0 ? errors : undefined
        });
    });
    attachFileToVectorStore = jest.fn().mockImplementation((vectorStoreId, fileId) => {
        return Promise.resolve({
            id: `mock-vector-file-${Date.now()}`,
            object: 'vector_store_file',
            created_at: Date.now(),
            vector_store_id: vectorStoreId,
            file_id: fileId
        });
    });
    uploadFileToVectorStore = jest.fn().mockImplementation((vectorStoreId, fileKey) => {
        return Promise.resolve({
            id: `mock-vector-file-${Date.now()}`,
            object: 'vector_store_file',
            created_at: Date.now(),
            vector_store_id: vectorStoreId,
            file_id: `mock-file-${Date.now()}`
        });
    });
    deleteVectorStoreFile = jest.fn().mockResolvedValue({
        deleted: true,
        vectorStoreId: 'mock-vector-store-id',
        fileId: 'mock-file-id'
    });
    deleteVectorStore = jest.fn().mockResolvedValue({
        deleted: true,
        id: 'mock-vector-store-id'
    });
    deleteOpenAIFile = jest.fn().mockResolvedValue({
        deleted: true,
        id: 'mock-file-id'
    });
};
exports.OpenAiService = OpenAiService;
exports.OpenAiService = OpenAiService = __decorate([
    (0, common_1.Injectable)()
], OpenAiService);
//# sourceMappingURL=openai.service.js.map