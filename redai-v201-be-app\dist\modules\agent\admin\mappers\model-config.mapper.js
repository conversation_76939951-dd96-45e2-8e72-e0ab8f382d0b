"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelConfigMapper = void 0;
class ModelConfigMapper {
    static toResponseDto(modelConfig) {
        return {
            temperature: modelConfig?.temperature,
            top_p: modelConfig?.top_p,
            top_k: modelConfig?.top_k,
            max_tokens: modelConfig?.max_tokens,
        };
    }
}
exports.ModelConfigMapper = ModelConfigMapper;
//# sourceMappingURL=model-config.mapper.js.map