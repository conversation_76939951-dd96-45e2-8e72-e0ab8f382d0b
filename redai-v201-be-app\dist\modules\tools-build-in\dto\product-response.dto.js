"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductSearchResponseDto = exports.ProductCategoryResponseDto = exports.ProductResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const constants_1 = require("../constants");
class ProductResponseDto {
    id;
    name;
    description;
    price;
    typePrice;
    images;
    tags;
    shipmentConfig;
    status;
    createdAt;
    updatedAt;
}
exports.ProductResponseDto = ProductResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của sản phẩm',
        example: 1,
        nullable: true
    }),
    __metadata("design:type", Number)
], ProductResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên sản phẩm',
        example: 'Áo thun nam'
    }),
    __metadata("design:type", String)
], ProductResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả sản phẩm',
        example: 'Áo thun nam chất liệu cotton 100%',
        nullable: true
    }),
    __metadata("design:type", String)
], ProductResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Giá sản phẩm',
        example: {
            amount: 150000,
            currency: 'VND',
            discount: 10,
            finalAmount: 135000
        }
    }),
    __metadata("design:type", Object)
], ProductResponseDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Kiểu giá',
        enum: constants_1.PriceTypeEnum,
        example: constants_1.PriceTypeEnum.HAS_PRICE
    }),
    __metadata("design:type", String)
], ProductResponseDto.prototype, "typePrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Hình ảnh sản phẩm',
        example: {
            main: 'https://example.com/image1.jpg',
            gallery: ['https://example.com/image2.jpg', 'https://example.com/image3.jpg']
        },
        nullable: true
    }),
    __metadata("design:type", Object)
], ProductResponseDto.prototype, "images", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Các tag của sản phẩm',
        example: {
            categories: ['thời trang', 'nam'],
            colors: ['đen', 'trắng', 'xanh']
        },
        nullable: true
    }),
    __metadata("design:type", Object)
], ProductResponseDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cấu hình vận chuyển',
        example: {
            widthCm: 25,
            heightCm: 5,
            lengthCm: 30,
            weightGram: 200
        },
        nullable: true
    }),
    __metadata("design:type", Object)
], ProductResponseDto.prototype, "shipmentConfig", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái sản phẩm',
        enum: constants_1.UserProductStatusEnum,
        example: constants_1.UserProductStatusEnum.PUBLISHED,
        nullable: true
    }),
    __metadata("design:type", String)
], ProductResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo',
        example: 1672531200000,
        nullable: true
    }),
    __metadata("design:type", Number)
], ProductResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian cập nhật',
        example: 1672531200000,
        nullable: true
    }),
    __metadata("design:type", Number)
], ProductResponseDto.prototype, "updatedAt", void 0);
class ProductCategoryResponseDto {
    id;
    name;
    description;
    parentId;
    displayOrder;
}
exports.ProductCategoryResponseDto = ProductCategoryResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của danh mục',
        example: 1,
        nullable: true
    }),
    __metadata("design:type", Number)
], ProductCategoryResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên danh mục',
        example: 'Thời trang nam'
    }),
    __metadata("design:type", String)
], ProductCategoryResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mô tả danh mục',
        example: 'Các sản phẩm thời trang dành cho nam giới',
        nullable: true
    }),
    __metadata("design:type", String)
], ProductCategoryResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh mục cha',
        example: 2,
        nullable: true
    }),
    __metadata("design:type", Number)
], ProductCategoryResponseDto.prototype, "parentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thứ tự hiển thị',
        example: 1,
        nullable: true
    }),
    __metadata("design:type", Number)
], ProductCategoryResponseDto.prototype, "displayOrder", void 0);
class ProductSearchResponseDto {
    products;
    total;
    keyword;
    filters;
}
exports.ProductSearchResponseDto = ProductSearchResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách sản phẩm tìm thấy',
        type: [ProductResponseDto]
    }),
    __metadata("design:type", Array)
], ProductSearchResponseDto.prototype, "products", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tổng số kết quả',
        example: 50
    }),
    __metadata("design:type", Number)
], ProductSearchResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Từ khóa tìm kiếm',
        example: 'áo thun',
        nullable: true
    }),
    __metadata("design:type", String)
], ProductSearchResponseDto.prototype, "keyword", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Bộ lọc đã áp dụng',
        example: {
            priceRange: { min: 100000, max: 500000 },
            categories: [1, 2, 3]
        },
        nullable: true
    }),
    __metadata("design:type", Object)
], ProductSearchResponseDto.prototype, "filters", void 0);
//# sourceMappingURL=product-response.dto.js.map