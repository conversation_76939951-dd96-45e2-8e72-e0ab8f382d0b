"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateFineTuningJobResponseDto = exports.CreateFineTuningJobDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const provider_enum_1 = require("../../../constants/provider.enum");
class CreateFineTuningJobDto {
    name;
    description;
    datasetId;
    baseModelId;
    provider;
    userKeyLlmId;
    suffix;
    hyperparameters;
    googleCloud;
}
exports.CreateFineTuningJobDto = CreateFineTuningJobDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên hiển thị cho model fine-tuned',
        example: 'My Custom GPT Model',
        maxLength: 255,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateFineTuningJobDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả về model fine-tuned',
        example: 'Model được fine-tune cho tác vụ customer support',
        maxLength: 1000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateFineTuningJobDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'UUID của dataset để fine-tune',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateFineTuningJobDto.prototype, "datasetId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'UUID của model cơ sở để fine-tune',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateFineTuningJobDto.prototype, "baseModelId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nhà cung cấp AI',
        enum: provider_enum_1.ProviderFineTuneEnum,
        example: provider_enum_1.ProviderFineTuneEnum.OPENAI,
    }),
    (0, class_validator_1.IsEnum)(provider_enum_1.ProviderFineTuneEnum),
    __metadata("design:type", String)
], CreateFineTuningJobDto.prototype, "provider", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'UUID của user key LLM (tùy chọn - nếu user muốn sử dụng key riêng)',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateFineTuningJobDto.prototype, "userKeyLlmId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Suffix cho tên model (chỉ cho OpenAI)',
        example: 'my-model',
        maxLength: 40,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.ValidateIf)(o => o.provider === provider_enum_1.ProviderFineTuneEnum.OPENAI),
    __metadata("design:type", String)
], CreateFineTuningJobDto.prototype, "suffix", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Siêu tham số cho quá trình fine-tuning',
        type: 'object',
        additionalProperties: true,
        example: {
            epochs: 3,
            batchSize: 'auto',
            learningRate: 0.0001
        }
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], CreateFineTuningJobDto.prototype, "hyperparameters", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Thông tin Google Cloud (chỉ cho Google AI)',
        type: 'object',
        additionalProperties: true,
        example: {
            projectId: 'my-ai-project',
            location: 'us-central1',
            bucketName: 'my-training-data'
        }
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateIf)(o => o.provider === provider_enum_1.ProviderFineTuneEnum.GOOGLE),
    __metadata("design:type", Object)
], CreateFineTuningJobDto.prototype, "googleCloud", void 0);
class CreateFineTuningJobResponseDto {
    jobId;
    jobName;
    status;
    baseModel;
    trainingFileId;
    trainingDataUri;
    estimatedTokens;
    costDeducted;
    remainingBalance;
    userKeyLlmId;
    userKeyLlmName;
    createdAt;
}
exports.CreateFineTuningJobResponseDto = CreateFineTuningJobResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của fine-tuning job',
        example: 'ftjob-abc123',
    }),
    __metadata("design:type", String)
], CreateFineTuningJobResponseDto.prototype, "jobId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tên của fine-tuning job (cho Google AI)',
        example: 'projects/my-project/locations/us-central1/tuningJobs/123',
    }),
    __metadata("design:type", String)
], CreateFineTuningJobResponseDto.prototype, "jobName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái của job',
        example: 'queued',
    }),
    __metadata("design:type", String)
], CreateFineTuningJobResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của model cơ sở',
        example: 'gpt-3.5-turbo',
    }),
    __metadata("design:type", String)
], CreateFineTuningJobResponseDto.prototype, "baseModel", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID của file training đã upload (cho OpenAI)',
        example: 'file-abc123',
    }),
    __metadata("design:type", String)
], CreateFineTuningJobResponseDto.prototype, "trainingFileId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URI của file training trong GCS (cho Google AI)',
        example: 'gs://my-bucket/training-data.jsonl',
    }),
    __metadata("design:type", String)
], CreateFineTuningJobResponseDto.prototype, "trainingDataUri", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số lượng token đã tính toán',
        example: 50000,
    }),
    __metadata("design:type", Number)
], CreateFineTuningJobResponseDto.prototype, "estimatedTokens", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Chi phí đã trừ (R-Points)',
        example: 100,
    }),
    __metadata("design:type", Number)
], CreateFineTuningJobResponseDto.prototype, "costDeducted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số dư R-Points còn lại',
        example: 9900,
    }),
    __metadata("design:type", Number)
], CreateFineTuningJobResponseDto.prototype, "remainingBalance", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID của user key LLM đã sử dụng (nếu có)',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    __metadata("design:type", String)
], CreateFineTuningJobResponseDto.prototype, "userKeyLlmId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tên của user key LLM đã sử dụng (nếu có)',
        example: 'My OpenAI Key',
    }),
    __metadata("design:type", String)
], CreateFineTuningJobResponseDto.prototype, "userKeyLlmName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo job (epoch millis)',
        example: 1640995200000,
    }),
    __metadata("design:type", Number)
], CreateFineTuningJobResponseDto.prototype, "createdAt", void 0);
//# sourceMappingURL=create-fine-tuning-job.dto.js.map