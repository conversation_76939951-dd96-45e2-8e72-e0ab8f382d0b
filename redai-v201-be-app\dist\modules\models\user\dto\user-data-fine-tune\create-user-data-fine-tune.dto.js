"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUserDataFineTuneDto = void 0;
const constants_1 = require("../../../constants");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateUserDataFineTuneDto {
    name;
    description;
    provider;
    trainDataset;
    validDataset;
}
exports.CreateUserDataFineTuneDto = CreateUserDataFineTuneDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên của bộ dữ liệu fine-tune',
        example: 'Customer Support Dataset',
        maxLength: 255,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateUserDataFineTuneDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả nội dung bộ dữ liệu',
        example: 'Dataset chứa các cuộc hội thoại hỗ trợ khách hàng',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateUserDataFineTuneDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nhà cung cấp AI',
        example: constants_1.ProviderFineTuneEnum.OPENAI,
        enum: constants_1.ProviderFineTuneEnum,
    }),
    (0, class_validator_1.IsEnum)(constants_1.ProviderFineTuneEnum),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateUserDataFineTuneDto.prototype, "provider", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tập dữ liệu huấn luyện, định dạng JSON',
        example: 'application/jsonl'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateUserDataFineTuneDto.prototype, "trainDataset", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tập dữ liệu validation, định dạng JSON (nếu có)',
        example: 'application/jsonl'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateUserDataFineTuneDto.prototype, "validDataset", void 0);
//# sourceMappingURL=create-user-data-fine-tune.dto.js.map