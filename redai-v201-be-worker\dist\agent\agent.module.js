"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AgentModule", {
    enumerable: true,
    get: function() {
        return AgentModule;
    }
});
const _common = require("@nestjs/common");
const _testmcpclient = require("./mcp/test-mcp-client");
const _streamcontroller = require("./worker/stream.controller");
const _rediseventcontroller = require("./controllers/redis-event.controller");
const _database = require("./database");
const _redissubscribermodule = require("./redis-subscriber.module");
const _apikeyencryptionhelper = require("./helper/api-key-encryption.helper");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
let AgentModule = class AgentModule {
};
AgentModule = _ts_decorate([
    (0, _common.Module)({
        imports: [
            _redissubscribermodule.RedisSubscriberModule
        ],
        controllers: [
            _streamcontroller.StreamController,
            _rediseventcontroller.RedisEventController
        ],
        providers: [
            {
                provide: 'McpClient',
                useFactory: async ()=>{
                    await (0, _testmcpclient.createMcpUserClient)();
                }
            },
            _database.AgentDatabaseService,
            _database.UserAgentRunsQueries,
            _database.UserMessagesQueries,
            _apikeyencryptionhelper.ApiKeyEncryptionHelper
        ],
        exports: [
            _database.AgentDatabaseService,
            _database.UserAgentRunsQueries,
            _database.UserMessagesQueries
        ]
    })
], AgentModule);

//# sourceMappingURL=agent.module.js.map