"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddGoogleIdToUsers1720000000000 = void 0;
class AddGoogleIdToUsers1720000000000 {
    async up(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE users
      ADD COLUMN IF NOT EXISTS google_id VARCHAR(255) UNIQUE COMMENT 'Google ID';
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE users
      DROP COLUMN IF EXISTS google_id;
    `);
    }
}
exports.AddGoogleIdToUsers1720000000000 = AddGoogleIdToUsers1720000000000;
//# sourceMappingURL=1720000000000-AddGoogleIdToUsers.js.map