{"version": 3, "sources": ["../../../../src/agent/system/core/react-agent-executor.ts"], "sourcesContent": ["import {\n  BaseMessage,\n  isAIMessage,\n  SystemMessage,\n  ToolMessage,\n} from '@langchain/core/messages';\nimport { RunnableConfig } from '@langchain/core/runnables';\nimport {\n  BaseCheckpointSaver,\n  BaseStore,\n} from '@langchain/langgraph-checkpoint';\nimport {\n  Annotation,\n  AnnotationRoot,\n  Command,\n  CompiledStateGraph,\n  END,\n  interrupt,\n  LangGraphRunnableConfig,\n  Messages,\n  messagesStateReducer,\n  START,\n  StateGraph,\n} from '@langchain/langgraph';\nimport {\n  InterruptShapeInterface,\n  InterruptValue,\n  SystemAgentConfig,\n  SystemAgentConfigMap,\n} from '../../interfaces';\nimport { ModelProviderEnum } from '../../enums';\nimport { DynamicStructuredTool } from '@langchain/core/dist/tools';\nimport { MultiServerMCPClient } from '@langchain/mcp-adapters';\nimport { ToolNode } from '@langchain/langgraph/prebuilt';\nimport { Logger } from '@nestjs/common';\nimport { getHandoffTool } from './helpers';\nimport {\n  SUPERVISOR_TAG,\n  SUPERVISOR_TOOL_CALL_TAG,\n  WORKER_TAG,\n  WORKER_TOOL_CALL_TAG,\n} from './constants';\nimport { initChatModel } from 'langchain/chat_models/universal';\n\nconst logger = new Logger('ReactAgentExecutor');\n\nconst CHAT_MODEL_WITHOUT_PARALLEL_TOOL_CALL_PARAM = new Set([\n  'o1',\n  'o1-2024-12-17',\n  'o1-preview-2024-09-12',\n  'o1-pro',\n  'o1-pro-2025-03-19',\n  'o3',\n  'o3-2025-04-16',\n  'o4-mini',\n  'o4-mini-2025-04-16',\n  'o3-mini',\n  'o3-mini-2025-01-31',\n  'o1-mini',\n  'o1-mini-2024-09-12',\n]);\n\nexport type N = typeof START | 'agent' | 'humanReview' | 'tools' | typeof END;\n\nexport function createReactAgentAnnotation() {\n  return Annotation.Root({\n    messages: Annotation<BaseMessage[], Messages>({\n      reducer: messagesStateReducer,\n      default: () => [],\n    }),\n    activeAgent: Annotation<string>({\n      reducer: (x, y) => {\n        logger.log(`transferring from ${x} to ${y}`);\n        return y;\n      },\n      default: () => 'supervisor',\n    }),\n    metadata: Annotation<Record<string, any>>,\n  });\n}\n\nexport const GraphState = createReactAgentAnnotation();\n\nexport type AgentState = (typeof GraphState)['State'];\n\n// Define custom configurable type for runtime configuration\nexport type CustomConfigurableType = {\n  alwaysApproveToolCall?: boolean;\n  thread_id?: string;\n  agentConfigMap?: SystemAgentConfigMap;\n  supervisorAgentId?: string;\n  multiMcpClients?: MultiServerMCPClient;\n};\n\n// Define custom runnable config with our configurable type\nexport type CustomRunnableConfig = RunnableConfig<CustomConfigurableType>;\n\nexport type CreateReactAgentParams = {\n  checkpointSaver?: BaseCheckpointSaver;\n  store?: BaseStore;\n  defaultTools?: DynamicStructuredTool[];\n};\n\nexport function createReactAgent<\n  A extends AnnotationRoot<any> = ReturnType<typeof createReactAgentAnnotation>,\n>(\n  params: CreateReactAgentParams,\n): CompiledStateGraph<A['State'], A['Update'], any, A['spec'], A['spec']> {\n  const { checkpointSaver, store } = params;\n\n  const shouldContinue = (\n    state: (typeof GraphState)['State'],\n    config?: CustomRunnableConfig,\n  ): N => {\n    const { messages } = state;\n    const lastMessage = messages[messages.length - 1];\n\n    if (isAIMessage(lastMessage)) {\n      const aiMessage = lastMessage;\n      if (!aiMessage.tool_calls || aiMessage.tool_calls.length === 0) {\n        return END;\n      }\n\n      const alwaysApproveToolCall =\n        config?.configurable?.alwaysApproveToolCall || false;\n      return alwaysApproveToolCall ? 'tools' : 'humanReview';\n    }\n\n    return END;\n  };\n\n  const humanReviewNode = (state: AgentState, config: CustomRunnableConfig) => {\n    const { messages } = state;\n    const lastMessage = messages[messages.length - 1];\n\n    if (!lastMessage || !isAIMessage(lastMessage)) {\n      throw new Error('Last message is not an AI message');\n    }\n\n    const aiMessage = lastMessage;\n\n    // Check for errored tool calls (missing name or id)\n    const erroredToolCalls =\n      aiMessage.tool_calls?.filter(\n        (toolCall) =>\n          !toolCall.name ||\n          toolCall.name.trim() === '' ||\n          !toolCall.id ||\n          toolCall.id.trim() === '',\n      ) || [];\n\n    if (erroredToolCalls.length > 0) {\n      throw new Error(\n        `Found ${erroredToolCalls.length} errored tool calls: ${JSON.stringify(erroredToolCalls)}. Tool calls must have both name and id.`,\n      );\n    }\n\n    // Note: If alwaysApproveToolCall is true, we wouldn't reach this node\n    // as shouldContinue would have routed directly to tools\n\n    // Display the tool calls to the user and ask for approval\n    const toolCalls =\n      aiMessage.tool_calls\n        ?.map((toolCall, index) => {\n          return `Tool ${index + 1}: ${toolCall.name}\\nArguments: ${JSON.stringify(toolCall.args, null, 2)}`;\n        })\n        .join('\\n\\n') || '';\n    const name =\n      state.activeAgent === config.configurable?.supervisorAgentId\n        ? 'supervisor'\n        : 'worker';\n    const { choice } = interrupt<string, InterruptShapeInterface>(\n      JSON.stringify({\n        role: name,\n        prompt: `The AI wants to use the following tools:\\n\\n${toolCalls}\\n\\nDo you approve these tool calls? (yes/no/always)`,\n        prompter:\n          config?.configurable?.agentConfigMap?.[state.activeAgent]?.name || '',\n        prompterId: state.activeAgent,\n      } as InterruptValue),\n    );\n\n    // Handle user response\n    if (choice === 'always' || choice === 'yes') {\n      // When user selects 'always', we just route to tools\n      // The frontend should capture this response and set alwaysApproveToolCall=true in the configurable for future calls\n      // We can't set configurable in the Command object\n      return new Command({\n        goto: 'tools',\n      });\n    } else {\n      // User rejected the tool calls, create rejection tool messages\n      const rejectionMessages =\n        aiMessage.tool_calls?.map((toolCall) => {\n          return new ToolMessage({\n            content: 'Tool call was rejected by the user.',\n            tool_call_id: toolCall.id ?? '',\n            name: toolCall.name,\n          });\n        }) || [];\n\n      // Return to the worker with the rejection messages\n      return new Command({\n        update: {\n          messages: rejectionMessages,\n        },\n        goto: 'agent',\n      });\n    }\n  };\n\n  const callModel = async (\n    state: typeof GraphState.State,\n    config?: CustomRunnableConfig,\n  ) => {\n    // Get the active worker ID from state\n    const activeAgentId = state.activeAgent;\n    // Get the worker configuration from the config\n    const agentConfig = config?.configurable?.agentConfigMap?.[activeAgentId];\n    if (!agentConfig) {\n      throw new Error(`No configuration found for agent: ${activeAgentId}`);\n    }\n    // Create a model instance based on the worker config\n    const modelConfig = agentConfig.model;\n    const dynamicLLM = await initChatModel(\n      `${modelConfig.provider.toLowerCase()}:${modelConfig.name}`,\n      {\n        configurableFields: modelConfig.samplingParameters,\n        ...modelConfig.parameters,\n      },\n    );\n    // todo: modify _callTool\n    const dynamicTools =\n      (await config?.configurable?.multiMcpClients?.getTools(\n        ...agentConfig.mcpConfig.map((mcp) => mcp.serverName),\n      )) || [];\n    const toolInstances = [...dynamicTools, ...(params.defaultTools || [])];\n    const handoffTool = getHandoffTool(config);\n    if (handoffTool) {\n      toolInstances.push(handoffTool);\n    }\n    // Bind tools to the model\n    // Check if the model is OpenAI to conditionally set parallel_tool_calls\n    const condition =\n      modelConfig.provider !== ModelProviderEnum.OPENAI ||\n      CHAT_MODEL_WITHOUT_PARALLEL_TOOL_CALL_PARAM.has(modelConfig.name);\n\n    const modelWithTools = condition\n      ? dynamicLLM.bindTools(toolInstances)\n      : dynamicLLM.bindTools(toolInstances, {\n          parallel_tool_calls: false,\n        });\n\n    // Create a prompt runnable with the worker's system prompt\n    const systemMessage = new SystemMessage(agentConfig.instruction);\n\n    // Filter messages to only include those from the current worker\n    const inputMessages = state.messages;\n\n    const input = [systemMessage, ...inputMessages];\n\n    const tag =\n      agentConfig.id === config?.configurable?.supervisorAgentId\n        ? SUPERVISOR_TAG\n        : WORKER_TAG;\n    // Invoke the model\n    const response = await modelWithTools.invoke(input, {\n      ...config,\n      tags: [tag],\n    });\n\n    // Add worker name to the response\n    response.response_metadata.invoker = activeAgentId;\n    if (response.lc_kwargs) {\n      response.lc_kwargs.name = `${activeAgentId}-${agentConfig.name}`;\n    }\n\n    return {\n      messages: [response],\n    };\n  };\n\n  const wrappedToolNode = async (\n    state: AgentState,\n    config?: LangGraphRunnableConfig,\n  ) => {\n    const { messages: prevMessages, activeAgent } = state;\n    const lastMessage = prevMessages.at(-1);\n    if (!lastMessage) {\n      throw new Error('No messages found');\n    }\n    if (isAIMessage(lastMessage)) {\n      const aiMessage = lastMessage;\n      if (aiMessage.tool_calls && aiMessage.tool_calls.length > 0) {\n        const erroredToolCalls = aiMessage.tool_calls.filter(\n          (toolCall) =>\n            !toolCall.name ||\n            toolCall.name.trim() === '' ||\n            !toolCall.id ||\n            toolCall.id.trim() === '',\n        );\n        if (erroredToolCalls.length > 0) {\n          throw new Error(\n            `Found ${erroredToolCalls.length} errored tool calls: ${JSON.stringify(erroredToolCalls)}. Tool calls must have both name and id.`,\n          );\n        }\n      }\n    }\n\n    const agentConfig = config?.configurable?.agentConfigMap?.[activeAgent];\n    if (!agentConfig) {\n      throw new Error(`No configuration found for agent: ${activeAgent}`);\n    }\n    const realAgentConfig = agentConfig as SystemAgentConfig;\n\n    // todo: modify _callTool\n    const dynamicTools =\n      (await config?.configurable?.multiMcpClients?.getTools(\n        ...agentConfig.mcpConfig.map((mcp) => mcp.serverName),\n      )) || [];\n    const toolInstances = [...dynamicTools, ...(params.defaultTools || [])];\n    const handoffTool = getHandoffTool(config);\n    if (handoffTool) {\n      toolInstances.push(handoffTool);\n    }\n\n    const tag =\n      realAgentConfig.id === config?.configurable?.supervisorAgentId\n        ? SUPERVISOR_TOOL_CALL_TAG\n        : WORKER_TOOL_CALL_TAG;\n    // Create a dynamic tool node\n    const dynamicToolNode = new ToolNode(toolInstances, {\n      handleToolErrors: true,\n    });\n    const raw = await dynamicToolNode.invoke(state, {\n      ...config,\n      tags: [tag],\n    });\n\n    // Flatten raw into a single output array of either Commands or BaseMessage[]\n    const output: any[] = [];\n    if (Array.isArray(raw)) {\n      logger.log('raw array');\n      output.push(...raw);\n    } else if (raw instanceof Command) {\n      logger.log('raw command');\n      output.push(raw);\n    } else if (Array.isArray(raw.messages)) {\n      logger.log('raw messages');\n      output.push(...raw.messages);\n    } else {\n      logger.error('bad raw');\n      throw new Error('wrappedToolNode: unexpected return shape');\n    }\n    const hasCommand = output.some((item) => item instanceof Command);\n    if (hasCommand) {\n      return output;\n    }\n    // Tag every BaseMessage with invoker\n    const messages: BaseMessage[] = [];\n    for (const item of output) {\n      // item is either a BaseMessage or an object with `.messages`\n      if (item instanceof BaseMessage) {\n        item.response_metadata = {\n          ...item.response_metadata,\n          invoker: state.activeAgent,\n        };\n        messages.push(item);\n      }\n    }\n    return { messages };\n  };\n\n  const workflow = new StateGraph(GraphState)\n    .addNode('agent', callModel)\n    .addNode('humanReview', humanReviewNode, { ends: ['tools', 'agent'] })\n    .addNode('tools', wrappedToolNode)\n    .addEdge(START, 'agent')\n    .addConditionalEdges('agent', shouldContinue, {\n      humanReview: 'humanReview',\n      tools: 'tools',\n      [END]: END,\n    })\n    .addEdge('tools', 'agent');\n  return workflow.compile({\n    checkpointer: checkpointSaver,\n    store,\n  });\n}\n"], "names": ["GraphState", "createReactAgent", "createReactAgentAnnotation", "logger", "<PERSON><PERSON>", "CHAT_MODEL_WITHOUT_PARALLEL_TOOL_CALL_PARAM", "Set", "Annotation", "Root", "messages", "reducer", "messagesStateReducer", "default", "activeAgent", "x", "y", "log", "metadata", "params", "checkpointSaver", "store", "shouldC<PERSON><PERSON>ue", "state", "config", "lastMessage", "length", "isAIMessage", "aiMessage", "tool_calls", "END", "alwaysApproveToolCall", "configurable", "humanReviewNode", "Error", "erroredToolCalls", "filter", "toolCall", "name", "trim", "id", "JSON", "stringify", "toolCalls", "map", "index", "args", "join", "supervisorAgentId", "choice", "interrupt", "role", "prompt", "prompter", "agentConfigMap", "prompterId", "Command", "goto", "rejectionMessages", "ToolMessage", "content", "tool_call_id", "update", "callModel", "activeAgentId", "agentConfig", "modelConfig", "model", "dynamicLLM", "initChatModel", "provider", "toLowerCase", "configurableFields", "samplingParameters", "parameters", "dynamicTools", "multiMcpClients", "getTools", "mcpConfig", "mcp", "serverName", "toolInstances", "defaultTools", "handoffTool", "getHandoffTool", "push", "condition", "ModelProviderEnum", "OPENAI", "has", "modelWithTools", "bindTools", "parallel_tool_calls", "systemMessage", "SystemMessage", "instruction", "inputMessages", "input", "tag", "SUPERVISOR_TAG", "WORKER_TAG", "response", "invoke", "tags", "response_metadata", "invoker", "lc_kwargs", "wrappedToolNode", "prevMessages", "at", "realAgentConfig", "SUPERVISOR_TOOL_CALL_TAG", "WORKER_TOOL_CALL_TAG", "dynamicToolNode", "ToolNode", "handleToolErrors", "raw", "output", "Array", "isArray", "error", "has<PERSON>ommand", "some", "item", "BaseMessage", "workflow", "StateGraph", "addNode", "ends", "addEdge", "START", "addConditionalEdges", "humanReview", "tools", "compile", "checkpointer"], "mappings": ";;;;;;;;;;;QAiFaA;eAAAA;;QAsBGC;eAAAA;;QAvCAC;eAAAA;;;0BA3DT;2BAkBA;uBAO2B;0BAGT;wBACF;yBACQ;2BAMxB;2BACuB;AAE9B,MAAMC,SAAS,IAAIC,cAAM,CAAC;AAE1B,MAAMC,8CAA8C,IAAIC,IAAI;IAC1D;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAIM,SAASJ;IACd,OAAOK,qBAAU,CAACC,IAAI,CAAC;QACrBC,UAAUF,IAAAA,qBAAU,EAA0B;YAC5CG,SAASC,+BAAoB;YAC7BC,SAAS,IAAM,EAAE;QACnB;QACAC,aAAaN,IAAAA,qBAAU,EAAS;YAC9BG,SAAS,CAACI,GAAGC;gBACXZ,OAAOa,GAAG,CAAC,CAAC,kBAAkB,EAAEF,EAAE,IAAI,EAAEC,GAAG;gBAC3C,OAAOA;YACT;YACAH,SAAS,IAAM;QACjB;QACAK,UAAUV,qBAAU;IACtB;AACF;AAEO,MAAMP,aAAaE;AAsBnB,SAASD,iBAGdiB,MAA8B;IAE9B,MAAM,EAAEC,eAAe,EAAEC,KAAK,EAAE,GAAGF;IAEnC,MAAMG,iBAAiB,CACrBC,OACAC;QAEA,MAAM,EAAEd,QAAQ,EAAE,GAAGa;QACrB,MAAME,cAAcf,QAAQ,CAACA,SAASgB,MAAM,GAAG,EAAE;QAEjD,IAAIC,IAAAA,qBAAW,EAACF,cAAc;YAC5B,MAAMG,YAAYH;YAClB,IAAI,CAACG,UAAUC,UAAU,IAAID,UAAUC,UAAU,CAACH,MAAM,KAAK,GAAG;gBAC9D,OAAOI,cAAG;YACZ;YAEA,MAAMC,wBACJP,QAAQQ,cAAcD,yBAAyB;YACjD,OAAOA,wBAAwB,UAAU;QAC3C;QAEA,OAAOD,cAAG;IACZ;IAEA,MAAMG,kBAAkB,CAACV,OAAmBC;QAC1C,MAAM,EAAEd,QAAQ,EAAE,GAAGa;QACrB,MAAME,cAAcf,QAAQ,CAACA,SAASgB,MAAM,GAAG,EAAE;QAEjD,IAAI,CAACD,eAAe,CAACE,IAAAA,qBAAW,EAACF,cAAc;YAC7C,MAAM,IAAIS,MAAM;QAClB;QAEA,MAAMN,YAAYH;QAElB,oDAAoD;QACpD,MAAMU,mBACJP,UAAUC,UAAU,EAAEO,OACpB,CAACC,WACC,CAACA,SAASC,IAAI,IACdD,SAASC,IAAI,CAACC,IAAI,OAAO,MACzB,CAACF,SAASG,EAAE,IACZH,SAASG,EAAE,CAACD,IAAI,OAAO,OACtB,EAAE;QAET,IAAIJ,iBAAiBT,MAAM,GAAG,GAAG;YAC/B,MAAM,IAAIQ,MACR,CAAC,MAAM,EAAEC,iBAAiBT,MAAM,CAAC,qBAAqB,EAAEe,KAAKC,SAAS,CAACP,kBAAkB,wCAAwC,CAAC;QAEtI;QAEA,sEAAsE;QACtE,wDAAwD;QAExD,0DAA0D;QAC1D,MAAMQ,YACJf,UAAUC,UAAU,EAChBe,IAAI,CAACP,UAAUQ;YACf,OAAO,CAAC,KAAK,EAAEA,QAAQ,EAAE,EAAE,EAAER,SAASC,IAAI,CAAC,aAAa,EAAEG,KAAKC,SAAS,CAACL,SAASS,IAAI,EAAE,MAAM,IAAI;QACpG,GACCC,KAAK,WAAW;QACrB,MAAMT,OACJf,MAAMT,WAAW,KAAKU,OAAOQ,YAAY,EAAEgB,oBACvC,eACA;QACN,MAAM,EAAEC,MAAM,EAAE,GAAGC,IAAAA,oBAAS,EAC1BT,KAAKC,SAAS,CAAC;YACbS,MAAMb;YACNc,QAAQ,CAAC,4CAA4C,EAAET,UAAU,oDAAoD,CAAC;YACtHU,UACE7B,QAAQQ,cAAcsB,gBAAgB,CAAC/B,MAAMT,WAAW,CAAC,EAAEwB,QAAQ;YACrEiB,YAAYhC,MAAMT,WAAW;QAC/B;QAGF,uBAAuB;QACvB,IAAImC,WAAW,YAAYA,WAAW,OAAO;YAC3C,qDAAqD;YACrD,oHAAoH;YACpH,kDAAkD;YAClD,OAAO,IAAIO,kBAAO,CAAC;gBACjBC,MAAM;YACR;QACF,OAAO;YACL,+DAA+D;YAC/D,MAAMC,oBACJ9B,UAAUC,UAAU,EAAEe,IAAI,CAACP;gBACzB,OAAO,IAAIsB,qBAAW,CAAC;oBACrBC,SAAS;oBACTC,cAAcxB,SAASG,EAAE,IAAI;oBAC7BF,MAAMD,SAASC,IAAI;gBACrB;YACF,MAAM,EAAE;YAEV,mDAAmD;YACnD,OAAO,IAAIkB,kBAAO,CAAC;gBACjBM,QAAQ;oBACNpD,UAAUgD;gBACZ;gBACAD,MAAM;YACR;QACF;IACF;IAEA,MAAMM,YAAY,OAChBxC,OACAC;QAEA,sCAAsC;QACtC,MAAMwC,gBAAgBzC,MAAMT,WAAW;QACvC,+CAA+C;QAC/C,MAAMmD,cAAczC,QAAQQ,cAAcsB,gBAAgB,CAACU,cAAc;QACzE,IAAI,CAACC,aAAa;YAChB,MAAM,IAAI/B,MAAM,CAAC,kCAAkC,EAAE8B,eAAe;QACtE;QACA,qDAAqD;QACrD,MAAME,cAAcD,YAAYE,KAAK;QACrC,MAAMC,aAAa,MAAMC,IAAAA,wBAAa,EACpC,GAAGH,YAAYI,QAAQ,CAACC,WAAW,GAAG,CAAC,EAAEL,YAAY5B,IAAI,EAAE,EAC3D;YACEkC,oBAAoBN,YAAYO,kBAAkB;YAClD,GAAGP,YAAYQ,UAAU;QAC3B;QAEF,yBAAyB;QACzB,MAAMC,eACJ,AAAC,MAAMnD,QAAQQ,cAAc4C,iBAAiBC,YACzCZ,YAAYa,SAAS,CAAClC,GAAG,CAAC,CAACmC,MAAQA,IAAIC,UAAU,MAChD,EAAE;QACV,MAAMC,gBAAgB;eAAIN;eAAkBxD,OAAO+D,YAAY,IAAI,EAAE;SAAE;QACvE,MAAMC,cAAcC,IAAAA,uBAAc,EAAC5D;QACnC,IAAI2D,aAAa;YACfF,cAAcI,IAAI,CAACF;QACrB;QACA,0BAA0B;QAC1B,wEAAwE;QACxE,MAAMG,YACJpB,YAAYI,QAAQ,KAAKiB,wBAAiB,CAACC,MAAM,IACjDlF,4CAA4CmF,GAAG,CAACvB,YAAY5B,IAAI;QAElE,MAAMoD,iBAAiBJ,YACnBlB,WAAWuB,SAAS,CAACV,iBACrBb,WAAWuB,SAAS,CAACV,eAAe;YAClCW,qBAAqB;QACvB;QAEJ,2DAA2D;QAC3D,MAAMC,gBAAgB,IAAIC,uBAAa,CAAC7B,YAAY8B,WAAW;QAE/D,gEAAgE;QAChE,MAAMC,gBAAgBzE,MAAMb,QAAQ;QAEpC,MAAMuF,QAAQ;YAACJ;eAAkBG;SAAc;QAE/C,MAAME,MACJjC,YAAYzB,EAAE,KAAKhB,QAAQQ,cAAcgB,oBACrCmD,yBAAc,GACdC,qBAAU;QAChB,mBAAmB;QACnB,MAAMC,WAAW,MAAMX,eAAeY,MAAM,CAACL,OAAO;YAClD,GAAGzE,MAAM;YACT+E,MAAM;gBAACL;aAAI;QACb;QAEA,kCAAkC;QAClCG,SAASG,iBAAiB,CAACC,OAAO,GAAGzC;QACrC,IAAIqC,SAASK,SAAS,EAAE;YACtBL,SAASK,SAAS,CAACpE,IAAI,GAAG,GAAG0B,cAAc,CAAC,EAAEC,YAAY3B,IAAI,EAAE;QAClE;QAEA,OAAO;YACL5B,UAAU;gBAAC2F;aAAS;QACtB;IACF;IAEA,MAAMM,kBAAkB,OACtBpF,OACAC;QAEA,MAAM,EAAEd,UAAUkG,YAAY,EAAE9F,WAAW,EAAE,GAAGS;QAChD,MAAME,cAAcmF,aAAaC,EAAE,CAAC,CAAC;QACrC,IAAI,CAACpF,aAAa;YAChB,MAAM,IAAIS,MAAM;QAClB;QACA,IAAIP,IAAAA,qBAAW,EAACF,cAAc;YAC5B,MAAMG,YAAYH;YAClB,IAAIG,UAAUC,UAAU,IAAID,UAAUC,UAAU,CAACH,MAAM,GAAG,GAAG;gBAC3D,MAAMS,mBAAmBP,UAAUC,UAAU,CAACO,MAAM,CAClD,CAACC,WACC,CAACA,SAASC,IAAI,IACdD,SAASC,IAAI,CAACC,IAAI,OAAO,MACzB,CAACF,SAASG,EAAE,IACZH,SAASG,EAAE,CAACD,IAAI,OAAO;gBAE3B,IAAIJ,iBAAiBT,MAAM,GAAG,GAAG;oBAC/B,MAAM,IAAIQ,MACR,CAAC,MAAM,EAAEC,iBAAiBT,MAAM,CAAC,qBAAqB,EAAEe,KAAKC,SAAS,CAACP,kBAAkB,wCAAwC,CAAC;gBAEtI;YACF;QACF;QAEA,MAAM8B,cAAczC,QAAQQ,cAAcsB,gBAAgB,CAACxC,YAAY;QACvE,IAAI,CAACmD,aAAa;YAChB,MAAM,IAAI/B,MAAM,CAAC,kCAAkC,EAAEpB,aAAa;QACpE;QACA,MAAMgG,kBAAkB7C;QAExB,yBAAyB;QACzB,MAAMU,eACJ,AAAC,MAAMnD,QAAQQ,cAAc4C,iBAAiBC,YACzCZ,YAAYa,SAAS,CAAClC,GAAG,CAAC,CAACmC,MAAQA,IAAIC,UAAU,MAChD,EAAE;QACV,MAAMC,gBAAgB;eAAIN;eAAkBxD,OAAO+D,YAAY,IAAI,EAAE;SAAE;QACvE,MAAMC,cAAcC,IAAAA,uBAAc,EAAC5D;QACnC,IAAI2D,aAAa;YACfF,cAAcI,IAAI,CAACF;QACrB;QAEA,MAAMe,MACJY,gBAAgBtE,EAAE,KAAKhB,QAAQQ,cAAcgB,oBACzC+D,mCAAwB,GACxBC,+BAAoB;QAC1B,6BAA6B;QAC7B,MAAMC,kBAAkB,IAAIC,kBAAQ,CAACjC,eAAe;YAClDkC,kBAAkB;QACpB;QACA,MAAMC,MAAM,MAAMH,gBAAgBX,MAAM,CAAC/E,OAAO;YAC9C,GAAGC,MAAM;YACT+E,MAAM;gBAACL;aAAI;QACb;QAEA,6EAA6E;QAC7E,MAAMmB,SAAgB,EAAE;QACxB,IAAIC,MAAMC,OAAO,CAACH,MAAM;YACtBhH,OAAOa,GAAG,CAAC;YACXoG,OAAOhC,IAAI,IAAI+B;QACjB,OAAO,IAAIA,eAAe5D,kBAAO,EAAE;YACjCpD,OAAOa,GAAG,CAAC;YACXoG,OAAOhC,IAAI,CAAC+B;QACd,OAAO,IAAIE,MAAMC,OAAO,CAACH,IAAI1G,QAAQ,GAAG;YACtCN,OAAOa,GAAG,CAAC;YACXoG,OAAOhC,IAAI,IAAI+B,IAAI1G,QAAQ;QAC7B,OAAO;YACLN,OAAOoH,KAAK,CAAC;YACb,MAAM,IAAItF,MAAM;QAClB;QACA,MAAMuF,aAAaJ,OAAOK,IAAI,CAAC,CAACC,OAASA,gBAAgBnE,kBAAO;QAChE,IAAIiE,YAAY;YACd,OAAOJ;QACT;QACA,qCAAqC;QACrC,MAAM3G,WAA0B,EAAE;QAClC,KAAK,MAAMiH,QAAQN,OAAQ;YACzB,6DAA6D;YAC7D,IAAIM,gBAAgBC,qBAAW,EAAE;gBAC/BD,KAAKnB,iBAAiB,GAAG;oBACvB,GAAGmB,KAAKnB,iBAAiB;oBACzBC,SAASlF,MAAMT,WAAW;gBAC5B;gBACAJ,SAAS2E,IAAI,CAACsC;YAChB;QACF;QACA,OAAO;YAAEjH;QAAS;IACpB;IAEA,MAAMmH,WAAW,IAAIC,qBAAU,CAAC7H,YAC7B8H,OAAO,CAAC,SAAShE,WACjBgE,OAAO,CAAC,eAAe9F,iBAAiB;QAAE+F,MAAM;YAAC;YAAS;SAAQ;IAAC,GACnED,OAAO,CAAC,SAASpB,iBACjBsB,OAAO,CAACC,gBAAK,EAAE,SACfC,mBAAmB,CAAC,SAAS7G,gBAAgB;QAC5C8G,aAAa;QACbC,OAAO;QACP,CAACvG,cAAG,CAAC,EAAEA,cAAG;IACZ,GACCmG,OAAO,CAAC,SAAS;IACpB,OAAOJ,SAASS,OAAO,CAAC;QACtBC,cAAcnH;QACdC;IACF;AACF"}