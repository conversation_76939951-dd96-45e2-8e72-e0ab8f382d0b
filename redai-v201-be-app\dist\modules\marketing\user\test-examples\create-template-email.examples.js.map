{"version": 3, "file": "create-template-email.examples.js", "sourceRoot": "", "sources": ["../../../../../src/modules/marketing/user/test-examples/create-template-email.examples.ts"], "names": [], "mappings": ";;;AAIa,QAAA,2BAA2B,GAAG;IAEzC,eAAe,EAAE;QACf,IAAI,EAAE,oBAAoB;QAC1B,OAAO,EAAE,sCAAsC;QAC/C,WAAW,EAAE;;;;;;;;;;;;QAYT;QACJ,WAAW,EAAE,0JAA0J;QACvK,IAAI,EAAE,YAAY;QAClB,WAAW,EAAE,sDAAsD;QACnE,IAAI,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,SAAS,CAAC;QAC5C,SAAS,EAAE;YACT;gBACE,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,MAAM;gBACZ,YAAY,EAAE,YAAY;gBAC1B,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,6CAA6C;aAC3D;YACD;gBACE,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,MAAM;gBACZ,YAAY,EAAE,UAAU;gBACxB,QAAQ,EAAE,KAAK;gBACf,WAAW,EAAE,yBAAyB;aACvC;YACD;gBACE,IAAI,EAAE,iBAAiB;gBACvB,IAAI,EAAE,QAAQ;gBACd,YAAY,EAAE,IAAI;gBAClB,QAAQ,EAAE,KAAK;gBACf,WAAW,EAAE,oBAAoB;aAClC;YACD;gBACE,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,MAAM;gBACZ,YAAY,EAAE,YAAY;gBAC1B,QAAQ,EAAE,KAAK;gBACf,WAAW,EAAE,yBAAyB;aACvC;YACD;gBACE,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,KAAK;gBACX,YAAY,EAAE,0BAA0B;gBACxC,QAAQ,EAAE,KAAK;gBACf,WAAW,EAAE,mBAAmB;aACjC;SACF;KACF;IAGD,YAAY,EAAE;QACZ,IAAI,EAAE,iBAAiB;QACvB,OAAO,EAAE,uCAAuC;QAChD,WAAW,EAAE;;;;OAIV;QACH,IAAI,EAAE,SAAS;QACf,WAAW,EAAE,8CAA8C;QAC3D,IAAI,EAAE,CAAC,SAAS,EAAE,cAAc,CAAC;QACjC,SAAS,EAAE;YACT;gBACE,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,MAAM;gBACZ,YAAY,EAAE,YAAY;gBAC1B,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,gBAAgB;aAC9B;YACD;gBACE,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,MAAM;gBACZ,YAAY,EAAE,OAAO;gBACrB,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,aAAa;aAC3B;YACD;gBACE,IAAI,EAAE,mBAAmB;gBACzB,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,aAAa;aAC3B;SACF;KACF;IAGD,gBAAgB,EAAE;QAChB,IAAI,EAAE,gBAAgB;QACtB,OAAO,EAAE,iCAAiC;QAC1C,WAAW,EAAE;;;;;;;;;;;;;QAaT;QACJ,WAAW,EAAE,gHAAgH;QAC7H,IAAI,EAAE,aAAa;QACnB,WAAW,EAAE,+CAA+C;QAC5D,IAAI,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,QAAQ,CAAC;QAC3C,SAAS,EAAE;YACT;gBACE,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,OAAO;gBACb,YAAY,EAAE,gCAAgC;gBAC9C,QAAQ,EAAE,KAAK;gBACf,WAAW,EAAE,gCAAgC;aAC9C;YACD;gBACE,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,QAAQ;gBACd,YAAY,EAAE,IAAI;gBAClB,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,+BAA+B;aAC7C;YACD;gBACE,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,QAAQ;gBACd,YAAY,EAAE,IAAI;gBAClB,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,2BAA2B;aACzC;YACD;gBACE,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,KAAK;gBACX,YAAY,EAAE,qCAAqC;gBACnD,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,2BAA2B;aACzC;SACF;KACF;IAGD,eAAe,EAAE;QACf,IAAI,EAAE,mBAAmB;QACzB,OAAO,EAAE,uBAAuB;QAChC,WAAW,EAAE,6CAA6C;KAC3D;IAGD,eAAe,EAAE;QACf,IAAI,EAAE,2BAA2B;QACjC,OAAO,EAAE,mDAAmD;QAC5D,WAAW,EAAE;;;;;;;;OAQV;QACH,IAAI,EAAE,eAAe;QACrB,IAAI,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,cAAc,CAAC;QAC9C,SAAS,EAAE;YACT;gBACE,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,aAAa;aAC3B;YACD;gBACE,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,gBAAgB;aAC9B;YACD;gBACE,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,kBAAkB;aAChC;YACD;gBACE,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,eAAe;aAC7B;YACD;gBACE,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,uBAAuB;aACrC;YACD;gBACE,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,qBAAqB;aACnC;YACD;gBACE,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,KAAK;gBACX,QAAQ,EAAE,KAAK;gBACf,WAAW,EAAE,wBAAwB;aACtC;SACF;KACF;CACF,CAAC;AAGW,QAAA,eAAe,GAAG;IAE7B,SAAS,EAAE;QACT,IAAI,EAAE,EAAE;QACR,OAAO,EAAE,cAAc;QACvB,WAAW,EAAE,qBAAqB;KACnC;IAGD,YAAY,EAAE;QACZ,IAAI,EAAE,eAAe;QACrB,OAAO,EAAE,EAAE;QACX,WAAW,EAAE,qBAAqB;KACnC;IAGD,YAAY,EAAE;QACZ,IAAI,EAAE,eAAe;QACrB,OAAO,EAAE,cAAc;QACvB,WAAW,EAAE,EAAE;KAChB;IAGD,WAAW,EAAE;QACX,IAAI,EAAE,eAAe;QACrB,OAAO,EAAE,cAAc;QACvB,WAAW,EAAE,qBAAqB;QAClC,IAAI,EAAE,cAAc;KACrB;IAGD,mBAAmB,EAAE;QACnB,IAAI,EAAE,eAAe;QACrB,OAAO,EAAE,cAAc;QACvB,WAAW,EAAE,4BAA4B;QACzC,SAAS,EAAE;YACT;gBACE,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,MAAM;aACb;SACF;KACF;IAGD,mBAAmB,EAAE;QACnB,IAAI,EAAE,eAAe;QACrB,OAAO,EAAE,cAAc;QACvB,WAAW,EAAE,2BAA2B;QACxC,SAAS,EAAE;YACT;gBACE,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,cAAc;aACrB;SACF;KACF;IAGD,sBAAsB,EAAE;QACtB,IAAI,EAAE,eAAe;QACrB,OAAO,EAAE,cAAc;QACvB,WAAW,EAAE,2BAA2B;QACxC,SAAS,EAAE;YACT;gBACE,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,MAAM;aACb;YACD;gBACE,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,MAAM;aACb;SACF;KACF;CACF,CAAC"}