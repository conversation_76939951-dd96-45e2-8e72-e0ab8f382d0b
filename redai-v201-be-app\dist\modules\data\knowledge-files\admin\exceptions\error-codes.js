"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.KNOWLEDGE_FILE_ERROR_CODES = void 0;
const common_1 = require("@nestjs/common");
const app_exception_1 = require("../../../../../common/exceptions/app.exception");
exports.KNOWLEDGE_FILE_ERROR_CODES = {
    KNOWLEDGE_FILE_CREATE_ERROR: new app_exception_1.ErrorCode(20101, 'Lỗi khi tạo file tri thức', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    KNOWLEDGE_FILE_LIST_ERROR: new app_exception_1.ErrorCode(20102, 'Lỗi khi lấy danh sách file tri thức', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    KNOWLEDGE_FILE_DELETE_ERROR: new app_exception_1.ErrorCode(20103, 'Lỗi khi xóa file tri thức', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    KNOWLEDGE_FILE_NOT_FOUND: new app_exception_1.ErrorCode(20104, 'Không tìm thấy file tri thức', common_1.HttpStatus.NOT_FOUND),
    KNOWLEDGE_FILE_UPLOAD_ERROR: new app_exception_1.ErrorCode(20105, 'Lỗi khi tải lên file tri thức', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    KNOWLEDGE_FILE_DOWNLOAD_ERROR: new app_exception_1.ErrorCode(20106, 'Lỗi khi tải xuống file tri thức', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    KNOWLEDGE_FILE_PERMISSION_ERROR: new app_exception_1.ErrorCode(20107, 'Không có quyền truy cập file tri thức', common_1.HttpStatus.FORBIDDEN),
    KNOWLEDGE_FILE_UPDATE_ERROR: new app_exception_1.ErrorCode(20108, 'Lỗi khi cập nhật trạng thái file', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
};
//# sourceMappingURL=error-codes.js.map