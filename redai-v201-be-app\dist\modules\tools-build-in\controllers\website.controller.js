"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WebsiteController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebsiteController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const services_1 = require("../services");
let WebsiteController = WebsiteController_1 = class WebsiteController {
    websiteService;
    logger = new common_1.Logger(WebsiteController_1.name);
    constructor(websiteService) {
        this.websiteService = websiteService;
    }
};
exports.WebsiteController = WebsiteController;
exports.WebsiteController = WebsiteController = WebsiteController_1 = __decorate([
    (0, swagger_1.ApiTags)('Website'),
    (0, common_1.Controller)('tools-build-in/website'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [services_1.WebsiteService])
], WebsiteController);
//# sourceMappingURL=website.controller.js.map