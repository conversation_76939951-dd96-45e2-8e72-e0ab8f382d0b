"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var GenericPageTemplateTagRepository_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericPageTemplateTagRepository = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const generic_page_template_tag_entity_1 = require("../entities/generic-page-template-tag.entity");
const common_2 = require("../../../common");
const generic_page_error_code_1 = require("../exceptions/generic-page-error.code");
let GenericPageTemplateTagRepository = GenericPageTemplateTagRepository_1 = class GenericPageTemplateTagRepository extends typeorm_1.Repository {
    dataSource;
    logger = new common_1.Logger(GenericPageTemplateTagRepository_1.name);
    constructor(dataSource) {
        super(generic_page_template_tag_entity_1.GenericPageTemplateTag, dataSource.createEntityManager());
        this.dataSource = dataSource;
    }
    async findTagsByTemplateId(templateId) {
        try {
            const tags = await this.find({
                where: { templateId },
                select: ['tag'],
            });
            return tags.map((tag) => tag.tag);
        }
        catch (error) {
            this.logger.error(`Error finding tags by template ID: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_NOT_FOUND, `Lỗi khi tìm tag cho mẫu trang với ID ${templateId}`);
        }
    }
    async deleteTagsByTemplateId(templateId) {
        try {
            await this.delete({ templateId });
        }
        catch (error) {
            this.logger.error(`Error deleting tags by template ID: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_DELETE_ERROR, `Lỗi khi xóa tag cho mẫu trang với ID ${templateId}`);
        }
    }
    async saveTags(templateId, tags) {
        try {
            await this.deleteTagsByTemplateId(templateId);
            if (!tags || tags.length === 0) {
                return;
            }
            const tagEntities = tags.map((tag) => {
                const tagEntity = new generic_page_template_tag_entity_1.GenericPageTemplateTag();
                tagEntity.templateId = templateId;
                tagEntity.tag = tag;
                return tagEntity;
            });
            await this.save(tagEntities);
        }
        catch (error) {
            this.logger.error(`Error saving tags for template: ${error.message}`, error.stack);
            throw new common_2.AppException(generic_page_error_code_1.GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_UPDATE_ERROR, `Lỗi khi lưu tag cho mẫu trang với ID ${templateId}`);
        }
    }
};
exports.GenericPageTemplateTagRepository = GenericPageTemplateTagRepository;
exports.GenericPageTemplateTagRepository = GenericPageTemplateTagRepository = GenericPageTemplateTagRepository_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], GenericPageTemplateTagRepository);
//# sourceMappingURL=generic-page-template-tag.repository.js.map