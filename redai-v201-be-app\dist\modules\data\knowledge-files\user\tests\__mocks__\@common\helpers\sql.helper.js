"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SqlHelper = void 0;
class SqlHelper {
    dataSource;
    options;
    constructor(dataSource, options = {}) {
        this.dataSource = dataSource;
        this.options = options;
    }
    async executeQuery(sql, params = []) {
        return [];
    }
    async executeQueryOne(sql, params = []) {
        return null;
    }
}
exports.SqlHelper = SqlHelper;
//# sourceMappingURL=sql.helper.js.map