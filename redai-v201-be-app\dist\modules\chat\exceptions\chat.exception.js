"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CHAT_ERROR_CODES = void 0;
const common_1 = require("@nestjs/common");
const exceptions_1 = require("../../../common/exceptions");
exports.CHAT_ERROR_CODES = {
    AGENT_NOT_FOUND: new exceptions_1.ErrorCode(10401, 'Không tìm thấy agent', common_1.HttpStatus.NOT_FOUND),
    RUN_CREATION_FAILED: new exceptions_1.ErrorCode(10402, 'Tạo run chat thất bại', common_1.HttpStatus.INTERNAL_SERVER_ERROR),
    INVALID_AGENT_TYPE: new exceptions_1.ErrorCode(10403, 'Loại agent không hợp lệ', common_1.HttpStatus.BAD_REQUEST),
    AGENT_ACCESS_DENIED: new exceptions_1.ErrorCode(10404, '<PERSON><PERSON><PERSON>ng c<PERSON> quyền truy cập agent', common_1.HttpStatus.FORBIDDEN),
};
//# sourceMappingURL=chat.exception.js.map