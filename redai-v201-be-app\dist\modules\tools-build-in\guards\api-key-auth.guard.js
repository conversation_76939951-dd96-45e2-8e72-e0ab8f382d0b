"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ApiKeyAuthGuard_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiKeyAuthGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const exceptions_1 = require("../../../common/exceptions");
const exceptions_2 = require("../exceptions");
const decorators_1 = require("../decorators");
const constants_1 = require("../constants");
const utils_1 = require("../utils");
const typeorm_1 = require("typeorm");
let ApiKeyAuthGuard = ApiKeyAuthGuard_1 = class ApiKeyAuthGuard {
    reflector;
    apiKeyUtil;
    dataSource;
    logger = new common_1.Logger(ApiKeyAuthGuard_1.name);
    constructor(reflector, apiKeyUtil, dataSource) {
        this.reflector = reflector;
        this.apiKeyUtil = apiKeyUtil;
        this.dataSource = dataSource;
    }
    async canActivate(context) {
        const isApiKeyRequired = this.reflector.getAllAndOverride(decorators_1.API_KEY_AUTH_KEY, [context.getHandler(), context.getClass()]);
        if (!isApiKeyRequired) {
            return true;
        }
        const request = context.switchToHttp().getRequest();
        const apiKey = this.extractApiKey(request);
        this.logger.warn(`API Key: ${apiKey}`);
        if (!apiKey) {
            throw new exceptions_1.AppException(exceptions_2.TOOLS_BUILD_IN_API_KEY_ERROR_CODES.API_KEY_MISSING, 'API Key không được cung cấp trong header');
        }
        const decodedKey = this.apiKeyUtil.decodeApiKey(apiKey);
        if (!decodedKey) {
            this.logger.warn(`API Key không hợp lệ hoặc không thể giải mã: ${this.maskApiKey(apiKey)}`);
            throw new exceptions_1.AppException(exceptions_2.TOOLS_BUILD_IN_API_KEY_ERROR_CODES.API_KEY_INVALID, 'API Key không hợp lệ hoặc không thể giải mã');
        }
        const agent = await this.checkAgentExists(decodedKey.agentId);
        if (!agent) {
            this.logger.warn(`Agent không tồn tại: ${decodedKey.agentId}`);
            throw new exceptions_1.AppException(exceptions_2.TOOLS_BUILD_IN_API_KEY_ERROR_CODES.AGENT_NOT_FOUND, 'Agent không tồn tại');
        }
        if (!agent.isActive) {
            this.logger.warn(`Agent không hoạt động: ${decodedKey.agentId}`);
            throw new exceptions_1.AppException(exceptions_2.TOOLS_BUILD_IN_API_KEY_ERROR_CODES.AGENT_INACTIVE, 'Agent không hoạt động');
        }
        request['agent'] = {
            id: decodedKey.agentId,
            userId: decodedKey.userId,
        };
        return true;
    }
    extractApiKey(request) {
        return request.headers[constants_1.ApiKeyHeaderEnum.API_KEY.toLowerCase()];
    }
    async checkAgentExists(agentId) {
        try {
            const agent = await this.dataSource
                .createQueryBuilder()
                .select(['id', 'status'])
                .from('agents', 'agent')
                .where('agent.id = :agentId', { agentId })
                .andWhere('agent.deleted_at IS NULL')
                .getRawOne();
            if (!agent) {
                return null;
            }
            const isActive = agent.status !== 'INACTIVE' && agent.status !== 'DELETED';
            return {
                id: agent.id,
                isActive,
            };
        }
        catch (error) {
            this.logger.error(`Lỗi khi kiểm tra Agent: ${error.message}`, error.stack);
            return null;
        }
    }
    maskApiKey(apiKey) {
        if (!apiKey || apiKey.length < 8) {
            return '********';
        }
        return '********' + apiKey.slice(-4);
    }
};
exports.ApiKeyAuthGuard = ApiKeyAuthGuard;
exports.ApiKeyAuthGuard = ApiKeyAuthGuard = ApiKeyAuthGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector,
        utils_1.ApiKeyUtil,
        typeorm_1.DataSource])
], ApiKeyAuthGuard);
//# sourceMappingURL=api-key-auth.guard.js.map