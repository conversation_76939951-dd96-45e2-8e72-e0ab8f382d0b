"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginatedBlogCommentDto = exports.CreateBlogCommentDto = exports.BlogCommentDto = exports.BlogCommentAuthorDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const enums_1 = require("../../enums");
const class_validator_1 = require("class-validator");
class BlogCommentAuthorDto {
    id;
    name;
    avatar;
    type;
}
exports.BlogCommentAuthorDto = BlogCommentAuthorDto;
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID của tác giả',
        example: 10,
    }),
    __metadata("design:type", Number)
], BlogCommentAuthorDto.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Tên tác giả',
        example: 'Nguyễn Văn A',
    }),
    __metadata("design:type", String)
], BlogCommentAuthorDto.prototype, "name", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Avatar của tác giả',
        example: 'https://cdn.example.com/avatars/user10.jpg',
    }),
    __metadata("design:type", String)
], BlogCommentAuthorDto.prototype, "avatar", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Loại tác giả',
        example: enums_1.AuthorTypeEnum.USER,
        enum: enums_1.AuthorTypeEnum,
    }),
    __metadata("design:type", String)
], BlogCommentAuthorDto.prototype, "type", void 0);
class BlogCommentDto {
    id;
    blog_id;
    user_id;
    created_at;
    content;
    author_type;
    employee_id;
    parent_comment_id;
    replies;
    author;
}
exports.BlogCommentDto = BlogCommentDto;
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID của bình luận',
        example: 1,
    }),
    __metadata("design:type", Number)
], BlogCommentDto.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID của bài viết',
        example: 1,
    }),
    __metadata("design:type", Number)
], BlogCommentDto.prototype, "blog_id", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID của người dùng',
        example: 10,
    }),
    __metadata("design:type", Number)
], BlogCommentDto.prototype, "user_id", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian tạo (Unix timestamp)',
        example: 1632474086123,
    }),
    __metadata("design:type", Number)
], BlogCommentDto.prototype, "created_at", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Nội dung bình luận',
        example: 'Nội dung bình luận',
    }),
    __metadata("design:type", String)
], BlogCommentDto.prototype, "content", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Loại tác giả',
        example: enums_1.AuthorTypeEnum.USER,
        enum: enums_1.AuthorTypeEnum,
    }),
    __metadata("design:type", String)
], BlogCommentDto.prototype, "author_type", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID của nhân viên nếu là bình luận của hệ thống',
        example: null,
        nullable: true,
    }),
    __metadata("design:type", Object)
], BlogCommentDto.prototype, "employee_id", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID của bình luận cha nếu đây là bình luận phản hồi',
        example: null,
        nullable: true,
    }),
    __metadata("design:type", Object)
], BlogCommentDto.prototype, "parent_comment_id", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => BlogCommentDto),
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách các bình luận phản hồi',
        type: [BlogCommentDto],
        nullable: true,
    }),
    __metadata("design:type", Array)
], BlogCommentDto.prototype, "replies", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => BlogCommentAuthorDto),
    (0, swagger_1.ApiProperty)({
        description: 'Thông tin tác giả',
        type: BlogCommentAuthorDto,
    }),
    __metadata("design:type", BlogCommentAuthorDto)
], BlogCommentDto.prototype, "author", void 0);
class CreateBlogCommentDto {
    content;
    parent_comment_id;
}
exports.CreateBlogCommentDto = CreateBlogCommentDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nội dung bình luận',
        example: 'Nội dung bình luận',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateBlogCommentDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID của bình luận cha nếu đây là bình luận phản hồi',
        example: null,
        nullable: true,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], CreateBlogCommentDto.prototype, "parent_comment_id", void 0);
class PaginatedBlogCommentDto {
    content;
    totalItems;
    itemCount;
    itemsPerPage;
    totalPages;
    currentPage;
}
exports.PaginatedBlogCommentDto = PaginatedBlogCommentDto;
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Danh sách bình luận',
        type: [BlogCommentDto],
    }),
    __metadata("design:type", Array)
], PaginatedBlogCommentDto.prototype, "content", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Tổng số bình luận',
        example: 50,
    }),
    __metadata("design:type", Number)
], PaginatedBlogCommentDto.prototype, "totalItems", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Số lượng bình luận trên trang hiện tại',
        example: 10,
    }),
    __metadata("design:type", Number)
], PaginatedBlogCommentDto.prototype, "itemCount", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Số lượng bình luận trên mỗi trang',
        example: 10,
    }),
    __metadata("design:type", Number)
], PaginatedBlogCommentDto.prototype, "itemsPerPage", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Tổng số trang',
        example: 5,
    }),
    __metadata("design:type", Number)
], PaginatedBlogCommentDto.prototype, "totalPages", void 0);
__decorate([
    (0, class_transformer_1.Expose)(),
    (0, swagger_1.ApiProperty)({
        description: 'Trang hiện tại',
        example: 1,
    }),
    __metadata("design:type", Number)
], PaginatedBlogCommentDto.prototype, "currentPage", void 0);
//# sourceMappingURL=blog-comment.dto.js.map