"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MockVectorStoreRepository = exports.MockKnowledgeFileRepository = exports.MockDataSource = exports.MockRepository = void 0;
exports.mockQueryBuilder = mockQueryBuilder;
exports.mockEntityManager = mockEntityManager;
class MockRepository {
    create = jest.fn().mockImplementation(entity => entity);
    save = jest.fn().mockImplementation(entity => Promise.resolve(entity));
    findOne = jest.fn().mockResolvedValue(null);
    find = jest.fn().mockResolvedValue([]);
    delete = jest.fn().mockResolvedValue({ affected: 1 });
    remove = jest.fn().mockResolvedValue(undefined);
    count = jest.fn().mockResolvedValue(0);
    createQueryBuilder = jest.fn().mockReturnValue(mockQueryBuilder());
}
exports.MockRepository = MockRepository;
class MockDataSource {
    createQueryBuilder = jest.fn().mockReturnValue(mockQueryBuilder());
    getRepository = jest.fn().mockReturnValue(new MockRepository());
    transaction = jest.fn().mockImplementation(async (callback) => {
        return callback(mockEntityManager());
    });
}
exports.MockDataSource = MockDataSource;
class MockKnowledgeFileRepository extends MockRepository {
}
exports.MockKnowledgeFileRepository = MockKnowledgeFileRepository;
class MockVectorStoreRepository extends MockRepository {
}
exports.MockVectorStoreRepository = MockVectorStoreRepository;
function mockQueryBuilder() {
    const qb = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
        getRawOne: jest.fn().mockResolvedValue({}),
        getMany: jest.fn().mockResolvedValue([]),
        getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
        getOne: jest.fn().mockResolvedValue(null),
        from: jest.fn().mockReturnThis(),
    };
    return qb;
}
function mockEntityManager() {
    return {
        save: jest.fn().mockImplementation(entity => Promise.resolve(entity)),
    };
}
//# sourceMappingURL=typeorm.mock.js.map