"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MediaTypeUtil = exports.MediaType = void 0;
var MediaType;
(function (MediaType) {
    MediaType["IMAGE_JPEG"] = "image/jpeg";
    MediaType["IMAGE_PNG"] = "image/png";
    MediaType["IMAGE_GIF"] = "image/gif";
    MediaType["APPLICATION_PDF"] = "application/pdf";
    MediaType["TEXT_PLAIN"] = "text/plain";
    MediaType["APPLICATION_JSON"] = "application/json";
    MediaType["APPLICATION_OCTET_STREAM"] = "application/octet-stream";
})(MediaType || (exports.MediaType = MediaType = {}));
class MediaTypeUtil {
    static getValue(value) {
        const mediaType = Object.values(MediaType).find(type => type === value);
        return mediaType || MediaType.APPLICATION_OCTET_STREAM;
    }
    static isValid(value) {
        return Object.values(MediaType).includes(value);
    }
    static getExtension(mediaType) {
        switch (mediaType) {
            case MediaType.IMAGE_JPEG:
                return 'jpg';
            case MediaType.IMAGE_PNG:
                return 'png';
            case MediaType.IMAGE_GIF:
                return 'gif';
            case MediaType.APPLICATION_PDF:
                return 'pdf';
            case MediaType.TEXT_PLAIN:
                return 'txt';
            case MediaType.APPLICATION_JSON:
                return 'json';
            default:
                return 'bin';
        }
    }
}
exports.MediaTypeUtil = MediaTypeUtil;
//# sourceMappingURL=file.js.map