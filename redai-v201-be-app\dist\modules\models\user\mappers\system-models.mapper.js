"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemModelsMapper = void 0;
class SystemModelsMapper {
    static toResponseDto(entity) {
        return {
            id: entity.id,
            modelId: entity.modelId,
            provider: entity.provider,
            modelNamePattern: entity.modelNamePattern,
            inputModalities: Array.isArray(entity.inputModalities) ? entity.inputModalities : [],
            outputModalities: Array.isArray(entity.outputModalities) ? entity.outputModalities : [],
            samplingParameters: Array.isArray(entity.samplingParameters) ? entity.samplingParameters : [],
            features: Array.isArray(entity.features) ? entity.features : [],
            basePricing: entity.basePricing || { inputRate: 0, outputRate: 0 },
            fineTunePricing: entity.fineTunePricing || { inputRate: 0, outputRate: 0 },
            trainingPricing: entity.trainingPricing || 0,
        };
    }
    static toResponseDtoList(entities) {
        if (!entities || !Array.isArray(entities)) {
            return [];
        }
        return entities
            .map(entity => this.toResponseDto(entity))
            .filter(dto => dto !== null);
    }
}
exports.SystemModelsMapper = SystemModelsMapper;
//# sourceMappingURL=system-models.mapper.js.map