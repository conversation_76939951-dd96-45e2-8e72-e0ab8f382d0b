"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get QueryDto () {
        return QueryDto;
    },
    get SortDirection () {
        return SortDirection;
    }
});
const _swagger = require("@nestjs/swagger");
const _classvalidator = require("class-validator");
const _classtransformer = require("class-transformer");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var SortDirection = /*#__PURE__*/ function(SortDirection) {
    SortDirection["ASC"] = "ASC";
    SortDirection["DESC"] = "DESC";
    return SortDirection;
}({});
let QueryDto = class QueryDto {
    constructor(){
        /**
   * Trang hiện tại (bắt đầu từ 1)
   * @example 1
   */ this.page = 1;
        /**
   * Số lượng item trên mỗi trang
   * @example 10
   */ this.limit = 10;
        /**
   * Thứ tự sắp xếp
   * @example "DESC"
   */ this.sortDirection = "DESC";
    }
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Trang hiện tại (bắt đầu từ 1)',
        example: 1,
        default: 1,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsInt)({
        message: 'Trang phải là số nguyên'
    }),
    (0, _classvalidator.Min)(1, {
        message: 'Trang phải lớn hơn hoặc bằng 1'
    }),
    (0, _classtransformer.Type)(()=>Number),
    _ts_metadata("design:type", Number)
], QueryDto.prototype, "page", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng item trên mỗi trang',
        example: 10,
        default: 10,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsInt)({
        message: 'Số lượng item phải là số nguyên'
    }),
    (0, _classvalidator.Min)(1, {
        message: 'Số lượng item phải lớn hơn hoặc bằng 1'
    }),
    (0, _classtransformer.Type)(()=>Number),
    _ts_metadata("design:type", Number)
], QueryDto.prototype, "limit", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Trường sắp xếp',
        example: 'createdAt',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], QueryDto.prototype, "sortBy", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thứ tự sắp xếp',
        enum: SortDirection,
        example: "DESC",
        default: "DESC",
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsEnum)(SortDirection, {
        message: `Thứ tự sắp xếp phải là một trong các giá trị: ${Object.values(SortDirection).join(', ')}`
    }),
    _ts_metadata("design:type", String)
], QueryDto.prototype, "sortDirection", void 0);

//# sourceMappingURL=query.dto.js.map