"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomValidationPipe = void 0;
const common_1 = require("@nestjs/common");
const exceptions_1 = require("../exceptions");
const exceptions_2 = require("../../modules/tools/exceptions");
const VALIDATION_ERROR_CODES = {
    GENERAL_VALIDATION_ERROR: new exceptions_1.ErrorCode(9001, 'Dữ liệu đầu vào không hợp lệ', 400),
    FIELD_VALIDATION_ERROR: new exceptions_1.ErrorCode(9002, 'Một hoặc nhiều trường dữ liệu không hợp lệ', 400),
    NESTED_VALIDATION_ERROR: new exceptions_1.ErrorCode(9003, 'Dữ liệu nested object không hợp lệ', 400),
};
class CustomValidationPipe extends common_1.ValidationPipe {
    constructor() {
        super({
            transform: true,
            whitelist: true,
            forbidNonWhitelisted: true,
            exceptionFactory: (errors) => {
                const toolNamePatternError = this.findToolNamePatternError(errors);
                if (toolNamePatternError) {
                    throw new exceptions_1.AppException(exceptions_2.TOOLS_ERROR_CODES.TOOL_NAME_INVALID, 'Tên tool chỉ được chứa a-z, A-Z, 0-9, hoặc dấu gạch dưới', { property: 'toolName', errors: toolNamePatternError });
                }
                const detailedErrors = this.buildDetailedErrors(errors);
                const errorSummary = this.buildErrorSummary(detailedErrors);
                const hasNestedErrors = detailedErrors.some(error => error.field.includes('.'));
                const errorCode = hasNestedErrors
                    ? VALIDATION_ERROR_CODES.NESTED_VALIDATION_ERROR
                    : detailedErrors.length > 1
                        ? VALIDATION_ERROR_CODES.FIELD_VALIDATION_ERROR
                        : VALIDATION_ERROR_CODES.GENERAL_VALIDATION_ERROR;
                console.log('=== VALIDATION ERROR DETAILS ===');
                console.log('Error Code:', errorCode.code);
                console.log('Error Summary:', errorSummary);
                console.log('Detailed Errors:', JSON.stringify(detailedErrors, null, 2));
                console.log('Has Nested Errors:', hasNestedErrors);
                console.log('Total Errors:', detailedErrors.length);
                console.log('================================');
                return new exceptions_1.AppException(errorCode, errorSummary, {
                    summary: errorSummary,
                    details: detailedErrors,
                    totalErrors: detailedErrors.length,
                    hasNestedErrors,
                    errorType: hasNestedErrors ? 'nested' : detailedErrors.length > 1 ? 'multiple' : 'single'
                });
            },
        });
    }
    findToolNamePatternError(errors) {
        for (const error of errors) {
            if (error.property === 'toolName' && error.constraints && error.constraints.matches) {
                return [error.constraints.matches];
            }
            if (error.children && error.children.length > 0) {
                const childError = this.findToolNamePatternError(error.children);
                if (childError) {
                    return childError;
                }
            }
        }
        return null;
    }
    buildDetailedErrors(errors) {
        const detailedErrors = [];
        const processError = (error, path = '') => {
            const currentPath = path ? `${path}.${error.property}` : error.property;
            if (error.constraints) {
                const constraintMessages = Object.entries(error.constraints).map(([key, message]) => ({
                    constraint: key,
                    message: message
                }));
                detailedErrors.push({
                    field: currentPath,
                    value: error.value,
                    type: typeof error.value,
                    constraints: constraintMessages,
                    messages: Object.values(error.constraints)
                });
            }
            if (error.children && error.children.length > 0) {
                error.children.forEach(child => {
                    processError(child, currentPath);
                });
            }
        };
        errors.forEach(error => processError(error));
        return detailedErrors;
    }
    buildErrorSummary(detailedErrors) {
        if (detailedErrors.length === 0)
            return 'Lỗi validation không xác định';
        const groupedErrors = new Map();
        detailedErrors.forEach(error => {
            const mainField = error.field.split('.')[0];
            if (!groupedErrors.has(mainField)) {
                groupedErrors.set(mainField, []);
            }
            groupedErrors.get(mainField).push(error);
        });
        const fieldSummaries = Array.from(groupedErrors.entries()).map(([mainField, errors]) => {
            if (errors.length === 1) {
                const error = errors[0];
                const vietnameseMessage = this.translateErrorMessage(error.messages[0] || 'Giá trị không hợp lệ');
                return `${mainField} (${vietnameseMessage})`;
            }
            else {
                return `${mainField} (${errors.length} lỗi)`;
            }
        });
        const summary = fieldSummaries.join(', ');
        return detailedErrors.length > 3
            ? `${fieldSummaries.slice(0, 3).join(', ')} và ${detailedErrors.length - 3} lỗi khác`
            : summary;
    }
    translateErrorMessage(message) {
        const translations = {
            'should not be empty': 'không được để trống',
            'must be a string': 'phải là chuỗi ký tự',
            'must be a number': 'phải là số',
            'must be an integer': 'phải là số nguyên',
            'must be a boolean': 'phải là true/false',
            'must be an array': 'phải là mảng',
            'must be an object': 'phải là object',
            'must be a valid email': 'phải là email hợp lệ',
            'must be a valid URL': 'phải là URL hợp lệ',
            'must be a valid date': 'phải là ngày hợp lệ',
            'must be positive': 'phải là số dương',
            'must be negative': 'phải là số âm',
            'must not be less than': 'không được nhỏ hơn',
            'must not be greater than': 'không được lớn hơn',
            'must be longer than': 'phải dài hơn',
            'must be shorter than': 'phải ngắn hơn',
            'must match': 'phải khớp với',
            'must be one of the following values': 'phải là một trong các giá trị',
            'nested property': 'thuộc tính con',
            'each value in': 'mỗi giá trị trong',
        };
        let translatedMessage = message;
        Object.entries(translations).forEach(([english, vietnamese]) => {
            translatedMessage = translatedMessage.replace(new RegExp(english, 'gi'), vietnamese);
        });
        return translatedMessage;
    }
    formatErrors(errors) {
        return errors.map(error => {
            const errorInfo = {
                property: error.property,
                value: error.value,
                constraints: error.constraints || {},
            };
            if (error.constraints) {
                errorInfo.messages = Object.values(error.constraints);
            }
            if (error.children && error.children.length > 0) {
                errorInfo.children = this.formatErrors(error.children);
            }
            return errorInfo;
        });
    }
    formatDetailedErrors(errors) {
        const result = {};
        const processError = (error, parentPath = '') => {
            const currentPath = parentPath ? `${parentPath}.${error.property}` : error.property;
            if (!result[currentPath]) {
                result[currentPath] = {
                    property: error.property,
                    value: error.value,
                    constraints: [],
                    messages: [],
                    children: {}
                };
            }
            if (error.constraints) {
                result[currentPath].constraints = Object.keys(error.constraints);
                result[currentPath].messages = Object.values(error.constraints);
            }
            if (error.children && error.children.length > 0) {
                error.children.forEach(child => {
                    processError(child, currentPath);
                });
            }
        };
        errors.forEach(error => processError(error));
        return result;
    }
    extractErrorMessages(errors) {
        const messages = [];
        const extractFromError = (error, parentPath = '') => {
            const currentPath = parentPath ? `${parentPath}.${error.property}` : error.property;
            if (error.constraints) {
                Object.values(error.constraints).forEach(message => {
                    messages.push(`${currentPath}: ${message}`);
                });
            }
            if (error.children && error.children.length > 0) {
                error.children.forEach(child => extractFromError(child, currentPath));
            }
        };
        errors.forEach(error => extractFromError(error));
        return messages;
    }
}
exports.CustomValidationPipe = CustomValidationPipe;
//# sourceMappingURL=custom-validation.pipe.js.map