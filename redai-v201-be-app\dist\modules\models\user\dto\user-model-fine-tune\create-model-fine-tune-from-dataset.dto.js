"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateModelFineTuneFromDatasetDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateModelFineTuneFromDatasetDto {
    name;
    baseModelName;
    datasetId;
    capabilities;
    keyLlmId;
}
exports.CreateModelFineTuneFromDatasetDto = CreateModelFineTuneFromDatasetDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên model fine-tune',
        example: 'My Custom GPT Model',
        maxLength: 255,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateModelFineTuneFromDatasetDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tên base model được fine-tune',
        example: 'gpt-3.5-turbo',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateModelFineTuneFromDatasetDto.prototype, "baseModelName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'UUID của dataset để fine-tune',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateModelFineTuneFromDatasetDto.prototype, "datasetId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'UUID của model registry capabilities',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateModelFineTuneFromDatasetDto.prototype, "capabilities", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'UUID của system key LLM để huấn luyện',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateModelFineTuneFromDatasetDto.prototype, "keyLlmId", void 0);
//# sourceMappingURL=create-model-fine-tune-from-dataset.dto.js.map